import posthog from 'posthog-js'

const PH_KEY = process.env.NEXT_PUBLIC_POSTHOG_KEY;
const PH_HOST = process.env.NEXT_PUBLIC_POSTHOG_HOST || 'https://us.i.posthog.com';

// Only initialize PostHog when a real key is provided
if (PH_KEY && !PH_KEY.includes('your_posthog_key')) {
  posthog.init(PH_KEY, {
    api_host: PH_HOST,
  });
} else {
  // No-op in dev without a valid key to avoid noisy network errors
}
