#!/bin/bash

# Docker Build Script for Siift Next.js Application
# This script builds the Docker image with all required environment variables

set -e  # Exit on any error

echo "🚀 Building Siift Docker Image..."

# Load environment variables from .env.local (development) or .env.docker
if [ -f .env.local ]; then
    echo "📋 Loading environment variables from .env.local..."
    export $(cat .env.local | grep -v '^#' | grep -v '^$' | xargs)
elif [ -f .env.docker ]; then
    echo "📋 Loading environment variables from .env.docker..."
    export $(cat .env.docker | grep -v '^#' | grep -v '^$' | xargs)
else
    echo "⚠️  Warning: No environment file found. Using default values."
fi

echo "🔍 Using Clerk Key: ${NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY:0:20}..."

# Build the Docker image with all environment variables as build args
echo "🔨 Building Docker image..."
docker build \
    --build-arg NEXT_PUBLIC_ADMIN_API_URL="${NEXT_PUBLIC_ADMIN_API_URL}" \
    --build-arg NEXT_PUBLIC_API_URL="${NEXT_PUBLIC_API_URL}" \
    --build-arg NEXT_PUBLIC_APP_NAME="${NEXT_PUBLIC_APP_NAME}" \
    --build-arg NEXT_PUBLIC_APP_URL="${NEXT_PUBLIC_APP_URL}" \
    --build-arg NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY="${NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY}" \
    --build-arg NEXT_PUBLIC_CLERK_SIGN_IN_URL="${NEXT_PUBLIC_CLERK_SIGN_IN_URL}" \
    --build-arg NEXT_PUBLIC_CLERK_SIGN_UP_URL="${NEXT_PUBLIC_CLERK_SIGN_UP_URL}" \
    --build-arg NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL="${NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL}" \
    --build-arg NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL="${NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL}" \
    --build-arg NEXT_PUBLIC_CLERK_SIGN_IN_FALLBACK_REDIRECT_URL="${NEXT_PUBLIC_CLERK_SIGN_IN_FALLBACK_REDIRECT_URL}" \
    --build-arg NEXT_PUBLIC_CLERK_SIGN_UP_FALLBACK_REDIRECT_URL="${NEXT_PUBLIC_CLERK_SIGN_UP_FALLBACK_REDIRECT_URL}" \
    --build-arg NEXT_PUBLIC_POSTHOG_KEY="${NEXT_PUBLIC_POSTHOG_KEY}" \
    --build-arg NEXT_PUBLIC_POSTHOG_HOST="${NEXT_PUBLIC_POSTHOG_HOST}" \
    --build-arg JWT_SECRET="${JWT_SECRET}" \
    --build-arg CLERK_SECRET_KEY="${CLERK_SECRET_KEY}" \
    --build-arg CLERK_JWT_ISSUER_DOMAIN="${CLERK_JWT_ISSUER_DOMAIN}" \
    --build-arg CLERK_WEBHOOK_SECRET="${CLERK_WEBHOOK_SECRET}" \
    -t siift-next:latest \
    .

echo "✅ Docker image built successfully!"
echo "🏃 To run the container, use:"
echo "   docker run -p 3000:3000 --env-file .env.docker siift-next:latest"
echo ""
echo "🐳 Or use docker-compose:"
echo "   docker-compose up"
