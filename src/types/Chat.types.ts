export interface ChatMessage {
  id: string;
  user: string;
  avatar: string;
  message: string;
  timestamp: string;
  isCurrentUser: boolean;
  cta?: {
    type: 'refetch_topics';
    label?: string;
  };
}

export interface ChatSession {
  id: string;
  projectId: string;
  // any other session-related data
}

export interface ChatStore {
  messages: ChatMessage[];
  chatSession: ChatSession | null;
  isLoading: boolean;
  isStreaming: boolean;
  // Project-specific loading states
  projectLoadingStates: { [projectId: string]: boolean };
  projectStreamingStates: { [projectId: string]: boolean };
  
  // Actions
  setMessages: (messages: ChatMessage[]) => void;
  addMessage: (message: ChatMessage) => void;
  updateLastMessage: (content: string) => void;
  appendToLastMessage: (contentChunk: string) => void;
  addCtaMessage?: (text: string, cta: { type: 'refetch_topics'; label?: string }) => void;
  setChatSession: (session: ChatSession | null) => void;
  setIsLoading: (isLoading: boolean) => void;
  setIsStreaming: (isStreaming: boolean) => void;
  setProjectLoading: (projectId: string, isLoading: boolean) => void;
  setProjectStreaming: (projectId: string, isStreaming: boolean) => void;
  getProjectLoading: (projectId: string) => boolean;
  getProjectStreaming: (projectId: string) => boolean;
  clearChat: () => void;
}