export interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  role: "user" | "admin";
  isEmailVerified: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
  expiresAt: number; // Unix timestamp
}

export interface SessionData {
  user: User | null;
  tokens: AuthTokens | null;
  isAuthenticated: boolean;
  isInitialized: boolean; // Whether the auth state has been checked
  pendingEmailVerification?: string; // Email waiting for verification
  emailVerificationSent?: boolean; // Whether verification email was sent
}

export interface LoginCredentials {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface SignupCredentials {
  email: string;
  password: string;
  name: string;
}

export interface AuthResponse {
  user: User;
  tokens: AuthTokens;
}

export interface RefreshTokenResponse {
  tokens: AuthTokens;
}

export interface ResetPasswordData {
  email: string;
  code: string;
  newPassword: string;
}

export interface ForgotPasswordData {
  email: string;
}

// Error types
export interface AuthError {
  code: string;
  message: string;
  field?: string;
}

// Session store actions
export interface SessionActions {
  // Authentication actions
  login: (credentials: LoginCredentials) => Promise<void>;
  signup: (credentials: SignupCredentials) => Promise<void>;
  logout: () => void;
  refreshToken: () => Promise<void>;

  // Session management
  initializeSession: () => Promise<void>;
  clearSession: () => void;

  // User actions
  updateUser: (userData: Partial<User>) => void;

  // Token management
  setTokens: (tokens: AuthTokens) => void;
  clearTokens: () => void;

  // Email verification
  sendEmailVerification: (email: string, name: string) => Promise<void>;
  verifyEmail: (email: string, code: string) => Promise<void>;
  resendEmailVerification: (email: string) => Promise<void>;

  // Loading states
  setLoading: (loading: boolean) => void;
  setError: (error: AuthError | null) => void;
}

// Combined session store state
export interface SessionStore extends SessionData {
  isLoading: boolean;
  error: AuthError | null;

  // Actions
  actions: SessionActions;
}
