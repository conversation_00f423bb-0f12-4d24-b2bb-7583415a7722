contentScript.bundle.js:66 [CSC] content script client up.
main-app.js?v=1754872829380:2325 Download the React DevTools for a better development experience: https://react.dev/link/react-devtools
PostHogProvider.tsx:9 [PostHog.js] set_config {config: {…}, oldConfig: {…}, newConfig: {…}}
PostHogProvider.tsx:9 [PostHog.js] Persistence loaded localStorage+cookie {distinct_id: 'user_30cifUbzDXOvTvxvvXrhxgZlKOm', $sesid: Array(3), $epp: true, $initial_person_info: {…}, $initialization_time: '2025-08-11T00:36:52.968Z', …}
PostHogProvider.tsx:9 [PostHog.js] Persistence loaded sessionStorage {$referrer: '$direct', $referring_domain: '$direct'}
PostHogProvider.tsx:9 [PostHog.js] Starting in debug mode {this: ca, config: {…}, thisC: {…}, p: {…}, s: {…}}
PostHogProvider.tsx:15 PostHog loaded
admin-api.ts:22 🔧 Admin API Base URL: http://localhost:3002
admin-api.ts:23 🔧 Environment variable NEXT_PUBLIC_ADMIN_API_URL: http://localhost:3002
PostHogProvider.tsx:9  GET https://us-assets.i.posthog.com/array/phc_your_posthog_key_here/config.js net::ERR_ABORTED 404 (Not Found)
n @ module.js:19
q @ module.js:19
v.__PosthogExtensions__.loadExternalDependency @ module.js:19
yr @ module.js:19
load @ module.js:19
Os @ module.js:19
_init @ module.js:19
init @ module.js:19
eval @ PostHogProvider.tsx:9
(app-pages-browser)/./src/components/analytics/PostHogProvider.tsx @ layout.js:1487
options.factory @ webpack.js?v=1754872829380:712
__webpack_require__ @ webpack.js?v=1754872829380:37
fn @ webpack.js?v=1754872829380:369
Promise.then
eval @ next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fclient%2FClerkProvider.js%22%2C%22ids%22%3A%5B%22*%22%2C%22ClientClerkProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fclient%2Fkeyless-cookie-sync.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FcontrolComponents.js%22%2C%22ids%22%3A%5B%22AuthenticateWithRedirectCallback%22%2C%22ClerkDegraded%22%2C%22ClerkFailed%22%2C%22ClerkLoaded%22%2C%22ClerkLoading%22%2C%22RedirectToCreateOrganization%22%2C%22RedirectToOrganizationProfile%22%2C%22RedirectToSignIn%22%2C%22RedirectToSignUp%22%2C%22RedirectToUserProfile%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2Fhooks.js%22%2C%22ids%22%3A%5B%22useAuth%22%2C%22useClerk%22%2C%22useEmailLink%22%2C%22useOrganization%22%2C%22useOrganizationList%22%2C%22useReverification%22%2C%22useSession%22%2C%22useSessionList%22%2C%22useSignIn%22%2C%22useSignUp%22%2C%22useUser%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FPromisifiedAuthProvider.js%22%2C%22ids%22%3A%5B%22PromisifiedAuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FuiComponents.js%22%2C%22ids%22%3A%5B%22APIKeys%22%2C%22CreateOrganization%22%2C%22GoogleOneTap%22%2C%22OrganizationList%22%2C%22OrganizationProfile%22%2C%22OrganizationSwitcher%22%2C%22PricingTable%22%2C%22SignIn%22%2C%22SignInButton%22%2C%22SignInWithMetamaskButton%22%2C%22SignOutButton%22%2C%22SignUp%22%2C%22SignUpButton%22%2C%22TaskSelectOrganization%22%2C%22UserButton%22%2C%22UserProfile%22%2C%22Waitlist%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2Fnext%2Ffont%2Flocal%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Flib%2Ffonts.ts%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5B%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Thin.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-ExtraLight.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22200%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Light.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22300%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Regular.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22400%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Medium.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22500%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-SemiBold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22600%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Bold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22700%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-ExtraBold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22800%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Black.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22900%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%5D%2C%5C%22variable%5C%22%3A%5C%22--font-outfit%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22outfit%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2Fnext%2Ffont%2Flocal%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Flib%2Ffonts.ts%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-VariableFont_wght.ttf%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-outfit-variable%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22outfitVariable%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fanalytics%2FPostHogProvider.tsx%22%2C%22ids%22%3A%5B%22PHProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fproviders%2FClerkSessionProvider.tsx%22%2C%22ids%22%3A%5B%22ClerkSessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fproviders%2FQueryProvider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fproviders%2FSessionProvider.tsx%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Ftheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fui%2Fsonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcontexts%2Fbackground-context.tsx%22%2C%22ids%22%3A%5B%22BackgroundProvider%22%5D%7D&server=false!:21
(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fclient%2FClerkProvider.js%22%2C%22ids%22%3A%5B%22*%22%2C%22ClientClerkProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fclient%2Fkeyless-cookie-sync.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FcontrolComponents.js%22%2C%22ids%22%3A%5B%22AuthenticateWithRedirectCallback%22%2C%22ClerkDegraded%22%2C%22ClerkFailed%22%2C%22ClerkLoaded%22%2C%22ClerkLoading%22%2C%22RedirectToCreateOrganization%22%2C%22RedirectToOrganizationProfile%22%2C%22RedirectToSignIn%22%2C%22RedirectToSignUp%22%2C%22RedirectToUserProfile%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2Fhooks.js%22%2C%22ids%22%3A%5B%22useAuth%22%2C%22useClerk%22%2C%22useEmailLink%22%2C%22useOrganization%22%2C%22useOrganizationList%22%2C%22useReverification%22%2C%22useSession%22%2C%22useSessionList%22%2C%22useSignIn%22%2C%22useSignUp%22%2C%22useUser%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FPromisifiedAuthProvider.js%22%2C%22ids%22%3A%5B%22PromisifiedAuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FuiComponents.js%22%2C%22ids%22%3A%5B%22APIKeys%22%2C%22CreateOrganization%22%2C%22GoogleOneTap%22%2C%22OrganizationList%22%2C%22OrganizationProfile%22%2C%22OrganizationSwitcher%22%2C%22PricingTable%22%2C%22SignIn%22%2C%22SignInButton%22%2C%22SignInWithMetamaskButton%22%2C%22SignOutButton%22%2C%22SignUp%22%2C%22SignUpButton%22%2C%22TaskSelectOrganization%22%2C%22UserButton%22%2C%22UserProfile%22%2C%22Waitlist%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2Fnext%2Ffont%2Flocal%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Flib%2Ffonts.ts%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5B%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Thin.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-ExtraLight.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22200%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Light.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22300%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Regular.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22400%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Medium.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22500%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-SemiBold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22600%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Bold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22700%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-ExtraBold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22800%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Black.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22900%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%5D%2C%5C%22variable%5C%22%3A%5C%22--font-outfit%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22outfit%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2Fnext%2Ffont%2Flocal%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Flib%2Ffonts.ts%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-VariableFont_wght.ttf%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-outfit-variable%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22outfitVariable%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fanalytics%2FPostHogProvider.tsx%22%2C%22ids%22%3A%5B%22PHProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fproviders%2FClerkSessionProvider.tsx%22%2C%22ids%22%3A%5B%22ClerkSessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fproviders%2FQueryProvider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fproviders%2FSessionProvider.tsx%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Ftheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fui%2Fsonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcontexts%2Fbackground-context.tsx%22%2C%22ids%22%3A%5B%22BackgroundProvider%22%5D%7D&server=false! @ layout.js:1138
options.factory @ webpack.js?v=1754872829380:712
__webpack_require__ @ webpack.js?v=1754872829380:37
__webpack_exec__ @ layout.js:1680
(anonymous) @ layout.js:1681
__webpack_require__.O @ webpack.js?v=1754872829380:84
webpackJsonpCallback @ webpack.js?v=1754872829380:1398
(anonymous) @ main-app.js?v=1754872829380:9
0f76ecbe-31c0-45d0-a574-8d599435b06d:1 Refused to execute script from 'https://us-assets.i.posthog.com/array/phc_your_posthog_key_here/config.js' because its MIME type ('application/json') is not executable, and strict MIME type checking is enabled.
module.js:19 [PostHog.js] [RemoteConfig] No config found after loading remote JS config. Falling back to JSON.
PostHogProvider.tsx:9  POST https://us.i.posthog.com/flags/?v=2&config=true&ip=0&_=1754872834513&ver=1.258.2&compression=base64 401 (Unauthorized)
method @ module.js:19
eval @ module.js:19
Pe @ module.js:19
Ee @ module.js:19
flags @ module.js:19
Os @ module.js:19
_init @ module.js:19
init @ module.js:19
eval @ PostHogProvider.tsx:9
(app-pages-browser)/./src/components/analytics/PostHogProvider.tsx @ layout.js:1487
options.factory @ webpack.js?v=1754872829380:712
__webpack_require__ @ webpack.js?v=1754872829380:37
fn @ webpack.js?v=1754872829380:369
Promise.then
eval @ next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fclient%2FClerkProvider.js%22%2C%22ids%22%3A%5B%22*%22%2C%22ClientClerkProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fclient%2Fkeyless-cookie-sync.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FcontrolComponents.js%22%2C%22ids%22%3A%5B%22AuthenticateWithRedirectCallback%22%2C%22ClerkDegraded%22%2C%22ClerkFailed%22%2C%22ClerkLoaded%22%2C%22ClerkLoading%22%2C%22RedirectToCreateOrganization%22%2C%22RedirectToOrganizationProfile%22%2C%22RedirectToSignIn%22%2C%22RedirectToSignUp%22%2C%22RedirectToUserProfile%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2Fhooks.js%22%2C%22ids%22%3A%5B%22useAuth%22%2C%22useClerk%22%2C%22useEmailLink%22%2C%22useOrganization%22%2C%22useOrganizationList%22%2C%22useReverification%22%2C%22useSession%22%2C%22useSessionList%22%2C%22useSignIn%22%2C%22useSignUp%22%2C%22useUser%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FPromisifiedAuthProvider.js%22%2C%22ids%22%3A%5B%22PromisifiedAuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FuiComponents.js%22%2C%22ids%22%3A%5B%22APIKeys%22%2C%22CreateOrganization%22%2C%22GoogleOneTap%22%2C%22OrganizationList%22%2C%22OrganizationProfile%22%2C%22OrganizationSwitcher%22%2C%22PricingTable%22%2C%22SignIn%22%2C%22SignInButton%22%2C%22SignInWithMetamaskButton%22%2C%22SignOutButton%22%2C%22SignUp%22%2C%22SignUpButton%22%2C%22TaskSelectOrganization%22%2C%22UserButton%22%2C%22UserProfile%22%2C%22Waitlist%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2Fnext%2Ffont%2Flocal%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Flib%2Ffonts.ts%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5B%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Thin.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-ExtraLight.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22200%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Light.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22300%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Regular.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22400%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Medium.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22500%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-SemiBold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22600%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Bold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22700%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-ExtraBold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22800%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Black.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22900%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%5D%2C%5C%22variable%5C%22%3A%5C%22--font-outfit%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22outfit%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2Fnext%2Ffont%2Flocal%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Flib%2Ffonts.ts%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-VariableFont_wght.ttf%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-outfit-variable%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22outfitVariable%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fanalytics%2FPostHogProvider.tsx%22%2C%22ids%22%3A%5B%22PHProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fproviders%2FClerkSessionProvider.tsx%22%2C%22ids%22%3A%5B%22ClerkSessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fproviders%2FQueryProvider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fproviders%2FSessionProvider.tsx%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Ftheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fui%2Fsonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcontexts%2Fbackground-context.tsx%22%2C%22ids%22%3A%5B%22BackgroundProvider%22%5D%7D&server=false!:21
(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fclient%2FClerkProvider.js%22%2C%22ids%22%3A%5B%22*%22%2C%22ClientClerkProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fclient%2Fkeyless-cookie-sync.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FcontrolComponents.js%22%2C%22ids%22%3A%5B%22AuthenticateWithRedirectCallback%22%2C%22ClerkDegraded%22%2C%22ClerkFailed%22%2C%22ClerkLoaded%22%2C%22ClerkLoading%22%2C%22RedirectToCreateOrganization%22%2C%22RedirectToOrganizationProfile%22%2C%22RedirectToSignIn%22%2C%22RedirectToSignUp%22%2C%22RedirectToUserProfile%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2Fhooks.js%22%2C%22ids%22%3A%5B%22useAuth%22%2C%22useClerk%22%2C%22useEmailLink%22%2C%22useOrganization%22%2C%22useOrganizationList%22%2C%22useReverification%22%2C%22useSession%22%2C%22useSessionList%22%2C%22useSignIn%22%2C%22useSignUp%22%2C%22useUser%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FPromisifiedAuthProvider.js%22%2C%22ids%22%3A%5B%22PromisifiedAuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FuiComponents.js%22%2C%22ids%22%3A%5B%22APIKeys%22%2C%22CreateOrganization%22%2C%22GoogleOneTap%22%2C%22OrganizationList%22%2C%22OrganizationProfile%22%2C%22OrganizationSwitcher%22%2C%22PricingTable%22%2C%22SignIn%22%2C%22SignInButton%22%2C%22SignInWithMetamaskButton%22%2C%22SignOutButton%22%2C%22SignUp%22%2C%22SignUpButton%22%2C%22TaskSelectOrganization%22%2C%22UserButton%22%2C%22UserProfile%22%2C%22Waitlist%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2Fnext%2Ffont%2Flocal%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Flib%2Ffonts.ts%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5B%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Thin.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-ExtraLight.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22200%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Light.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22300%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Regular.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22400%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Medium.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22500%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-SemiBold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22600%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Bold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22700%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-ExtraBold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22800%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Black.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22900%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%5D%2C%5C%22variable%5C%22%3A%5C%22--font-outfit%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22outfit%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2Fnext%2Ffont%2Flocal%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Flib%2Ffonts.ts%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-VariableFont_wght.ttf%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-outfit-variable%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22outfitVariable%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fanalytics%2FPostHogProvider.tsx%22%2C%22ids%22%3A%5B%22PHProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fproviders%2FClerkSessionProvider.tsx%22%2C%22ids%22%3A%5B%22ClerkSessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fproviders%2FQueryProvider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fproviders%2FSessionProvider.tsx%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Ftheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fui%2Fsonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcontexts%2Fbackground-context.tsx%22%2C%22ids%22%3A%5B%22BackgroundProvider%22%5D%7D&server=false! @ layout.js:1138
options.factory @ webpack.js?v=1754872829380:712
__webpack_require__ @ webpack.js?v=1754872829380:37
__webpack_exec__ @ layout.js:1680
(anonymous) @ layout.js:1681
__webpack_require__.O @ webpack.js?v=1754872829380:84
webpackJsonpCallback @ webpack.js?v=1754872829380:1398
(anonymous) @ main-app.js?v=1754872829380:9
clerk.browser.js:16 Clerk: Clerk has been loaded with development keys. Development instances have strict usage limits and should not be used when deploying your application to production. Learn more: https://clerk.com/docs/deployments/overview
warnOnce @ clerk.browser.js:16
load @ clerk.browser.js:5
loadClerkJS @ index.mjs:1328
_IsomorphicClerk @ index.mjs:1201
getOrCreateInstance @ index.mjs:1227
useLoadedIsomorphicClerk @ index.mjs:1497
ClerkContextProvider @ index.mjs:1428
react-stack-bottom-frame @ react-dom-client.development.js:22974
renderWithHooks @ react-dom-client.development.js:6667
updateFunctionComponent @ react-dom-client.development.js:8931
beginWork @ react-dom-client.development.js:10556
runWithFiberInDEV @ react-dom-client.development.js:845
performUnitOfWork @ react-dom-client.development.js:15258
workLoopConcurrentByScheduler @ react-dom-client.development.js:15252
renderRootConcurrent @ react-dom-client.development.js:15227
performWorkOnRoot @ react-dom-client.development.js:14525
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16350
performWorkUntilDeadline @ scheduler.development.js:45
<ClerkContextProvider>
exports.createElement @ react.development.js:1038
ClerkProviderBase @ index.mjs:1528
react-stack-bottom-frame @ react-dom-client.development.js:22974
renderWithHooksAgain @ react-dom-client.development.js:6767
renderWithHooks @ react-dom-client.development.js:6679
updateFunctionComponent @ react-dom-client.development.js:8931
beginWork @ react-dom-client.development.js:10556
runWithFiberInDEV @ react-dom-client.development.js:845
performUnitOfWork @ react-dom-client.development.js:15258
workLoopConcurrentByScheduler @ react-dom-client.development.js:15252
renderRootConcurrent @ react-dom-client.development.js:15227
performWorkOnRoot @ react-dom-client.development.js:14525
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16350
performWorkUntilDeadline @ scheduler.development.js:45
<ClerkProviderBase>
exports.createElement @ react.development.js:1038
Hoc @ chunk-SNP6N6BO.mjs:82
react-stack-bottom-frame @ react-dom-client.development.js:22974
renderWithHooksAgain @ react-dom-client.development.js:6767
renderWithHooks @ react-dom-client.development.js:6679
updateFunctionComponent @ react-dom-client.development.js:8931
beginWork @ react-dom-client.development.js:10556
runWithFiberInDEV @ react-dom-client.development.js:845
performUnitOfWork @ react-dom-client.development.js:15258
workLoopConcurrentByScheduler @ react-dom-client.development.js:15252
renderRootConcurrent @ react-dom-client.development.js:15227
performWorkOnRoot @ react-dom-client.development.js:14525
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16350
performWorkUntilDeadline @ scheduler.development.js:45
<ClerkProvider>
exports.createElement @ react.development.js:1038
NextClientClerkProvider @ ClerkProvider.js:123
react-stack-bottom-frame @ react-dom-client.development.js:22974
renderWithHooksAgain @ react-dom-client.development.js:6767
renderWithHooks @ react-dom-client.development.js:6679
updateFunctionComponent @ react-dom-client.development.js:8931
beginWork @ react-dom-client.development.js:10556
runWithFiberInDEV @ react-dom-client.development.js:845
performUnitOfWork @ react-dom-client.development.js:15258
workLoopConcurrentByScheduler @ react-dom-client.development.js:15252
renderRootConcurrent @ react-dom-client.development.js:15227
performWorkOnRoot @ react-dom-client.development.js:14525
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16350
performWorkUntilDeadline @ scheduler.development.js:45
<NextClientClerkProvider>
exports.createElement @ react.development.js:1038
ClientClerkProvider @ ClerkProvider.js:144
react-stack-bottom-frame @ react-dom-client.development.js:22974
renderWithHooksAgain @ react-dom-client.development.js:6767
renderWithHooks @ react-dom-client.development.js:6679
updateFunctionComponent @ react-dom-client.development.js:8931
beginWork @ react-dom-client.development.js:10505
runWithFiberInDEV @ react-dom-client.development.js:845
performUnitOfWork @ react-dom-client.development.js:15258
workLoopConcurrentByScheduler @ react-dom-client.development.js:15252
renderRootConcurrent @ react-dom-client.development.js:15227
performWorkOnRoot @ react-dom-client.development.js:14525
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16350
performWorkUntilDeadline @ scheduler.development.js:45
"use client"
eval @ react-server-dom-webpack-client.browser.development.js:2355
initializeModelChunk @ react-server-dom-webpack-client.browser.development.js:1054
readChunk @ react-server-dom-webpack-client.browser.development.js:949
react-stack-bottom-frame @ react-dom-client.development.js:23078
reconcileChildFibersImpl @ react-dom-client.development.js:5999
eval @ react-dom-client.development.js:6118
reconcileChildren @ react-dom-client.development.js:8655
beginWork @ react-dom-client.development.js:10755
runWithFiberInDEV @ react-dom-client.development.js:845
performUnitOfWork @ react-dom-client.development.js:15258
workLoopConcurrentByScheduler @ react-dom-client.development.js:15252
renderRootConcurrent @ react-dom-client.development.js:15227
performWorkOnRoot @ react-dom-client.development.js:14525
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16350
performWorkUntilDeadline @ scheduler.development.js:45
<ClerkProvider>
RootLayout @ layout.tsx:57
buildFakeTask @ react-server-dom-webpack-client.browser.development.js:2041
initializeFakeTask @ react-server-dom-webpack-client.browser.development.js:2027
resolveDebugInfo @ react-server-dom-webpack-client.browser.development.js:2063
processFullStringRow @ react-server-dom-webpack-client.browser.development.js:2261
processFullBinaryRow @ react-server-dom-webpack-client.browser.development.js:2233
progress @ react-server-dom-webpack-client.browser.development.js:2479
<RootLayout>
buildFakeTask @ react-server-dom-webpack-client.browser.development.js:2040
initializeFakeTask @ react-server-dom-webpack-client.browser.development.js:2027
resolveDebugInfo @ react-server-dom-webpack-client.browser.development.js:2063
processFullStringRow @ react-server-dom-webpack-client.browser.development.js:2261
processFullBinaryRow @ react-server-dom-webpack-client.browser.development.js:2233
progress @ react-server-dom-webpack-client.browser.development.js:2479
"use server"
ResponseInstance @ react-server-dom-webpack-client.browser.development.js:1587
createResponseFromOptions @ react-server-dom-webpack-client.browser.development.js:2396
exports.createFromReadableStream @ react-server-dom-webpack-client.browser.development.js:2717
eval @ app-index.js:132
(app-pages-browser)/./node_modules/next/dist/client/app-index.js @ main-app.js?v=1754872829380:171
options.factory @ webpack.js?v=1754872829380:712
__webpack_require__ @ webpack.js?v=1754872829380:37
fn @ webpack.js?v=1754872829380:369
eval @ app-next-dev.js:11
eval @ app-bootstrap.js:62
loadScriptsInSequence @ app-bootstrap.js:23
appBootstrap @ app-bootstrap.js:56
eval @ app-next-dev.js:10
(app-pages-browser)/./node_modules/next/dist/client/app-next-dev.js @ main-app.js?v=1754872829380:193
options.factory @ webpack.js?v=1754872829380:712
__webpack_require__ @ webpack.js?v=1754872829380:37
__webpack_exec__ @ main-app.js?v=1754872829380:2836
(anonymous) @ main-app.js?v=1754872829380:2837
webpackJsonpCallback @ webpack.js?v=1754872829380:1388
(anonymous) @ main-app.js?v=1754872829380:9
PostHogProvider.tsx:9 [PostHog.js] [RateLimiter] could not rate limit - continuing. Error: "Unexpected token 'T', "The provid"... is not valid JSON" {text: 'The provided API key is invalid or has expired. Please check your API key and try again.'}
t @ module.js:19
warn @ module.js:19
Oo.checkForLimiting @ module.js:19
callback @ module.js:19
eval @ module.js:19
Promise.then
eval @ module.js:19
Promise.then
method @ module.js:19
eval @ module.js:19
Pe @ module.js:19
Ee @ module.js:19
flags @ module.js:19
Os @ module.js:19
_init @ module.js:19
init @ module.js:19
eval @ PostHogProvider.tsx:9
(app-pages-browser)/./src/components/analytics/PostHogProvider.tsx @ layout.js:1487
options.factory @ webpack.js?v=1754872829380:712
__webpack_require__ @ webpack.js?v=1754872829380:37
fn @ webpack.js?v=1754872829380:369
Promise.then
eval @ next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fclient%2FClerkProvider.js%22%2C%22ids%22%3A%5B%22*%22%2C%22ClientClerkProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fclient%2Fkeyless-cookie-sync.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FcontrolComponents.js%22%2C%22ids%22%3A%5B%22AuthenticateWithRedirectCallback%22%2C%22ClerkDegraded%22%2C%22ClerkFailed%22%2C%22ClerkLoaded%22%2C%22ClerkLoading%22%2C%22RedirectToCreateOrganization%22%2C%22RedirectToOrganizationProfile%22%2C%22RedirectToSignIn%22%2C%22RedirectToSignUp%22%2C%22RedirectToUserProfile%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2Fhooks.js%22%2C%22ids%22%3A%5B%22useAuth%22%2C%22useClerk%22%2C%22useEmailLink%22%2C%22useOrganization%22%2C%22useOrganizationList%22%2C%22useReverification%22%2C%22useSession%22%2C%22useSessionList%22%2C%22useSignIn%22%2C%22useSignUp%22%2C%22useUser%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FPromisifiedAuthProvider.js%22%2C%22ids%22%3A%5B%22PromisifiedAuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FuiComponents.js%22%2C%22ids%22%3A%5B%22APIKeys%22%2C%22CreateOrganization%22%2C%22GoogleOneTap%22%2C%22OrganizationList%22%2C%22OrganizationProfile%22%2C%22OrganizationSwitcher%22%2C%22PricingTable%22%2C%22SignIn%22%2C%22SignInButton%22%2C%22SignInWithMetamaskButton%22%2C%22SignOutButton%22%2C%22SignUp%22%2C%22SignUpButton%22%2C%22TaskSelectOrganization%22%2C%22UserButton%22%2C%22UserProfile%22%2C%22Waitlist%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2Fnext%2Ffont%2Flocal%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Flib%2Ffonts.ts%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5B%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Thin.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-ExtraLight.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22200%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Light.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22300%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Regular.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22400%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Medium.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22500%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-SemiBold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22600%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Bold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22700%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-ExtraBold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22800%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Black.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22900%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%5D%2C%5C%22variable%5C%22%3A%5C%22--font-outfit%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22outfit%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2Fnext%2Ffont%2Flocal%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Flib%2Ffonts.ts%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-VariableFont_wght.ttf%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-outfit-variable%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22outfitVariable%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fanalytics%2FPostHogProvider.tsx%22%2C%22ids%22%3A%5B%22PHProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fproviders%2FClerkSessionProvider.tsx%22%2C%22ids%22%3A%5B%22ClerkSessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fproviders%2FQueryProvider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fproviders%2FSessionProvider.tsx%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Ftheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fui%2Fsonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcontexts%2Fbackground-context.tsx%22%2C%22ids%22%3A%5B%22BackgroundProvider%22%5D%7D&server=false!:21
(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fclient%2FClerkProvider.js%22%2C%22ids%22%3A%5B%22*%22%2C%22ClientClerkProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fclient%2Fkeyless-cookie-sync.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FcontrolComponents.js%22%2C%22ids%22%3A%5B%22AuthenticateWithRedirectCallback%22%2C%22ClerkDegraded%22%2C%22ClerkFailed%22%2C%22ClerkLoaded%22%2C%22ClerkLoading%22%2C%22RedirectToCreateOrganization%22%2C%22RedirectToOrganizationProfile%22%2C%22RedirectToSignIn%22%2C%22RedirectToSignUp%22%2C%22RedirectToUserProfile%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2Fhooks.js%22%2C%22ids%22%3A%5B%22useAuth%22%2C%22useClerk%22%2C%22useEmailLink%22%2C%22useOrganization%22%2C%22useOrganizationList%22%2C%22useReverification%22%2C%22useSession%22%2C%22useSessionList%22%2C%22useSignIn%22%2C%22useSignUp%22%2C%22useUser%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FPromisifiedAuthProvider.js%22%2C%22ids%22%3A%5B%22PromisifiedAuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FuiComponents.js%22%2C%22ids%22%3A%5B%22APIKeys%22%2C%22CreateOrganization%22%2C%22GoogleOneTap%22%2C%22OrganizationList%22%2C%22OrganizationProfile%22%2C%22OrganizationSwitcher%22%2C%22PricingTable%22%2C%22SignIn%22%2C%22SignInButton%22%2C%22SignInWithMetamaskButton%22%2C%22SignOutButton%22%2C%22SignUp%22%2C%22SignUpButton%22%2C%22TaskSelectOrganization%22%2C%22UserButton%22%2C%22UserProfile%22%2C%22Waitlist%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2Fnext%2Ffont%2Flocal%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Flib%2Ffonts.ts%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5B%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Thin.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-ExtraLight.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22200%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Light.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22300%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Regular.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22400%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Medium.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22500%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-SemiBold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22600%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Bold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22700%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-ExtraBold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22800%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Black.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22900%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%5D%2C%5C%22variable%5C%22%3A%5C%22--font-outfit%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22outfit%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2Fnext%2Ffont%2Flocal%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Flib%2Ffonts.ts%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-VariableFont_wght.ttf%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-outfit-variable%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22outfitVariable%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fanalytics%2FPostHogProvider.tsx%22%2C%22ids%22%3A%5B%22PHProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fproviders%2FClerkSessionProvider.tsx%22%2C%22ids%22%3A%5B%22ClerkSessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fproviders%2FQueryProvider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fproviders%2FSessionProvider.tsx%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Ftheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fui%2Fsonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcontexts%2Fbackground-context.tsx%22%2C%22ids%22%3A%5B%22BackgroundProvider%22%5D%7D&server=false! @ layout.js:1138
options.factory @ webpack.js?v=1754872829380:712
__webpack_require__ @ webpack.js?v=1754872829380:37
__webpack_exec__ @ layout.js:1680
(anonymous) @ layout.js:1681
__webpack_require__.O @ webpack.js?v=1754872829380:84
webpackJsonpCallback @ webpack.js?v=1754872829380:1398
(anonymous) @ main-app.js?v=1754872829380:9
PostHogProvider.tsx:9 [PostHog.js] Bad HTTP status: 401 The provided API key is invalid or has expired. Please check your API key and try again.
error @ intercept-console-error.js:50
t @ module.js:19
error @ module.js:19
on_request_error @ module.js:19
callback @ module.js:19
eval @ module.js:19
Promise.then
eval @ module.js:19
Promise.then
method @ module.js:19
eval @ module.js:19
Pe @ module.js:19
Ee @ module.js:19
flags @ module.js:19
Os @ module.js:19
_init @ module.js:19
init @ module.js:19
eval @ PostHogProvider.tsx:9
(app-pages-browser)/./src/components/analytics/PostHogProvider.tsx @ layout.js:1487
options.factory @ webpack.js?v=1754872829380:712
__webpack_require__ @ webpack.js?v=1754872829380:37
fn @ webpack.js?v=1754872829380:369
Promise.then
eval @ next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fclient%2FClerkProvider.js%22%2C%22ids%22%3A%5B%22*%22%2C%22ClientClerkProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fclient%2Fkeyless-cookie-sync.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FcontrolComponents.js%22%2C%22ids%22%3A%5B%22AuthenticateWithRedirectCallback%22%2C%22ClerkDegraded%22%2C%22ClerkFailed%22%2C%22ClerkLoaded%22%2C%22ClerkLoading%22%2C%22RedirectToCreateOrganization%22%2C%22RedirectToOrganizationProfile%22%2C%22RedirectToSignIn%22%2C%22RedirectToSignUp%22%2C%22RedirectToUserProfile%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2Fhooks.js%22%2C%22ids%22%3A%5B%22useAuth%22%2C%22useClerk%22%2C%22useEmailLink%22%2C%22useOrganization%22%2C%22useOrganizationList%22%2C%22useReverification%22%2C%22useSession%22%2C%22useSessionList%22%2C%22useSignIn%22%2C%22useSignUp%22%2C%22useUser%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FPromisifiedAuthProvider.js%22%2C%22ids%22%3A%5B%22PromisifiedAuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FuiComponents.js%22%2C%22ids%22%3A%5B%22APIKeys%22%2C%22CreateOrganization%22%2C%22GoogleOneTap%22%2C%22OrganizationList%22%2C%22OrganizationProfile%22%2C%22OrganizationSwitcher%22%2C%22PricingTable%22%2C%22SignIn%22%2C%22SignInButton%22%2C%22SignInWithMetamaskButton%22%2C%22SignOutButton%22%2C%22SignUp%22%2C%22SignUpButton%22%2C%22TaskSelectOrganization%22%2C%22UserButton%22%2C%22UserProfile%22%2C%22Waitlist%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2Fnext%2Ffont%2Flocal%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Flib%2Ffonts.ts%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5B%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Thin.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-ExtraLight.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22200%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Light.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22300%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Regular.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22400%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Medium.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22500%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-SemiBold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22600%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Bold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22700%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-ExtraBold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22800%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Black.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22900%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%5D%2C%5C%22variable%5C%22%3A%5C%22--font-outfit%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22outfit%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2Fnext%2Ffont%2Flocal%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Flib%2Ffonts.ts%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-VariableFont_wght.ttf%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-outfit-variable%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22outfitVariable%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fanalytics%2FPostHogProvider.tsx%22%2C%22ids%22%3A%5B%22PHProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fproviders%2FClerkSessionProvider.tsx%22%2C%22ids%22%3A%5B%22ClerkSessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fproviders%2FQueryProvider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fproviders%2FSessionProvider.tsx%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Ftheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fui%2Fsonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcontexts%2Fbackground-context.tsx%22%2C%22ids%22%3A%5B%22BackgroundProvider%22%5D%7D&server=false!:21
(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fclient%2FClerkProvider.js%22%2C%22ids%22%3A%5B%22*%22%2C%22ClientClerkProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fclient%2Fkeyless-cookie-sync.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FcontrolComponents.js%22%2C%22ids%22%3A%5B%22AuthenticateWithRedirectCallback%22%2C%22ClerkDegraded%22%2C%22ClerkFailed%22%2C%22ClerkLoaded%22%2C%22ClerkLoading%22%2C%22RedirectToCreateOrganization%22%2C%22RedirectToOrganizationProfile%22%2C%22RedirectToSignIn%22%2C%22RedirectToSignUp%22%2C%22RedirectToUserProfile%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2Fhooks.js%22%2C%22ids%22%3A%5B%22useAuth%22%2C%22useClerk%22%2C%22useEmailLink%22%2C%22useOrganization%22%2C%22useOrganizationList%22%2C%22useReverification%22%2C%22useSession%22%2C%22useSessionList%22%2C%22useSignIn%22%2C%22useSignUp%22%2C%22useUser%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FPromisifiedAuthProvider.js%22%2C%22ids%22%3A%5B%22PromisifiedAuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FuiComponents.js%22%2C%22ids%22%3A%5B%22APIKeys%22%2C%22CreateOrganization%22%2C%22GoogleOneTap%22%2C%22OrganizationList%22%2C%22OrganizationProfile%22%2C%22OrganizationSwitcher%22%2C%22PricingTable%22%2C%22SignIn%22%2C%22SignInButton%22%2C%22SignInWithMetamaskButton%22%2C%22SignOutButton%22%2C%22SignUp%22%2C%22SignUpButton%22%2C%22TaskSelectOrganization%22%2C%22UserButton%22%2C%22UserProfile%22%2C%22Waitlist%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2Fnext%2Ffont%2Flocal%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Flib%2Ffonts.ts%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5B%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Thin.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-ExtraLight.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22200%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Light.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22300%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Regular.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22400%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Medium.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22500%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-SemiBold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22600%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Bold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22700%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-ExtraBold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22800%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Black.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22900%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%5D%2C%5C%22variable%5C%22%3A%5C%22--font-outfit%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22outfit%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2Fnext%2Ffont%2Flocal%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Flib%2Ffonts.ts%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-VariableFont_wght.ttf%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-outfit-variable%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22outfitVariable%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fanalytics%2FPostHogProvider.tsx%22%2C%22ids%22%3A%5B%22PHProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fproviders%2FClerkSessionProvider.tsx%22%2C%22ids%22%3A%5B%22ClerkSessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fproviders%2FQueryProvider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fproviders%2FSessionProvider.tsx%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Ftheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fui%2Fsonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcontexts%2Fbackground-context.tsx%22%2C%22ids%22%3A%5B%22BackgroundProvider%22%5D%7D&server=false! @ layout.js:1138
options.factory @ webpack.js?v=1754872829380:712
__webpack_require__ @ webpack.js?v=1754872829380:37
__webpack_exec__ @ layout.js:1680
(anonymous) @ layout.js:1681
__webpack_require__.O @ webpack.js?v=1754872829380:84
webpackJsonpCallback @ webpack.js?v=1754872829380:1398
(anonymous) @ main-app.js?v=1754872829380:9
module.js:19 [PostHog.js] Persistence loaded sessionStorage {$referrer: '$direct', $referring_domain: '$direct'}
module.js:19 [PostHog.js] set_config {config: {…}, oldConfig: {…}, newConfig: {…}}
PostHogProvider.tsx:9 [PostHog.js] [Surveys] Flags not loaded yet. Not loading surveys.
t @ module.js:19
warn @ module.js:19
onRemoteConfig @ module.js:19
Re @ module.js:19
callback @ module.js:19
callback @ module.js:19
eval @ module.js:19
Promise.then
eval @ module.js:19
Promise.then
method @ module.js:19
eval @ module.js:19
Pe @ module.js:19
Ee @ module.js:19
flags @ module.js:19
Os @ module.js:19
_init @ module.js:19
init @ module.js:19
eval @ PostHogProvider.tsx:9
(app-pages-browser)/./src/components/analytics/PostHogProvider.tsx @ layout.js:1487
options.factory @ webpack.js?v=1754872829380:712
__webpack_require__ @ webpack.js?v=1754872829380:37
fn @ webpack.js?v=1754872829380:369
Promise.then
eval @ next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fclient%2FClerkProvider.js%22%2C%22ids%22%3A%5B%22*%22%2C%22ClientClerkProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fclient%2Fkeyless-cookie-sync.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FcontrolComponents.js%22%2C%22ids%22%3A%5B%22AuthenticateWithRedirectCallback%22%2C%22ClerkDegraded%22%2C%22ClerkFailed%22%2C%22ClerkLoaded%22%2C%22ClerkLoading%22%2C%22RedirectToCreateOrganization%22%2C%22RedirectToOrganizationProfile%22%2C%22RedirectToSignIn%22%2C%22RedirectToSignUp%22%2C%22RedirectToUserProfile%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2Fhooks.js%22%2C%22ids%22%3A%5B%22useAuth%22%2C%22useClerk%22%2C%22useEmailLink%22%2C%22useOrganization%22%2C%22useOrganizationList%22%2C%22useReverification%22%2C%22useSession%22%2C%22useSessionList%22%2C%22useSignIn%22%2C%22useSignUp%22%2C%22useUser%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FPromisifiedAuthProvider.js%22%2C%22ids%22%3A%5B%22PromisifiedAuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FuiComponents.js%22%2C%22ids%22%3A%5B%22APIKeys%22%2C%22CreateOrganization%22%2C%22GoogleOneTap%22%2C%22OrganizationList%22%2C%22OrganizationProfile%22%2C%22OrganizationSwitcher%22%2C%22PricingTable%22%2C%22SignIn%22%2C%22SignInButton%22%2C%22SignInWithMetamaskButton%22%2C%22SignOutButton%22%2C%22SignUp%22%2C%22SignUpButton%22%2C%22TaskSelectOrganization%22%2C%22UserButton%22%2C%22UserProfile%22%2C%22Waitlist%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2Fnext%2Ffont%2Flocal%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Flib%2Ffonts.ts%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5B%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Thin.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-ExtraLight.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22200%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Light.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22300%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Regular.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22400%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Medium.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22500%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-SemiBold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22600%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Bold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22700%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-ExtraBold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22800%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Black.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22900%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%5D%2C%5C%22variable%5C%22%3A%5C%22--font-outfit%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22outfit%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2Fnext%2Ffont%2Flocal%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Flib%2Ffonts.ts%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-VariableFont_wght.ttf%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-outfit-variable%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22outfitVariable%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fanalytics%2FPostHogProvider.tsx%22%2C%22ids%22%3A%5B%22PHProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fproviders%2FClerkSessionProvider.tsx%22%2C%22ids%22%3A%5B%22ClerkSessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fproviders%2FQueryProvider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fproviders%2FSessionProvider.tsx%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Ftheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fui%2Fsonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcontexts%2Fbackground-context.tsx%22%2C%22ids%22%3A%5B%22BackgroundProvider%22%5D%7D&server=false!:21
(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fclient%2FClerkProvider.js%22%2C%22ids%22%3A%5B%22*%22%2C%22ClientClerkProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fclient%2Fkeyless-cookie-sync.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FcontrolComponents.js%22%2C%22ids%22%3A%5B%22AuthenticateWithRedirectCallback%22%2C%22ClerkDegraded%22%2C%22ClerkFailed%22%2C%22ClerkLoaded%22%2C%22ClerkLoading%22%2C%22RedirectToCreateOrganization%22%2C%22RedirectToOrganizationProfile%22%2C%22RedirectToSignIn%22%2C%22RedirectToSignUp%22%2C%22RedirectToUserProfile%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2Fhooks.js%22%2C%22ids%22%3A%5B%22useAuth%22%2C%22useClerk%22%2C%22useEmailLink%22%2C%22useOrganization%22%2C%22useOrganizationList%22%2C%22useReverification%22%2C%22useSession%22%2C%22useSessionList%22%2C%22useSignIn%22%2C%22useSignUp%22%2C%22useUser%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FPromisifiedAuthProvider.js%22%2C%22ids%22%3A%5B%22PromisifiedAuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FuiComponents.js%22%2C%22ids%22%3A%5B%22APIKeys%22%2C%22CreateOrganization%22%2C%22GoogleOneTap%22%2C%22OrganizationList%22%2C%22OrganizationProfile%22%2C%22OrganizationSwitcher%22%2C%22PricingTable%22%2C%22SignIn%22%2C%22SignInButton%22%2C%22SignInWithMetamaskButton%22%2C%22SignOutButton%22%2C%22SignUp%22%2C%22SignUpButton%22%2C%22TaskSelectOrganization%22%2C%22UserButton%22%2C%22UserProfile%22%2C%22Waitlist%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2Fnext%2Ffont%2Flocal%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Flib%2Ffonts.ts%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5B%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Thin.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-ExtraLight.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22200%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Light.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22300%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Regular.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22400%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Medium.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22500%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-SemiBold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22600%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Bold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22700%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-ExtraBold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22800%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Black.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22900%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%5D%2C%5C%22variable%5C%22%3A%5C%22--font-outfit%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22outfit%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2Fnext%2Ffont%2Flocal%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Flib%2Ffonts.ts%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-VariableFont_wght.ttf%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-outfit-variable%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22outfitVariable%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fanalytics%2FPostHogProvider.tsx%22%2C%22ids%22%3A%5B%22PHProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fproviders%2FClerkSessionProvider.tsx%22%2C%22ids%22%3A%5B%22ClerkSessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fproviders%2FQueryProvider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fproviders%2FSessionProvider.tsx%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Ftheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fui%2Fsonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcontexts%2Fbackground-context.tsx%22%2C%22ids%22%3A%5B%22BackgroundProvider%22%5D%7D&server=false! @ layout.js:1138
options.factory @ webpack.js?v=1754872829380:712
__webpack_require__ @ webpack.js?v=1754872829380:37
__webpack_exec__ @ layout.js:1680
(anonymous) @ layout.js:1681
__webpack_require__.O @ webpack.js?v=1754872829380:84
webpackJsonpCallback @ webpack.js?v=1754872829380:1398
(anonymous) @ main-app.js?v=1754872829380:9
PostHogProvider.tsx:9 [PostHog.js] [FeatureFlags] Using an older version of the feature flags endpoint. Please upgrade your PostHog server to the latest version
t @ module.js:19
warn @ module.js:19
vo @ module.js:19
eval @ module.js:19
receivedFeatureFlags @ module.js:19
callback @ module.js:19
callback @ module.js:19
eval @ module.js:19
Promise.then
eval @ module.js:19
Promise.then
method @ module.js:19
eval @ module.js:19
Pe @ module.js:19
Ee @ module.js:19
flags @ module.js:19
Os @ module.js:19
_init @ module.js:19
init @ module.js:19
eval @ PostHogProvider.tsx:9
(app-pages-browser)/./src/components/analytics/PostHogProvider.tsx @ layout.js:1487
options.factory @ webpack.js?v=1754872829380:712
__webpack_require__ @ webpack.js?v=1754872829380:37
fn @ webpack.js?v=1754872829380:369
Promise.then
eval @ next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fclient%2FClerkProvider.js%22%2C%22ids%22%3A%5B%22*%22%2C%22ClientClerkProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fclient%2Fkeyless-cookie-sync.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FcontrolComponents.js%22%2C%22ids%22%3A%5B%22AuthenticateWithRedirectCallback%22%2C%22ClerkDegraded%22%2C%22ClerkFailed%22%2C%22ClerkLoaded%22%2C%22ClerkLoading%22%2C%22RedirectToCreateOrganization%22%2C%22RedirectToOrganizationProfile%22%2C%22RedirectToSignIn%22%2C%22RedirectToSignUp%22%2C%22RedirectToUserProfile%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2Fhooks.js%22%2C%22ids%22%3A%5B%22useAuth%22%2C%22useClerk%22%2C%22useEmailLink%22%2C%22useOrganization%22%2C%22useOrganizationList%22%2C%22useReverification%22%2C%22useSession%22%2C%22useSessionList%22%2C%22useSignIn%22%2C%22useSignUp%22%2C%22useUser%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FPromisifiedAuthProvider.js%22%2C%22ids%22%3A%5B%22PromisifiedAuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FuiComponents.js%22%2C%22ids%22%3A%5B%22APIKeys%22%2C%22CreateOrganization%22%2C%22GoogleOneTap%22%2C%22OrganizationList%22%2C%22OrganizationProfile%22%2C%22OrganizationSwitcher%22%2C%22PricingTable%22%2C%22SignIn%22%2C%22SignInButton%22%2C%22SignInWithMetamaskButton%22%2C%22SignOutButton%22%2C%22SignUp%22%2C%22SignUpButton%22%2C%22TaskSelectOrganization%22%2C%22UserButton%22%2C%22UserProfile%22%2C%22Waitlist%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2Fnext%2Ffont%2Flocal%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Flib%2Ffonts.ts%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5B%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Thin.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-ExtraLight.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22200%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Light.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22300%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Regular.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22400%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Medium.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22500%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-SemiBold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22600%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Bold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22700%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-ExtraBold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22800%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Black.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22900%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%5D%2C%5C%22variable%5C%22%3A%5C%22--font-outfit%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22outfit%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2Fnext%2Ffont%2Flocal%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Flib%2Ffonts.ts%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-VariableFont_wght.ttf%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-outfit-variable%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22outfitVariable%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fanalytics%2FPostHogProvider.tsx%22%2C%22ids%22%3A%5B%22PHProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fproviders%2FClerkSessionProvider.tsx%22%2C%22ids%22%3A%5B%22ClerkSessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fproviders%2FQueryProvider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fproviders%2FSessionProvider.tsx%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Ftheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fui%2Fsonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcontexts%2Fbackground-context.tsx%22%2C%22ids%22%3A%5B%22BackgroundProvider%22%5D%7D&server=false!:21
(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fclient%2FClerkProvider.js%22%2C%22ids%22%3A%5B%22*%22%2C%22ClientClerkProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fclient%2Fkeyless-cookie-sync.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FcontrolComponents.js%22%2C%22ids%22%3A%5B%22AuthenticateWithRedirectCallback%22%2C%22ClerkDegraded%22%2C%22ClerkFailed%22%2C%22ClerkLoaded%22%2C%22ClerkLoading%22%2C%22RedirectToCreateOrganization%22%2C%22RedirectToOrganizationProfile%22%2C%22RedirectToSignIn%22%2C%22RedirectToSignUp%22%2C%22RedirectToUserProfile%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2Fhooks.js%22%2C%22ids%22%3A%5B%22useAuth%22%2C%22useClerk%22%2C%22useEmailLink%22%2C%22useOrganization%22%2C%22useOrganizationList%22%2C%22useReverification%22%2C%22useSession%22%2C%22useSessionList%22%2C%22useSignIn%22%2C%22useSignUp%22%2C%22useUser%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FPromisifiedAuthProvider.js%22%2C%22ids%22%3A%5B%22PromisifiedAuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FuiComponents.js%22%2C%22ids%22%3A%5B%22APIKeys%22%2C%22CreateOrganization%22%2C%22GoogleOneTap%22%2C%22OrganizationList%22%2C%22OrganizationProfile%22%2C%22OrganizationSwitcher%22%2C%22PricingTable%22%2C%22SignIn%22%2C%22SignInButton%22%2C%22SignInWithMetamaskButton%22%2C%22SignOutButton%22%2C%22SignUp%22%2C%22SignUpButton%22%2C%22TaskSelectOrganization%22%2C%22UserButton%22%2C%22UserProfile%22%2C%22Waitlist%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2Fnext%2Ffont%2Flocal%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Flib%2Ffonts.ts%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5B%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Thin.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-ExtraLight.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22200%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Light.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22300%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Regular.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22400%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Medium.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22500%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-SemiBold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22600%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Bold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22700%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-ExtraBold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22800%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Black.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22900%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%5D%2C%5C%22variable%5C%22%3A%5C%22--font-outfit%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22outfit%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2Fnext%2Ffont%2Flocal%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Flib%2Ffonts.ts%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-VariableFont_wght.ttf%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-outfit-variable%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22outfitVariable%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fanalytics%2FPostHogProvider.tsx%22%2C%22ids%22%3A%5B%22PHProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fproviders%2FClerkSessionProvider.tsx%22%2C%22ids%22%3A%5B%22ClerkSessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fproviders%2FQueryProvider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fproviders%2FSessionProvider.tsx%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Ftheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fui%2Fsonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcontexts%2Fbackground-context.tsx%22%2C%22ids%22%3A%5B%22BackgroundProvider%22%5D%7D&server=false! @ layout.js:1138
options.factory @ webpack.js?v=1754872829380:712
__webpack_require__ @ webpack.js?v=1754872829380:37
__webpack_exec__ @ layout.js:1680
(anonymous) @ layout.js:1681
__webpack_require__.O @ webpack.js?v=1754872829380:84
webpackJsonpCallback @ webpack.js?v=1754872829380:1398
(anonymous) @ main-app.js?v=1754872829380:9
PostHogProvider.tsx:9  GET https://us-assets.i.posthog.com/array/phc_your_posthog_key_here/config?ip=0&_=1754872834879&ver=1.258.2 404 (Not Found)
method @ module.js:19
eval @ module.js:19
Pe @ module.js:19
wr @ module.js:19
eval @ module.js:19
eval @ module.js:19
r.onerror @ module.js:19
script
n @ module.js:19
q @ module.js:19
v.__PosthogExtensions__.loadExternalDependency @ module.js:19
yr @ module.js:19
load @ module.js:19
Os @ module.js:19
_init @ module.js:19
init @ module.js:19
eval @ PostHogProvider.tsx:9
(app-pages-browser)/./src/components/analytics/PostHogProvider.tsx @ layout.js:1487
options.factory @ webpack.js?v=1754872829380:712
__webpack_require__ @ webpack.js?v=1754872829380:37
fn @ webpack.js?v=1754872829380:369
Promise.then
eval @ next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fclient%2FClerkProvider.js%22%2C%22ids%22%3A%5B%22*%22%2C%22ClientClerkProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fclient%2Fkeyless-cookie-sync.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FcontrolComponents.js%22%2C%22ids%22%3A%5B%22AuthenticateWithRedirectCallback%22%2C%22ClerkDegraded%22%2C%22ClerkFailed%22%2C%22ClerkLoaded%22%2C%22ClerkLoading%22%2C%22RedirectToCreateOrganization%22%2C%22RedirectToOrganizationProfile%22%2C%22RedirectToSignIn%22%2C%22RedirectToSignUp%22%2C%22RedirectToUserProfile%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2Fhooks.js%22%2C%22ids%22%3A%5B%22useAuth%22%2C%22useClerk%22%2C%22useEmailLink%22%2C%22useOrganization%22%2C%22useOrganizationList%22%2C%22useReverification%22%2C%22useSession%22%2C%22useSessionList%22%2C%22useSignIn%22%2C%22useSignUp%22%2C%22useUser%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FPromisifiedAuthProvider.js%22%2C%22ids%22%3A%5B%22PromisifiedAuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FuiComponents.js%22%2C%22ids%22%3A%5B%22APIKeys%22%2C%22CreateOrganization%22%2C%22GoogleOneTap%22%2C%22OrganizationList%22%2C%22OrganizationProfile%22%2C%22OrganizationSwitcher%22%2C%22PricingTable%22%2C%22SignIn%22%2C%22SignInButton%22%2C%22SignInWithMetamaskButton%22%2C%22SignOutButton%22%2C%22SignUp%22%2C%22SignUpButton%22%2C%22TaskSelectOrganization%22%2C%22UserButton%22%2C%22UserProfile%22%2C%22Waitlist%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2Fnext%2Ffont%2Flocal%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Flib%2Ffonts.ts%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5B%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Thin.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-ExtraLight.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22200%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Light.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22300%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Regular.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22400%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Medium.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22500%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-SemiBold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22600%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Bold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22700%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-ExtraBold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22800%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Black.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22900%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%5D%2C%5C%22variable%5C%22%3A%5C%22--font-outfit%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22outfit%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2Fnext%2Ffont%2Flocal%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Flib%2Ffonts.ts%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-VariableFont_wght.ttf%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-outfit-variable%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22outfitVariable%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fanalytics%2FPostHogProvider.tsx%22%2C%22ids%22%3A%5B%22PHProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fproviders%2FClerkSessionProvider.tsx%22%2C%22ids%22%3A%5B%22ClerkSessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fproviders%2FQueryProvider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fproviders%2FSessionProvider.tsx%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Ftheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fui%2Fsonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcontexts%2Fbackground-context.tsx%22%2C%22ids%22%3A%5B%22BackgroundProvider%22%5D%7D&server=false!:21
(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fclient%2FClerkProvider.js%22%2C%22ids%22%3A%5B%22*%22%2C%22ClientClerkProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fclient%2Fkeyless-cookie-sync.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FcontrolComponents.js%22%2C%22ids%22%3A%5B%22AuthenticateWithRedirectCallback%22%2C%22ClerkDegraded%22%2C%22ClerkFailed%22%2C%22ClerkLoaded%22%2C%22ClerkLoading%22%2C%22RedirectToCreateOrganization%22%2C%22RedirectToOrganizationProfile%22%2C%22RedirectToSignIn%22%2C%22RedirectToSignUp%22%2C%22RedirectToUserProfile%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2Fhooks.js%22%2C%22ids%22%3A%5B%22useAuth%22%2C%22useClerk%22%2C%22useEmailLink%22%2C%22useOrganization%22%2C%22useOrganizationList%22%2C%22useReverification%22%2C%22useSession%22%2C%22useSessionList%22%2C%22useSignIn%22%2C%22useSignUp%22%2C%22useUser%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FPromisifiedAuthProvider.js%22%2C%22ids%22%3A%5B%22PromisifiedAuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FuiComponents.js%22%2C%22ids%22%3A%5B%22APIKeys%22%2C%22CreateOrganization%22%2C%22GoogleOneTap%22%2C%22OrganizationList%22%2C%22OrganizationProfile%22%2C%22OrganizationSwitcher%22%2C%22PricingTable%22%2C%22SignIn%22%2C%22SignInButton%22%2C%22SignInWithMetamaskButton%22%2C%22SignOutButton%22%2C%22SignUp%22%2C%22SignUpButton%22%2C%22TaskSelectOrganization%22%2C%22UserButton%22%2C%22UserProfile%22%2C%22Waitlist%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2Fnext%2Ffont%2Flocal%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Flib%2Ffonts.ts%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5B%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Thin.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-ExtraLight.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22200%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Light.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22300%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Regular.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22400%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Medium.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22500%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-SemiBold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22600%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Bold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22700%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-ExtraBold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22800%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Black.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22900%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%5D%2C%5C%22variable%5C%22%3A%5C%22--font-outfit%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22outfit%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2Fnext%2Ffont%2Flocal%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Flib%2Ffonts.ts%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-VariableFont_wght.ttf%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-outfit-variable%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22outfitVariable%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fanalytics%2FPostHogProvider.tsx%22%2C%22ids%22%3A%5B%22PHProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fproviders%2FClerkSessionProvider.tsx%22%2C%22ids%22%3A%5B%22ClerkSessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fproviders%2FQueryProvider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fproviders%2FSessionProvider.tsx%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Ftheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fui%2Fsonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcontexts%2Fbackground-context.tsx%22%2C%22ids%22%3A%5B%22BackgroundProvider%22%5D%7D&server=false! @ layout.js:1138
options.factory @ webpack.js?v=1754872829380:712
__webpack_require__ @ webpack.js?v=1754872829380:37
__webpack_exec__ @ layout.js:1680
(anonymous) @ layout.js:1681
__webpack_require__.O @ webpack.js?v=1754872829380:84
webpackJsonpCallback @ webpack.js?v=1754872829380:1398
(anonymous) @ main-app.js?v=1754872829380:9
page.tsx:65 🔥 [ProjectDetailPage] Store sections: {storeSectionsCount: 0, storeSections: Array(0)}
page.tsx:65 🔥 [ProjectDetailPage] Store sections: {storeSectionsCount: 0, storeSections: Array(0)}
main-app.js?v=1754872829380:171 ReferenceError: Cannot access 'allTopics' before initialization
    at ProjectDetailPage (page.tsx:124:84)
    at react-stack-bottom-frame (react-dom-client.development.js:22974:20)
    at renderWithHooks (react-dom-client.development.js:6667:22)
    at updateFunctionComponent (react-dom-client.development.js:8931:19)
    at beginWork (react-dom-client.development.js:10556:18)
    at runWithFiberInDEV (react-dom-client.development.js:845:30)
    at performUnitOfWork (react-dom-client.development.js:15258:22)
    at workLoopSync (react-dom-client.development.js:15078:41)
    at renderRootSync (react-dom-client.development.js:15058:11)
    at performWorkOnRoot (react-dom-client.development.js:14569:44)
    at performSyncWorkOnRoot (react-dom-client.development.js:16365:7)
    at flushSyncWorkAcrossRoots_impl (react-dom-client.development.js:16211:21)
    at flushPassiveEffects (react-dom-client.development.js:15881:9)
    at eval (react-dom-client.development.js:15505:15)
    at MessagePort.performWorkUntilDeadline (scheduler.development.js:45:48)

The above error occurred in the <ProjectDetailPage> component. It was handled by the <ErrorBoundaryHandler> error boundary.
onCaughtError @ error-boundary-callbacks.js:67
logCaughtError @ react-dom-client.development.js:8401
runWithFiberInDEV @ react-dom-client.development.js:845
update.callback @ react-dom-client.development.js:8434
callCallback @ react-dom-client.development.js:6429
commitCallbacks @ react-dom-client.development.js:6449
runWithFiberInDEV @ react-dom-client.development.js:845
commitClassCallbacks @ react-dom-client.development.js:12140
commitLayoutEffectOnFiber @ react-dom-client.development.js:12764
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13673
commitLayoutEffectOnFiber @ react-dom-client.development.js:12687
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13673
commitLayoutEffectOnFiber @ react-dom-client.development.js:12692
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13673
commitLayoutEffectOnFiber @ react-dom-client.development.js:12687
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13673
commitLayoutEffectOnFiber @ react-dom-client.development.js:12687
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13673
commitLayoutEffectOnFiber @ react-dom-client.development.js:12867
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13673
commitLayoutEffectOnFiber @ react-dom-client.development.js:12687
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13673
commitLayoutEffectOnFiber @ react-dom-client.development.js:12687
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13673
commitLayoutEffectOnFiber @ react-dom-client.development.js:12687
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13673
commitLayoutEffectOnFiber @ react-dom-client.development.js:12867
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13673
commitLayoutEffectOnFiber @ react-dom-client.development.js:12687
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13673
commitLayoutEffectOnFiber @ react-dom-client.development.js:12867
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13673
commitLayoutEffectOnFiber @ react-dom-client.development.js:12687
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13673
commitLayoutEffectOnFiber @ react-dom-client.development.js:12687
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13673
commitLayoutEffectOnFiber @ react-dom-client.development.js:12687
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13673
commitLayoutEffectOnFiber @ react-dom-client.development.js:12867
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13673
commitLayoutEffectOnFiber @ react-dom-client.development.js:12687
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13673
commitLayoutEffectOnFiber @ react-dom-client.development.js:12687
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13673
commitLayoutEffectOnFiber @ react-dom-client.development.js:12867
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13673
commitLayoutEffectOnFiber @ react-dom-client.development.js:12687
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13673
commitLayoutEffectOnFiber @ react-dom-client.development.js:12687
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13673
commitLayoutEffectOnFiber @ react-dom-client.development.js:12867
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13673
commitLayoutEffectOnFiber @ react-dom-client.development.js:12687
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13673
commitLayoutEffectOnFiber @ react-dom-client.development.js:12867
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13673
commitLayoutEffectOnFiber @ react-dom-client.development.js:12867
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13673
commitLayoutEffectOnFiber @ react-dom-client.development.js:12867
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13673
commitLayoutEffectOnFiber @ react-dom-client.development.js:12867
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13673
commitLayoutEffectOnFiber @ react-dom-client.development.js:12687
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13673
commitLayoutEffectOnFiber @ react-dom-client.development.js:12687
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13673
commitLayoutEffectOnFiber @ react-dom-client.development.js:12867
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13673
commitLayoutEffectOnFiber @ react-dom-client.development.js:12867
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13673
commitLayoutEffectOnFiber @ react-dom-client.development.js:12867
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13673
commitLayoutEffectOnFiber @ react-dom-client.development.js:12687
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13673
commitLayoutEffectOnFiber @ react-dom-client.development.js:12687
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13673
commitLayoutEffectOnFiber @ react-dom-client.development.js:12687
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13673
commitLayoutEffectOnFiber @ react-dom-client.development.js:12867
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13673
commitLayoutEffectOnFiber @ react-dom-client.development.js:12687
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13673
commitLayoutEffectOnFiber @ react-dom-client.development.js:12687
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13673
commitLayoutEffectOnFiber @ react-dom-client.development.js:12687
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13673
commitLayoutEffectOnFiber @ react-dom-client.development.js:12803
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13673
commitLayoutEffectOnFiber @ react-dom-client.development.js:12803
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13673
commitLayoutEffectOnFiber @ react-dom-client.development.js:12867
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13673
commitLayoutEffectOnFiber @ react-dom-client.development.js:12692
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13673
commitLayoutEffectOnFiber @ react-dom-client.development.js:12687
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13673
commitLayoutEffectOnFiber @ react-dom-client.development.js:12692
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13673
commitLayoutEffectOnFiber @ react-dom-client.development.js:12687
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13673
commitLayoutEffectOnFiber @ react-dom-client.development.js:12687
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13673
commitLayoutEffectOnFiber @ react-dom-client.development.js:12692
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13673
commitLayoutEffectOnFiber @ react-dom-client.development.js:12687
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13673
commitLayoutEffectOnFiber @ react-dom-client.development.js:12687
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13673
commitLayoutEffectOnFiber @ react-dom-client.development.js:12867
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13673
commitLayoutEffectOnFiber @ react-dom-client.development.js:12867
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13673
commitLayoutEffectOnFiber @ react-dom-client.development.js:12867
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13673
commitLayoutEffectOnFiber @ react-dom-client.development.js:12867
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13673
commitLayoutEffectOnFiber @ react-dom-client.development.js:12867
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13673
commitLayoutEffectOnFiber @ react-dom-client.development.js:12867
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13673
commitLayoutEffectOnFiber @ react-dom-client.development.js:12687
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13673
commitLayoutEffectOnFiber @ react-dom-client.development.js:12692
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13673
commitLayoutEffectOnFiber @ react-dom-client.development.js:12687
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13673
commitLayoutEffectOnFiber @ react-dom-client.development.js:12687
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13673
commitLayoutEffectOnFiber @ react-dom-client.development.js:12867
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13673
commitLayoutEffectOnFiber @ react-dom-client.development.js:12687
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13673
commitLayoutEffectOnFiber @ react-dom-client.development.js:12687
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13673
commitLayoutEffectOnFiber @ react-dom-client.development.js:12867
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13673
commitLayoutEffectOnFiber @ react-dom-client.development.js:12867
recursivelyTraverseLayoutEffects @ react-dom-client.development.js:13673
commitLayoutEffectOnFiber @ react-dom-client.development.js:12769
flushLayoutEffects @ react-dom-client.development.js:15687
commitRoot @ react-dom-client.development.js:15528
commitRootWhenReady @ react-dom-client.development.js:14759
performWorkOnRoot @ react-dom-client.development.js:14682
performSyncWorkOnRoot @ react-dom-client.development.js:16365
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16211
flushPassiveEffects @ react-dom-client.development.js:15881
eval @ react-dom-client.development.js:15505
performWorkUntilDeadline @ scheduler.development.js:45
<ProjectDetailPage>
exports.jsx @ react-jsx-runtime.development.js:339
ClientPageRoot @ client-page.js:20
react-stack-bottom-frame @ react-dom-client.development.js:22974
renderWithHooksAgain @ react-dom-client.development.js:6767
renderWithHooks @ react-dom-client.development.js:6679
updateFunctionComponent @ react-dom-client.development.js:8931
beginWork @ react-dom-client.development.js:10505
runWithFiberInDEV @ react-dom-client.development.js:845
performUnitOfWork @ react-dom-client.development.js:15258
workLoopSync @ react-dom-client.development.js:15078
renderRootSync @ react-dom-client.development.js:15058
performWorkOnRoot @ react-dom-client.development.js:14569
performSyncWorkOnRoot @ react-dom-client.development.js:16365
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16211
flushPassiveEffects @ react-dom-client.development.js:15881
eval @ react-dom-client.development.js:15505
performWorkUntilDeadline @ scheduler.development.js:45
"use client"
eval @ react-server-dom-webpack-client.browser.development.js:2354
initializeModelChunk @ react-server-dom-webpack-client.browser.development.js:1054
resolveModelChunk @ react-server-dom-webpack-client.browser.development.js:1031
resolveModel @ react-server-dom-webpack-client.browser.development.js:1599
processFullStringRow @ react-server-dom-webpack-client.browser.development.js:2288
processFullBinaryRow @ react-server-dom-webpack-client.browser.development.js:2233
progress @ react-server-dom-webpack-client.browser.development.js:2479
"use server"
ResponseInstance @ react-server-dom-webpack-client.browser.development.js:1587
createResponseFromOptions @ react-server-dom-webpack-client.browser.development.js:2396
exports.createFromReadableStream @ react-server-dom-webpack-client.browser.development.js:2717
eval @ app-index.js:132
(app-pages-browser)/./node_modules/next/dist/client/app-index.js @ main-app.js?v=1754872829380:171
options.factory @ webpack.js?v=1754872829380:712
__webpack_require__ @ webpack.js?v=1754872829380:37
fn @ webpack.js?v=1754872829380:369
eval @ app-next-dev.js:11
eval @ app-bootstrap.js:62
loadScriptsInSequence @ app-bootstrap.js:23
appBootstrap @ app-bootstrap.js:56
eval @ app-next-dev.js:10
(app-pages-browser)/./node_modules/next/dist/client/app-next-dev.js @ main-app.js?v=1754872829380:193
options.factory @ webpack.js?v=1754872829380:712
__webpack_require__ @ webpack.js?v=1754872829380:37
__webpack_exec__ @ main-app.js?v=1754872829380:2836
(anonymous) @ main-app.js?v=1754872829380:2837
webpackJsonpCallback @ webpack.js?v=1754872829380:1388
(anonymous) @ main-app.js?v=1754872829380:9
error.tsx:17 Application error: ReferenceError: Cannot access 'allTopics' before initialization
    at ProjectDetailPage (page.tsx:124:84)
    at react-stack-bottom-frame (react-dom-client.development.js:22974:20)
    at renderWithHooks (react-dom-client.development.js:6667:22)
    at updateFunctionComponent (react-dom-client.development.js:8931:19)
    at beginWork (react-dom-client.development.js:10556:18)
    at runWithFiberInDEV (react-dom-client.development.js:845:30)
    at performUnitOfWork (react-dom-client.development.js:15258:22)
    at workLoopSync (react-dom-client.development.js:15078:41)
    at renderRootSync (react-dom-client.development.js:15058:11)
    at performWorkOnRoot (react-dom-client.development.js:14569:44)
    at performSyncWorkOnRoot (react-dom-client.development.js:16365:7)
    at flushSyncWorkAcrossRoots_impl (react-dom-client.development.js:16211:21)
    at flushPassiveEffects (react-dom-client.development.js:15881:9)
    at eval (react-dom-client.development.js:15505:15)
    at MessagePort.performWorkUntilDeadline (scheduler.development.js:45:48)
error @ intercept-console-error.js:50
Error.useEffect @ error.tsx:17
react-stack-bottom-frame @ react-dom-client.development.js:23055
runWithFiberInDEV @ react-dom-client.development.js:845
commitHookEffectListMount @ react-dom-client.development.js:11978
commitHookPassiveMountEffects @ react-dom-client.development.js:12099
commitPassiveMountOnFiber @ react-dom-client.development.js:13929
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13932
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13932
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13932
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13932
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13932
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13932
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13941
flushPassiveEffects @ react-dom-client.development.js:15869
flushPendingEffects @ react-dom-client.development.js:15830
flushSpawnedWork @ react-dom-client.development.js:15796
commitRoot @ react-dom-client.development.js:15529
commitRootWhenReady @ react-dom-client.development.js:14759
performWorkOnRoot @ react-dom-client.development.js:14682
performSyncWorkOnRoot @ react-dom-client.development.js:16365
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16211
flushPassiveEffects @ react-dom-client.development.js:15881
eval @ react-dom-client.development.js:15505
performWorkUntilDeadline @ scheduler.development.js:45
<Error>
exports.jsx @ react-jsx-runtime.development.js:339
render @ error-boundary.js:112
react-stack-bottom-frame @ react-dom-client.development.js:22987
updateClassComponent @ react-dom-client.development.js:9488
beginWork @ react-dom-client.development.js:10570
runWithFiberInDEV @ react-dom-client.development.js:845
performUnitOfWork @ react-dom-client.development.js:15258
workLoopSync @ react-dom-client.development.js:15078
renderRootSync @ react-dom-client.development.js:15058
performWorkOnRoot @ react-dom-client.development.js:14569
performSyncWorkOnRoot @ react-dom-client.development.js:16365
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16211
flushPassiveEffects @ react-dom-client.development.js:15881
eval @ react-dom-client.development.js:15505
performWorkUntilDeadline @ scheduler.development.js:45
<ErrorBoundaryHandler>
exports.jsx @ react-jsx-runtime.development.js:339
ErrorBoundary @ error-boundary.js:184
react-stack-bottom-frame @ react-dom-client.development.js:22974
renderWithHooksAgain @ react-dom-client.development.js:6767
renderWithHooks @ react-dom-client.development.js:6679
updateFunctionComponent @ react-dom-client.development.js:8931
beginWork @ react-dom-client.development.js:10556
runWithFiberInDEV @ react-dom-client.development.js:845
performUnitOfWork @ react-dom-client.development.js:15258
workLoopSync @ react-dom-client.development.js:15078
renderRootSync @ react-dom-client.development.js:15058
performWorkOnRoot @ react-dom-client.development.js:14569
performSyncWorkOnRoot @ react-dom-client.development.js:16365
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16211
flushPassiveEffects @ react-dom-client.development.js:15881
eval @ react-dom-client.development.js:15505
performWorkUntilDeadline @ scheduler.development.js:45
<ErrorBoundary>
exports.jsx @ react-jsx-runtime.development.js:339
OuterLayoutRouter @ layout-router.js:447
react-stack-bottom-frame @ react-dom-client.development.js:22974
renderWithHooksAgain @ react-dom-client.development.js:6767
renderWithHooks @ react-dom-client.development.js:6679
updateFunctionComponent @ react-dom-client.development.js:8931
beginWork @ react-dom-client.development.js:10505
runWithFiberInDEV @ react-dom-client.development.js:845
performUnitOfWork @ react-dom-client.development.js:15258
workLoopSync @ react-dom-client.development.js:15078
renderRootSync @ react-dom-client.development.js:15058
performWorkOnRoot @ react-dom-client.development.js:14569
performSyncWorkOnRoot @ react-dom-client.development.js:16365
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16211
flushPassiveEffects @ react-dom-client.development.js:15881
eval @ react-dom-client.development.js:15505
performWorkUntilDeadline @ scheduler.development.js:45
"use client"
eval @ react-server-dom-webpack-client.browser.development.js:2354
initializeModelChunk @ react-server-dom-webpack-client.browser.development.js:1054
readChunk @ react-server-dom-webpack-client.browser.development.js:949
react-stack-bottom-frame @ react-dom-client.development.js:23078
reconcileChildFibersImpl @ react-dom-client.development.js:5999
eval @ react-dom-client.development.js:6118
reconcileChildren @ react-dom-client.development.js:8655
beginWork @ react-dom-client.development.js:10755
runWithFiberInDEV @ react-dom-client.development.js:845
performUnitOfWork @ react-dom-client.development.js:15258
workLoopConcurrentByScheduler @ react-dom-client.development.js:15252
renderRootConcurrent @ react-dom-client.development.js:15227
performWorkOnRoot @ react-dom-client.development.js:14525
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16350
performWorkUntilDeadline @ scheduler.development.js:45
"use server"
ResponseInstance @ react-server-dom-webpack-client.browser.development.js:1587
createResponseFromOptions @ react-server-dom-webpack-client.browser.development.js:2396
exports.createFromReadableStream @ react-server-dom-webpack-client.browser.development.js:2717
eval @ app-index.js:132
(app-pages-browser)/./node_modules/next/dist/client/app-index.js @ main-app.js?v=1754872829380:171
options.factory @ webpack.js?v=1754872829380:712
__webpack_require__ @ webpack.js?v=1754872829380:37
fn @ webpack.js?v=1754872829380:369
eval @ app-next-dev.js:11
eval @ app-bootstrap.js:62
loadScriptsInSequence @ app-bootstrap.js:23
appBootstrap @ app-bootstrap.js:56
eval @ app-next-dev.js:10
(app-pages-browser)/./node_modules/next/dist/client/app-next-dev.js @ main-app.js?v=1754872829380:193
options.factory @ webpack.js?v=1754872829380:712
__webpack_require__ @ webpack.js?v=1754872829380:37
__webpack_exec__ @ main-app.js?v=1754872829380:2836
(anonymous) @ main-app.js?v=1754872829380:2837
webpackJsonpCallback @ webpack.js?v=1754872829380:1388
(anonymous) @ main-app.js?v=1754872829380:9
error.tsx:17 Application error: ReferenceError: Cannot access 'allTopics' before initialization
    at ProjectDetailPage (page.tsx:124:84)
    at react-stack-bottom-frame (react-dom-client.development.js:22974:20)
    at renderWithHooks (react-dom-client.development.js:6667:22)
    at updateFunctionComponent (react-dom-client.development.js:8931:19)
    at beginWork (react-dom-client.development.js:10556:18)
    at runWithFiberInDEV (react-dom-client.development.js:845:30)
    at performUnitOfWork (react-dom-client.development.js:15258:22)
    at workLoopSync (react-dom-client.development.js:15078:41)
    at renderRootSync (react-dom-client.development.js:15058:11)
    at performWorkOnRoot (react-dom-client.development.js:14569:44)
    at performSyncWorkOnRoot (react-dom-client.development.js:16365:7)
    at flushSyncWorkAcrossRoots_impl (react-dom-client.development.js:16211:21)
    at flushPassiveEffects (react-dom-client.development.js:15881:9)
    at eval (react-dom-client.development.js:15505:15)
    at MessagePort.performWorkUntilDeadline (scheduler.development.js:45:48)
error @ intercept-console-error.js:50
Error.useEffect @ error.tsx:17
react-stack-bottom-frame @ react-dom-client.development.js:23055
runWithFiberInDEV @ react-dom-client.development.js:845
commitHookEffectListMount @ react-dom-client.development.js:11978
commitHookPassiveMountEffects @ react-dom-client.development.js:12099
reconnectPassiveEffects @ react-dom-client.development.js:14097
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14068
reconnectPassiveEffects @ react-dom-client.development.js:14144
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14068
reconnectPassiveEffects @ react-dom-client.development.js:14090
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14068
reconnectPassiveEffects @ react-dom-client.development.js:14144
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14068
reconnectPassiveEffects @ react-dom-client.development.js:14090
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14068
reconnectPassiveEffects @ react-dom-client.development.js:14090
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14068
reconnectPassiveEffects @ react-dom-client.development.js:14144
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14068
reconnectPassiveEffects @ react-dom-client.development.js:14090
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14068
reconnectPassiveEffects @ react-dom-client.development.js:14090
doubleInvokeEffectsOnFiber @ react-dom-client.development.js:16100
runWithFiberInDEV @ react-dom-client.development.js:845
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16060
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16067
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16067
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16067
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16067
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16067
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16067
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16067
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16067
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16067
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16067
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16067
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16067
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16067
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16067
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16067
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16067
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16067
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16067
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16067
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16067
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16067
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16067
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16067
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16067
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16067
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16067
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16067
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16067
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16067
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16067
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16067
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16067
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16067
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16067
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16067
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16067
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16067
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16067
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16067
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16067
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16067
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16067
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16067
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16067
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16067
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16067
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16067
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16067
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16067
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16067
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16067
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16067
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16067
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16067
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16067
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16067
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16067
commitDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16109
flushPassiveEffects @ react-dom-client.development.js:15879
flushPendingEffects @ react-dom-client.development.js:15830
flushSpawnedWork @ react-dom-client.development.js:15796
commitRoot @ react-dom-client.development.js:15529
commitRootWhenReady @ react-dom-client.development.js:14759
performWorkOnRoot @ react-dom-client.development.js:14682
performSyncWorkOnRoot @ react-dom-client.development.js:16365
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16211
flushPassiveEffects @ react-dom-client.development.js:15881
eval @ react-dom-client.development.js:15505
performWorkUntilDeadline @ scheduler.development.js:45
<Error>
exports.jsx @ react-jsx-runtime.development.js:339
render @ error-boundary.js:112
react-stack-bottom-frame @ react-dom-client.development.js:22987
updateClassComponent @ react-dom-client.development.js:9488
beginWork @ react-dom-client.development.js:10570
runWithFiberInDEV @ react-dom-client.development.js:845
performUnitOfWork @ react-dom-client.development.js:15258
workLoopSync @ react-dom-client.development.js:15078
renderRootSync @ react-dom-client.development.js:15058
performWorkOnRoot @ react-dom-client.development.js:14569
performSyncWorkOnRoot @ react-dom-client.development.js:16365
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16211
flushPassiveEffects @ react-dom-client.development.js:15881
eval @ react-dom-client.development.js:15505
performWorkUntilDeadline @ scheduler.development.js:45
<ErrorBoundaryHandler>
exports.jsx @ react-jsx-runtime.development.js:339
ErrorBoundary @ error-boundary.js:184
react-stack-bottom-frame @ react-dom-client.development.js:22974
renderWithHooksAgain @ react-dom-client.development.js:6767
renderWithHooks @ react-dom-client.development.js:6679
updateFunctionComponent @ react-dom-client.development.js:8931
beginWork @ react-dom-client.development.js:10556
runWithFiberInDEV @ react-dom-client.development.js:845
performUnitOfWork @ react-dom-client.development.js:15258
workLoopSync @ react-dom-client.development.js:15078
renderRootSync @ react-dom-client.development.js:15058
performWorkOnRoot @ react-dom-client.development.js:14569
performSyncWorkOnRoot @ react-dom-client.development.js:16365
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16211
flushPassiveEffects @ react-dom-client.development.js:15881
eval @ react-dom-client.development.js:15505
performWorkUntilDeadline @ scheduler.development.js:45
<ErrorBoundary>
exports.jsx @ react-jsx-runtime.development.js:339
OuterLayoutRouter @ layout-router.js:447
react-stack-bottom-frame @ react-dom-client.development.js:22974
renderWithHooksAgain @ react-dom-client.development.js:6767
renderWithHooks @ react-dom-client.development.js:6679
updateFunctionComponent @ react-dom-client.development.js:8931
beginWork @ react-dom-client.development.js:10505
runWithFiberInDEV @ react-dom-client.development.js:845
performUnitOfWork @ react-dom-client.development.js:15258
workLoopSync @ react-dom-client.development.js:15078
renderRootSync @ react-dom-client.development.js:15058
performWorkOnRoot @ react-dom-client.development.js:14569
performSyncWorkOnRoot @ react-dom-client.development.js:16365
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16211
flushPassiveEffects @ react-dom-client.development.js:15881
eval @ react-dom-client.development.js:15505
performWorkUntilDeadline @ scheduler.development.js:45
"use client"
eval @ react-server-dom-webpack-client.browser.development.js:2354
initializeModelChunk @ react-server-dom-webpack-client.browser.development.js:1054
readChunk @ react-server-dom-webpack-client.browser.development.js:949
react-stack-bottom-frame @ react-dom-client.development.js:23078
reconcileChildFibersImpl @ react-dom-client.development.js:5999
eval @ react-dom-client.development.js:6118
reconcileChildren @ react-dom-client.development.js:8655
beginWork @ react-dom-client.development.js:10755
runWithFiberInDEV @ react-dom-client.development.js:845
performUnitOfWork @ react-dom-client.development.js:15258
workLoopConcurrentByScheduler @ react-dom-client.development.js:15252
renderRootConcurrent @ react-dom-client.development.js:15227
performWorkOnRoot @ react-dom-client.development.js:14525
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16350
performWorkUntilDeadline @ scheduler.development.js:45
"use server"
ResponseInstance @ react-server-dom-webpack-client.browser.development.js:1587
createResponseFromOptions @ react-server-dom-webpack-client.browser.development.js:2396
exports.createFromReadableStream @ react-server-dom-webpack-client.browser.development.js:2717
eval @ app-index.js:132
(app-pages-browser)/./node_modules/next/dist/client/app-index.js @ main-app.js?v=1754872829380:171
options.factory @ webpack.js?v=1754872829380:712
__webpack_require__ @ webpack.js?v=1754872829380:37
fn @ webpack.js?v=1754872829380:369
eval @ app-next-dev.js:11
eval @ app-bootstrap.js:62
loadScriptsInSequence @ app-bootstrap.js:23
appBootstrap @ app-bootstrap.js:56
eval @ app-next-dev.js:10
(app-pages-browser)/./node_modules/next/dist/client/app-next-dev.js @ main-app.js?v=1754872829380:193
options.factory @ webpack.js?v=1754872829380:712
__webpack_require__ @ webpack.js?v=1754872829380:37
__webpack_exec__ @ main-app.js?v=1754872829380:2836
(anonymous) @ main-app.js?v=1754872829380:2837
webpackJsonpCallback @ webpack.js?v=1754872829380:1388
(anonymous) @ main-app.js?v=1754872829380:9
PostHogProvider.tsx:9 [PostHog.js] Bad HTTP status: 404 {"type":"invalid_request","code":"not_found","detail":"Not found.","attr":null}
error @ intercept-console-error.js:50
t @ module.js:19
error @ module.js:19
on_request_error @ module.js:19
callback @ module.js:19
eval @ module.js:19
Promise.then
eval @ module.js:19
Promise.then
method @ module.js:19
eval @ module.js:19
Pe @ module.js:19
wr @ module.js:19
eval @ module.js:19
eval @ module.js:19
r.onerror @ module.js:19
script
n @ module.js:19
q @ module.js:19
v.__PosthogExtensions__.loadExternalDependency @ module.js:19
yr @ module.js:19
load @ module.js:19
Os @ module.js:19
_init @ module.js:19
init @ module.js:19
eval @ PostHogProvider.tsx:9
(app-pages-browser)/./src/components/analytics/PostHogProvider.tsx @ layout.js:1487
options.factory @ webpack.js?v=1754872829380:712
__webpack_require__ @ webpack.js?v=1754872829380:37
fn @ webpack.js?v=1754872829380:369
Promise.then
eval @ next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fclient%2FClerkProvider.js%22%2C%22ids%22%3A%5B%22*%22%2C%22ClientClerkProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fclient%2Fkeyless-cookie-sync.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FcontrolComponents.js%22%2C%22ids%22%3A%5B%22AuthenticateWithRedirectCallback%22%2C%22ClerkDegraded%22%2C%22ClerkFailed%22%2C%22ClerkLoaded%22%2C%22ClerkLoading%22%2C%22RedirectToCreateOrganization%22%2C%22RedirectToOrganizationProfile%22%2C%22RedirectToSignIn%22%2C%22RedirectToSignUp%22%2C%22RedirectToUserProfile%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2Fhooks.js%22%2C%22ids%22%3A%5B%22useAuth%22%2C%22useClerk%22%2C%22useEmailLink%22%2C%22useOrganization%22%2C%22useOrganizationList%22%2C%22useReverification%22%2C%22useSession%22%2C%22useSessionList%22%2C%22useSignIn%22%2C%22useSignUp%22%2C%22useUser%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FPromisifiedAuthProvider.js%22%2C%22ids%22%3A%5B%22PromisifiedAuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FuiComponents.js%22%2C%22ids%22%3A%5B%22APIKeys%22%2C%22CreateOrganization%22%2C%22GoogleOneTap%22%2C%22OrganizationList%22%2C%22OrganizationProfile%22%2C%22OrganizationSwitcher%22%2C%22PricingTable%22%2C%22SignIn%22%2C%22SignInButton%22%2C%22SignInWithMetamaskButton%22%2C%22SignOutButton%22%2C%22SignUp%22%2C%22SignUpButton%22%2C%22TaskSelectOrganization%22%2C%22UserButton%22%2C%22UserProfile%22%2C%22Waitlist%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2Fnext%2Ffont%2Flocal%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Flib%2Ffonts.ts%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5B%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Thin.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-ExtraLight.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22200%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Light.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22300%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Regular.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22400%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Medium.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22500%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-SemiBold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22600%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Bold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22700%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-ExtraBold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22800%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Black.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22900%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%5D%2C%5C%22variable%5C%22%3A%5C%22--font-outfit%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22outfit%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2Fnext%2Ffont%2Flocal%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Flib%2Ffonts.ts%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-VariableFont_wght.ttf%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-outfit-variable%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22outfitVariable%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fanalytics%2FPostHogProvider.tsx%22%2C%22ids%22%3A%5B%22PHProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fproviders%2FClerkSessionProvider.tsx%22%2C%22ids%22%3A%5B%22ClerkSessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fproviders%2FQueryProvider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fproviders%2FSessionProvider.tsx%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Ftheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fui%2Fsonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcontexts%2Fbackground-context.tsx%22%2C%22ids%22%3A%5B%22BackgroundProvider%22%5D%7D&server=false!:21
(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fclient%2FClerkProvider.js%22%2C%22ids%22%3A%5B%22*%22%2C%22ClientClerkProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fclient%2Fkeyless-cookie-sync.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FcontrolComponents.js%22%2C%22ids%22%3A%5B%22AuthenticateWithRedirectCallback%22%2C%22ClerkDegraded%22%2C%22ClerkFailed%22%2C%22ClerkLoaded%22%2C%22ClerkLoading%22%2C%22RedirectToCreateOrganization%22%2C%22RedirectToOrganizationProfile%22%2C%22RedirectToSignIn%22%2C%22RedirectToSignUp%22%2C%22RedirectToUserProfile%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2Fhooks.js%22%2C%22ids%22%3A%5B%22useAuth%22%2C%22useClerk%22%2C%22useEmailLink%22%2C%22useOrganization%22%2C%22useOrganizationList%22%2C%22useReverification%22%2C%22useSession%22%2C%22useSessionList%22%2C%22useSignIn%22%2C%22useSignUp%22%2C%22useUser%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FPromisifiedAuthProvider.js%22%2C%22ids%22%3A%5B%22PromisifiedAuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FuiComponents.js%22%2C%22ids%22%3A%5B%22APIKeys%22%2C%22CreateOrganization%22%2C%22GoogleOneTap%22%2C%22OrganizationList%22%2C%22OrganizationProfile%22%2C%22OrganizationSwitcher%22%2C%22PricingTable%22%2C%22SignIn%22%2C%22SignInButton%22%2C%22SignInWithMetamaskButton%22%2C%22SignOutButton%22%2C%22SignUp%22%2C%22SignUpButton%22%2C%22TaskSelectOrganization%22%2C%22UserButton%22%2C%22UserProfile%22%2C%22Waitlist%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2Fnext%2Ffont%2Flocal%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Flib%2Ffonts.ts%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5B%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Thin.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-ExtraLight.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22200%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Light.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22300%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Regular.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22400%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Medium.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22500%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-SemiBold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22600%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Bold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22700%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-ExtraBold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22800%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Black.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22900%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%5D%2C%5C%22variable%5C%22%3A%5C%22--font-outfit%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22outfit%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2Fnext%2Ffont%2Flocal%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Flib%2Ffonts.ts%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-VariableFont_wght.ttf%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-outfit-variable%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22outfitVariable%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fanalytics%2FPostHogProvider.tsx%22%2C%22ids%22%3A%5B%22PHProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fproviders%2FClerkSessionProvider.tsx%22%2C%22ids%22%3A%5B%22ClerkSessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fproviders%2FQueryProvider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fproviders%2FSessionProvider.tsx%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Ftheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fui%2Fsonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcontexts%2Fbackground-context.tsx%22%2C%22ids%22%3A%5B%22BackgroundProvider%22%5D%7D&server=false! @ layout.js:1138
options.factory @ webpack.js?v=1754872829380:712
__webpack_require__ @ webpack.js?v=1754872829380:37
__webpack_exec__ @ layout.js:1680
(anonymous) @ layout.js:1681
__webpack_require__.O @ webpack.js?v=1754872829380:84
webpackJsonpCallback @ webpack.js?v=1754872829380:1398
(anonymous) @ main-app.js?v=1754872829380:9
PostHogProvider.tsx:9 [PostHog.js] [RemoteConfig] Failed to fetch remote config from PostHog.
error @ intercept-console-error.js:50
t @ module.js:19
error @ module.js:19
Re @ module.js:19
eval @ module.js:19
callback @ module.js:19
callback @ module.js:19
eval @ module.js:19
Promise.then
eval @ module.js:19
Promise.then
method @ module.js:19
eval @ module.js:19
Pe @ module.js:19
wr @ module.js:19
eval @ module.js:19
eval @ module.js:19
r.onerror @ module.js:19
script
n @ module.js:19
q @ module.js:19
v.__PosthogExtensions__.loadExternalDependency @ module.js:19
yr @ module.js:19
load @ module.js:19
Os @ module.js:19
_init @ module.js:19
init @ module.js:19
eval @ PostHogProvider.tsx:9
(app-pages-browser)/./src/components/analytics/PostHogProvider.tsx @ layout.js:1487
options.factory @ webpack.js?v=1754872829380:712
__webpack_require__ @ webpack.js?v=1754872829380:37
fn @ webpack.js?v=1754872829380:369
Promise.then
eval @ next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fclient%2FClerkProvider.js%22%2C%22ids%22%3A%5B%22*%22%2C%22ClientClerkProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fclient%2Fkeyless-cookie-sync.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FcontrolComponents.js%22%2C%22ids%22%3A%5B%22AuthenticateWithRedirectCallback%22%2C%22ClerkDegraded%22%2C%22ClerkFailed%22%2C%22ClerkLoaded%22%2C%22ClerkLoading%22%2C%22RedirectToCreateOrganization%22%2C%22RedirectToOrganizationProfile%22%2C%22RedirectToSignIn%22%2C%22RedirectToSignUp%22%2C%22RedirectToUserProfile%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2Fhooks.js%22%2C%22ids%22%3A%5B%22useAuth%22%2C%22useClerk%22%2C%22useEmailLink%22%2C%22useOrganization%22%2C%22useOrganizationList%22%2C%22useReverification%22%2C%22useSession%22%2C%22useSessionList%22%2C%22useSignIn%22%2C%22useSignUp%22%2C%22useUser%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FPromisifiedAuthProvider.js%22%2C%22ids%22%3A%5B%22PromisifiedAuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FuiComponents.js%22%2C%22ids%22%3A%5B%22APIKeys%22%2C%22CreateOrganization%22%2C%22GoogleOneTap%22%2C%22OrganizationList%22%2C%22OrganizationProfile%22%2C%22OrganizationSwitcher%22%2C%22PricingTable%22%2C%22SignIn%22%2C%22SignInButton%22%2C%22SignInWithMetamaskButton%22%2C%22SignOutButton%22%2C%22SignUp%22%2C%22SignUpButton%22%2C%22TaskSelectOrganization%22%2C%22UserButton%22%2C%22UserProfile%22%2C%22Waitlist%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2Fnext%2Ffont%2Flocal%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Flib%2Ffonts.ts%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5B%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Thin.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-ExtraLight.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22200%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Light.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22300%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Regular.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22400%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Medium.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22500%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-SemiBold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22600%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Bold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22700%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-ExtraBold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22800%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Black.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22900%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%5D%2C%5C%22variable%5C%22%3A%5C%22--font-outfit%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22outfit%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2Fnext%2Ffont%2Flocal%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Flib%2Ffonts.ts%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-VariableFont_wght.ttf%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-outfit-variable%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22outfitVariable%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fanalytics%2FPostHogProvider.tsx%22%2C%22ids%22%3A%5B%22PHProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fproviders%2FClerkSessionProvider.tsx%22%2C%22ids%22%3A%5B%22ClerkSessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fproviders%2FQueryProvider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fproviders%2FSessionProvider.tsx%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Ftheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fui%2Fsonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcontexts%2Fbackground-context.tsx%22%2C%22ids%22%3A%5B%22BackgroundProvider%22%5D%7D&server=false!:21
(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fclient%2FClerkProvider.js%22%2C%22ids%22%3A%5B%22*%22%2C%22ClientClerkProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fclient%2Fkeyless-cookie-sync.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FcontrolComponents.js%22%2C%22ids%22%3A%5B%22AuthenticateWithRedirectCallback%22%2C%22ClerkDegraded%22%2C%22ClerkFailed%22%2C%22ClerkLoaded%22%2C%22ClerkLoading%22%2C%22RedirectToCreateOrganization%22%2C%22RedirectToOrganizationProfile%22%2C%22RedirectToSignIn%22%2C%22RedirectToSignUp%22%2C%22RedirectToUserProfile%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2Fhooks.js%22%2C%22ids%22%3A%5B%22useAuth%22%2C%22useClerk%22%2C%22useEmailLink%22%2C%22useOrganization%22%2C%22useOrganizationList%22%2C%22useReverification%22%2C%22useSession%22%2C%22useSessionList%22%2C%22useSignIn%22%2C%22useSignUp%22%2C%22useUser%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FPromisifiedAuthProvider.js%22%2C%22ids%22%3A%5B%22PromisifiedAuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FuiComponents.js%22%2C%22ids%22%3A%5B%22APIKeys%22%2C%22CreateOrganization%22%2C%22GoogleOneTap%22%2C%22OrganizationList%22%2C%22OrganizationProfile%22%2C%22OrganizationSwitcher%22%2C%22PricingTable%22%2C%22SignIn%22%2C%22SignInButton%22%2C%22SignInWithMetamaskButton%22%2C%22SignOutButton%22%2C%22SignUp%22%2C%22SignUpButton%22%2C%22TaskSelectOrganization%22%2C%22UserButton%22%2C%22UserProfile%22%2C%22Waitlist%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2Fnext%2Ffont%2Flocal%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Flib%2Ffonts.ts%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5B%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Thin.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-ExtraLight.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22200%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Light.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22300%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Regular.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22400%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Medium.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22500%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-SemiBold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22600%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Bold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22700%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-ExtraBold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22800%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-Black.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22900%5C%22%2C%5C%22style%5C%22%3A%5C%22normal%5C%22%7D%5D%2C%5C%22variable%5C%22%3A%5C%22--font-outfit%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22outfit%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fnode_modules%2Fnext%2Ffont%2Flocal%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Flib%2Ffonts.ts%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22..%2F..%2Fpublic%2Ffonts%2Foutfit%2FOutfit-VariableFont_wght.ttf%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-outfit-variable%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22outfitVariable%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fanalytics%2FPostHogProvider.tsx%22%2C%22ids%22%3A%5B%22PHProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fproviders%2FClerkSessionProvider.tsx%22%2C%22ids%22%3A%5B%22ClerkSessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fproviders%2FQueryProvider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fproviders%2FSessionProvider.tsx%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Ftheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcomponents%2Fui%2Fsonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frishit%2Fsrc%2Frepos%2FsiiftFE%2Fsrc%2Fcontexts%2Fbackground-context.tsx%22%2C%22ids%22%3A%5B%22BackgroundProvider%22%5D%7D&server=false! @ layout.js:1138
options.factory @ webpack.js?v=1754872829380:712
__webpack_require__ @ webpack.js?v=1754872829380:37
__webpack_exec__ @ layout.js:1680
(anonymous) @ layout.js:1681
__webpack_require__.O @ webpack.js?v=1754872829380:84
webpackJsonpCallback @ webpack.js?v=1754872829380:1398
(anonymous) @ main-app.js?v=1754872829380:9
PostHogProvider.tsx:31 [PostHog.js] send "$pageview" {uuid: '********-6dc7-7ce8-a029-1588c1bf225f', event: '$pageview', properties: {…}, $set_once: {…}, timestamp: Sun Aug 10 2025 17:40:35 GMT-0700 (Pacific Daylight Time)}
ClerkSessionProvider.tsx:36 🔐 Clerk Authentication Success
ClerkSessionProvider.tsx:37 👤 User: <EMAIL>
ClerkSessionProvider.tsx:38 🎫 Token: eyJhbGciOiJSUzI1NiIsImNhdCI6ImNsX0I3ZDRQRDExMUFBQSIsImtpZCI6Imluc18zMFJMZEhtN3pDanhCazZZNGQ4VGlrZ1VreWEiLCJ0eXAiOiJKV1QifQ.******************************************************************************************************************************************************************************************************************************************************************************************************************************.G7roiltkRRa7DQJkW_0-nQFRda_6XgolykUBo4k_hx_WWA1ZhseLHt63IpDZyz8uZ0E8-zaavnZUJAoO3gte68EVO0RgVl4Fs2YYtSXYkdopyoVP0BbpfhLHUgg2W1leuyoh5sWz5yN6wTECXhgDeokVFItslf4FAz5E5SKQx6cN0azxFOqM3bs2iCpyl0hbfkIGktdEp0cMZIE2WcXQqpSeFp7awzxy92-1tbQMEDUrd9yFur2BXxANfEHhPvK_-nYPCQUaPyB1j1arn_-AoHZfpNE7DsoL-dvGtxJLaec9CLUht5QlCzGVc8ZzWoBWnMPX2uHBefWoNw8a287reg
ClerkSessionProvider.tsx:39 📏 Token Length: 784
ClerkSessionProvider.tsx:40 🆔 User ID: user_30cifUbzDXOvTvxvvXrhxgZlKOm
ClerkSessionProvider.tsx:44 ⏰ Token Expires: Sun Aug 10 2025 17:41:35 GMT-0700 (Pacific Daylight Time)
ClerkSessionProvider.tsx:45 🏷️ Token Subject: user_30cifUbzDXOvTvxvvXrhxgZlKOm
useAnalytics.ts:92 [PostHog.js] send "$set" {uuid: '********-6dcd-74e2-bd4b-2c7eae3c8f9b', event: '$set', properties: {…}, timestamp: Sun Aug 10 2025 17:40:35 GMT-0700 (Pacific Daylight Time)}event: "$set"offset: 1981properties: {$os: 'Mac OS X', $os_version: '10.15.7', $browser: 'Chrome', $device_type: 'Desktop', $timezone: 'America/Vancouver', …}uuid: "********-6dcd-74e2-bd4b-2c7eae3c8f9b"[[Prototype]]: Object
useAnalytics.ts:92  POST https://us.i.posthog.com/flags/?v=2&config=true&ip=0&_=1754872835548&ver=1.258.2&compression=base64 401 (Unauthorized)
method @ module.js:19
eval @ module.js:19
Pe @ module.js:19
Ee @ module.js:19
eval @ module.js:19
setTimeout
reloadFeatureFlags @ module.js:19
reloadFeatureFlags @ module.js:19
setPersonPropertiesForFlags @ module.js:19
setPersonPropertiesForFlags @ module.js:19
setPersonProperties @ module.js:19
identify @ module.js:19
eval @ module.js:19
useAnalytics.useCallback[identifyUser] @ useAnalytics.ts:92
ClerkSessionProvider.useEffect.syncClerkWithSession @ ClerkSessionProvider.tsx:94
await in ClerkSessionProvider.useEffect.syncClerkWithSession
ClerkSessionProvider.useEffect @ ClerkSessionProvider.tsx:117
react-stack-bottom-frame @ react-dom-client.development.js:23055
runWithFiberInDEV @ react-dom-client.development.js:845
commitHookEffectListMount @ react-dom-client.development.js:11978
commitHookPassiveMountEffects @ react-dom-client.development.js:12099
commitPassiveMountOnFiber @ react-dom-client.development.js:13929
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13932
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13932
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13932
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13932
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13941
flushPassiveEffects @ react-dom-client.development.js:15869
eval @ react-dom-client.development.js:15505
performWorkUntilDeadline @ scheduler.development.js:45
"use client"
RootLayout @ layout.tsx:68
eval @ react-server-dom-webpack-client.browser.development.js:2355
initializeModelChunk @ react-server-dom-webpack-client.browser.development.js:1054
readChunk @ react-server-dom-webpack-client.browser.development.js:949
react-stack-bottom-frame @ react-dom-client.development.js:23078
reconcileChildFibersImpl @ react-dom-client.development.js:5999
eval @ react-dom-client.development.js:6118
reconcileChildren @ react-dom-client.development.js:8655
beginWork @ react-dom-client.development.js:10755
runWithFiberInDEV @ react-dom-client.development.js:845
performUnitOfWork @ react-dom-client.development.js:15258
workLoopConcurrentByScheduler @ react-dom-client.development.js:15252
renderRootConcurrent @ react-dom-client.development.js:15227
performWorkOnRoot @ react-dom-client.development.js:14525
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16350
performWorkUntilDeadline @ scheduler.development.js:45
<RootLayout>
buildFakeTask @ react-server-dom-webpack-client.browser.development.js:2040
initializeFakeTask @ react-server-dom-webpack-client.browser.development.js:2027
resolveDebugInfo @ react-server-dom-webpack-client.browser.development.js:2063
processFullStringRow @ react-server-dom-webpack-client.browser.development.js:2261
processFullBinaryRow @ react-server-dom-webpack-client.browser.development.js:2233
progress @ react-server-dom-webpack-client.browser.development.js:2479
"use server"
ResponseInstance @ react-server-dom-webpack-client.browser.development.js:1587
createResponseFromOptions @ react-server-dom-webpack-client.browser.development.js:2396
exports.createFromReadableStream @ react-server-dom-webpack-client.browser.development.js:2717
eval @ app-index.js:132
(app-pages-browser)/./node_modules/next/dist/client/app-index.js @ main-app.js?v=1754872829380:171
options.factory @ webpack.js?v=1754872829380:712
__webpack_require__ @ webpack.js?v=1754872829380:37
fn @ webpack.js?v=1754872829380:369
eval @ app-next-dev.js:11
eval @ app-bootstrap.js:62
loadScriptsInSequence @ app-bootstrap.js:23
appBootstrap @ app-bootstrap.js:56
eval @ app-next-dev.js:10
(app-pages-browser)/./node_modules/next/dist/client/app-next-dev.js @ main-app.js?v=1754872829380:193
options.factory @ webpack.js?v=1754872829380:712
__webpack_require__ @ webpack.js?v=1754872829380:37
__webpack_exec__ @ main-app.js?v=1754872829380:2836
(anonymous) @ main-app.js?v=1754872829380:2837
webpackJsonpCallback @ webpack.js?v=1754872829380:1388
(anonymous) @ main-app.js?v=1754872829380:9
useAnalytics.ts:92 [PostHog.js] [RateLimiter] could not rate limit - continuing. Error: "Unexpected token 'T', "The provid"... is not valid JSON" {text: 'The provided API key is invalid or has expired. Please check your API key and try again.'}
t @ module.js:19
warn @ module.js:19
Oo.checkForLimiting @ module.js:19
callback @ module.js:19
eval @ module.js:19
Promise.then
eval @ module.js:19
Promise.then
method @ module.js:19
eval @ module.js:19
Pe @ module.js:19
Ee @ module.js:19
eval @ module.js:19
setTimeout
reloadFeatureFlags @ module.js:19
reloadFeatureFlags @ module.js:19
setPersonPropertiesForFlags @ module.js:19
setPersonPropertiesForFlags @ module.js:19
setPersonProperties @ module.js:19
identify @ module.js:19
eval @ module.js:19
useAnalytics.useCallback[identifyUser] @ useAnalytics.ts:92
ClerkSessionProvider.useEffect.syncClerkWithSession @ ClerkSessionProvider.tsx:94
await in ClerkSessionProvider.useEffect.syncClerkWithSession
ClerkSessionProvider.useEffect @ ClerkSessionProvider.tsx:117
react-stack-bottom-frame @ react-dom-client.development.js:23055
runWithFiberInDEV @ react-dom-client.development.js:845
commitHookEffectListMount @ react-dom-client.development.js:11978
commitHookPassiveMountEffects @ react-dom-client.development.js:12099
commitPassiveMountOnFiber @ react-dom-client.development.js:13929
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13932
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13932
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13932
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13932
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13941
flushPassiveEffects @ react-dom-client.development.js:15869
eval @ react-dom-client.development.js:15505
performWorkUntilDeadline @ scheduler.development.js:45
"use client"
RootLayout @ layout.tsx:68
eval @ react-server-dom-webpack-client.browser.development.js:2355
initializeModelChunk @ react-server-dom-webpack-client.browser.development.js:1054
readChunk @ react-server-dom-webpack-client.browser.development.js:949
react-stack-bottom-frame @ react-dom-client.development.js:23078
reconcileChildFibersImpl @ react-dom-client.development.js:5999
eval @ react-dom-client.development.js:6118
reconcileChildren @ react-dom-client.development.js:8655
beginWork @ react-dom-client.development.js:10755
runWithFiberInDEV @ react-dom-client.development.js:845
performUnitOfWork @ react-dom-client.development.js:15258
workLoopConcurrentByScheduler @ react-dom-client.development.js:15252
renderRootConcurrent @ react-dom-client.development.js:15227
performWorkOnRoot @ react-dom-client.development.js:14525
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16350
performWorkUntilDeadline @ scheduler.development.js:45
<RootLayout>
buildFakeTask @ react-server-dom-webpack-client.browser.development.js:2040
initializeFakeTask @ react-server-dom-webpack-client.browser.development.js:2027
resolveDebugInfo @ react-server-dom-webpack-client.browser.development.js:2063
processFullStringRow @ react-server-dom-webpack-client.browser.development.js:2261
processFullBinaryRow @ react-server-dom-webpack-client.browser.development.js:2233
progress @ react-server-dom-webpack-client.browser.development.js:2479
"use server"
ResponseInstance @ react-server-dom-webpack-client.browser.development.js:1587
createResponseFromOptions @ react-server-dom-webpack-client.browser.development.js:2396
exports.createFromReadableStream @ react-server-dom-webpack-client.browser.development.js:2717
eval @ app-index.js:132
(app-pages-browser)/./node_modules/next/dist/client/app-index.js @ main-app.js?v=1754872829380:171
options.factory @ webpack.js?v=1754872829380:712
__webpack_require__ @ webpack.js?v=1754872829380:37
fn @ webpack.js?v=1754872829380:369
eval @ app-next-dev.js:11
eval @ app-bootstrap.js:62
loadScriptsInSequence @ app-bootstrap.js:23
appBootstrap @ app-bootstrap.js:56
eval @ app-next-dev.js:10
(app-pages-browser)/./node_modules/next/dist/client/app-next-dev.js @ main-app.js?v=1754872829380:193
options.factory @ webpack.js?v=1754872829380:712
__webpack_require__ @ webpack.js?v=1754872829380:37
__webpack_exec__ @ main-app.js?v=1754872829380:2836
(anonymous) @ main-app.js?v=1754872829380:2837
webpackJsonpCallback @ webpack.js?v=1754872829380:1388
(anonymous) @ main-app.js?v=1754872829380:9
useAnalytics.ts:92 [PostHog.js] Bad HTTP status: 401 The provided API key is invalid or has expired. Please check your API key and try again.
error @ intercept-console-error.js:50
t @ module.js:19
error @ module.js:19
on_request_error @ module.js:19
callback @ module.js:19
eval @ module.js:19
Promise.then
eval @ module.js:19
Promise.then
method @ module.js:19
eval @ module.js:19
Pe @ module.js:19
Ee @ module.js:19
eval @ module.js:19
setTimeout
reloadFeatureFlags @ module.js:19
reloadFeatureFlags @ module.js:19
setPersonPropertiesForFlags @ module.js:19
setPersonPropertiesForFlags @ module.js:19
setPersonProperties @ module.js:19
identify @ module.js:19
eval @ module.js:19
useAnalytics.useCallback[identifyUser] @ useAnalytics.ts:92
ClerkSessionProvider.useEffect.syncClerkWithSession @ ClerkSessionProvider.tsx:94
await in ClerkSessionProvider.useEffect.syncClerkWithSession
ClerkSessionProvider.useEffect @ ClerkSessionProvider.tsx:117
react-stack-bottom-frame @ react-dom-client.development.js:23055
runWithFiberInDEV @ react-dom-client.development.js:845
commitHookEffectListMount @ react-dom-client.development.js:11978
commitHookPassiveMountEffects @ react-dom-client.development.js:12099
commitPassiveMountOnFiber @ react-dom-client.development.js:13929
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13932
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13932
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13932
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13932
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13941
flushPassiveEffects @ react-dom-client.development.js:15869
eval @ react-dom-client.development.js:15505
performWorkUntilDeadline @ scheduler.development.js:45
"use client"
RootLayout @ layout.tsx:68
eval @ react-server-dom-webpack-client.browser.development.js:2355
initializeModelChunk @ react-server-dom-webpack-client.browser.development.js:1054
readChunk @ react-server-dom-webpack-client.browser.development.js:949
react-stack-bottom-frame @ react-dom-client.development.js:23078
reconcileChildFibersImpl @ react-dom-client.development.js:5999
eval @ react-dom-client.development.js:6118
reconcileChildren @ react-dom-client.development.js:8655
beginWork @ react-dom-client.development.js:10755
runWithFiberInDEV @ react-dom-client.development.js:845
performUnitOfWork @ react-dom-client.development.js:15258
workLoopConcurrentByScheduler @ react-dom-client.development.js:15252
renderRootConcurrent @ react-dom-client.development.js:15227
performWorkOnRoot @ react-dom-client.development.js:14525
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16350
performWorkUntilDeadline @ scheduler.development.js:45
<RootLayout>
buildFakeTask @ react-server-dom-webpack-client.browser.development.js:2040
initializeFakeTask @ react-server-dom-webpack-client.browser.development.js:2027
resolveDebugInfo @ react-server-dom-webpack-client.browser.development.js:2063
processFullStringRow @ react-server-dom-webpack-client.browser.development.js:2261
processFullBinaryRow @ react-server-dom-webpack-client.browser.development.js:2233
progress @ react-server-dom-webpack-client.browser.development.js:2479
"use server"
ResponseInstance @ react-server-dom-webpack-client.browser.development.js:1587
createResponseFromOptions @ react-server-dom-webpack-client.browser.development.js:2396
exports.createFromReadableStream @ react-server-dom-webpack-client.browser.development.js:2717
eval @ app-index.js:132
(app-pages-browser)/./node_modules/next/dist/client/app-index.js @ main-app.js?v=1754872829380:171
options.factory @ webpack.js?v=1754872829380:712
__webpack_require__ @ webpack.js?v=1754872829380:37
fn @ webpack.js?v=1754872829380:369
eval @ app-next-dev.js:11
eval @ app-bootstrap.js:62
loadScriptsInSequence @ app-bootstrap.js:23
appBootstrap @ app-bootstrap.js:56
eval @ app-next-dev.js:10
(app-pages-browser)/./node_modules/next/dist/client/app-next-dev.js @ main-app.js?v=1754872829380:193
options.factory @ webpack.js?v=1754872829380:712
__webpack_require__ @ webpack.js?v=1754872829380:37
__webpack_exec__ @ main-app.js?v=1754872829380:2836
(anonymous) @ main-app.js?v=1754872829380:2837
webpackJsonpCallback @ webpack.js?v=1754872829380:1388
(anonymous) @ main-app.js?v=1754872829380:9
useAnalytics.ts:92 [PostHog.js] [FeatureFlags] Using an older version of the feature flags endpoint. Please upgrade your PostHog server to the latest version
t @ module.js:19
warn @ module.js:19
vo @ module.js:19
eval @ module.js:19
receivedFeatureFlags @ module.js:19
callback @ module.js:19
callback @ module.js:19
eval @ module.js:19
Promise.then
eval @ module.js:19
Promise.then
method @ module.js:19
eval @ module.js:19
Pe @ module.js:19
Ee @ module.js:19
eval @ module.js:19
setTimeout
reloadFeatureFlags @ module.js:19
reloadFeatureFlags @ module.js:19
setPersonPropertiesForFlags @ module.js:19
setPersonPropertiesForFlags @ module.js:19
setPersonProperties @ module.js:19
identify @ module.js:19
eval @ module.js:19
useAnalytics.useCallback[identifyUser] @ useAnalytics.ts:92
ClerkSessionProvider.useEffect.syncClerkWithSession @ ClerkSessionProvider.tsx:94
await in ClerkSessionProvider.useEffect.syncClerkWithSession
ClerkSessionProvider.useEffect @ ClerkSessionProvider.tsx:117
react-stack-bottom-frame @ react-dom-client.development.js:23055
runWithFiberInDEV @ react-dom-client.development.js:845
commitHookEffectListMount @ react-dom-client.development.js:11978
commitHookPassiveMountEffects @ react-dom-client.development.js:12099
commitPassiveMountOnFiber @ react-dom-client.development.js:13929
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13932
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13932
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13932
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13932
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13941
flushPassiveEffects @ react-dom-client.development.js:15869
eval @ react-dom-client.development.js:15505
performWorkUntilDeadline @ scheduler.development.js:45
"use client"
RootLayout @ layout.tsx:68
eval @ react-server-dom-webpack-client.browser.development.js:2355
initializeModelChunk @ react-server-dom-webpack-client.browser.development.js:1054
readChunk @ react-server-dom-webpack-client.browser.development.js:949
react-stack-bottom-frame @ react-dom-client.development.js:23078
reconcileChildFibersImpl @ react-dom-client.development.js:5999
eval @ react-dom-client.development.js:6118
reconcileChildren @ react-dom-client.development.js:8655
beginWork @ react-dom-client.development.js:10755
runWithFiberInDEV @ react-dom-client.development.js:845
performUnitOfWork @ react-dom-client.development.js:15258
workLoopConcurrentByScheduler @ react-dom-client.development.js:15252
renderRootConcurrent @ react-dom-client.development.js:15227
performWorkOnRoot @ react-dom-client.development.js:14525
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16350
performWorkUntilDeadline @ scheduler.development.js:45
<RootLayout>
buildFakeTask @ react-server-dom-webpack-client.browser.development.js:2040
initializeFakeTask @ react-server-dom-webpack-client.browser.development.js:2027
resolveDebugInfo @ react-server-dom-webpack-client.browser.development.js:2063
processFullStringRow @ react-server-dom-webpack-client.browser.development.js:2261
processFullBinaryRow @ react-server-dom-webpack-client.browser.development.js:2233
progress @ react-server-dom-webpack-client.browser.development.js:2479
"use server"
ResponseInstance @ react-server-dom-webpack-client.browser.development.js:1587
createResponseFromOptions @ react-server-dom-webpack-client.browser.development.js:2396
exports.createFromReadableStream @ react-server-dom-webpack-client.browser.development.js:2717
eval @ app-index.js:132
(app-pages-browser)/./node_modules/next/dist/client/app-index.js @ main-app.js?v=1754872829380:171
options.factory @ webpack.js?v=1754872829380:712
__webpack_require__ @ webpack.js?v=1754872829380:37
fn @ webpack.js?v=1754872829380:369
eval @ app-next-dev.js:11
eval @ app-bootstrap.js:62
loadScriptsInSequence @ app-bootstrap.js:23
appBootstrap @ app-bootstrap.js:56
eval @ app-next-dev.js:10
(app-pages-browser)/./node_modules/next/dist/client/app-next-dev.js @ main-app.js?v=1754872829380:193
options.factory @ webpack.js?v=1754872829380:712
__webpack_require__ @ webpack.js?v=1754872829380:37
__webpack_exec__ @ main-app.js?v=1754872829380:2836
(anonymous) @ main-app.js?v=1754872829380:2837
webpackJsonpCallback @ webpack.js?v=1754872829380:1388
(anonymous) @ main-app.js?v=1754872829380:9
hot-reloader-client.js:197 [Fast Refresh] rebuilding
report-hmr-latency.js:14 [Fast Refresh] done in 1324ms
