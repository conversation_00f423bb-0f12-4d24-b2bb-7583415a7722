export interface BlogPost {
  id: string;
  slug: string;
  title: string;
  description: string;
  content: string;
  image?: string;
  publishedAt: string;
  updatedAt?: string;
  readTime: number; // in minutes
  featured: boolean;
}

export interface BlogCardProps {
  post: BlogPost;
  className?: string;
}

export interface BlogSectionProps {
  posts: BlogPost[];
  showViewAll?: boolean;
  className?: string;
}
