"use client";

// Prevent static generation for this page
export const dynamic = 'force-dynamic';

import { zodResolver } from "@hookform/resolvers/zod";
import {
  AlertCircle,
  Bell,
  CheckCircle,
  Eye,
  EyeOff,
  Globe,
  Loader2,
  Save,
  Shield,
} from "lucide-react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { DashboardLayout } from "@/components/layout/dashboard-layout";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { useAuth } from "@/hooks/useAuth";

const notificationSchema = z.object({
  emailNotifications: z.boolean(),
  projectUpdates: z.boolean(),
  securityAlerts: z.boolean(),
  marketingEmails: z.boolean(),
});

const securitySchema = z
  .object({
    currentPassword: z.string().min(1, "Current password is required"),
    newPassword: z.string().min(8, "Password must be at least 8 characters"),
    confirmPassword: z.string().min(1, "Please confirm your password"),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"],
  });

type NotificationFormData = z.infer<typeof notificationSchema>;
type SecurityFormData = z.infer<typeof securitySchema>;

// Utility function to safely format dates
const formatDate = (date: Date | string): string => {
  try {
    const dateObj = date instanceof Date ? date : new Date(date);
    return dateObj.toLocaleDateString();
  } catch (error) {
    return "Invalid date";
  }
};

export default function SettingsPage() {
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [message, setMessage] = useState<{
    type: "success" | "error";
    text: string;
  } | null>(null);

  // Mock user settings
  const userSettings = {
    emailNotifications: true,
    projectUpdates: true,
    securityAlerts: true,
    marketingEmails: false,
  };

  const {
    register: registerNotifications,
    handleSubmit: handleNotificationSubmit,
    formState: { isDirty: notificationDirty },
    reset: resetNotifications,
  } = useForm<NotificationFormData>({
    resolver: zodResolver(notificationSchema),
    defaultValues: {
      emailNotifications: userSettings.emailNotifications,
      projectUpdates: userSettings.projectUpdates,
      securityAlerts: userSettings.securityAlerts,
      marketingEmails: userSettings.marketingEmails,
    },
  });

  const {
    register: registerSecurity,
    handleSubmit: handleSecuritySubmit,
    formState: { errors: securityErrors },
    reset: resetSecurity,
  } = useForm<SecurityFormData>({
    resolver: zodResolver(securitySchema),
  });

  const onNotificationSubmit = async (data: NotificationFormData) => {
    setIsLoading(true);
    setMessage(null);

    try {
      // Mock API call - simulate network delay
      await new Promise((resolve) => setTimeout(resolve, 1000));

      setMessage({
        type: "success",
        text: "Notification settings updated successfully!",
      });
      resetNotifications(data);
    } catch (error: any) {
      setMessage({
        type: "error",
        text: error.message || "Failed to update notification settings",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const onSecuritySubmit = async (_data: SecurityFormData) => {
    setIsLoading(true);
    setMessage(null);

    try {
      // Mock API call - simulate network delay
      await new Promise((resolve) => setTimeout(resolve, 1000));

      setMessage({ type: "success", text: "Password updated successfully!" });
      resetSecurity();
    } catch (error: any) {
      setMessage({
        type: "error",
        text: error.message || "Failed to update password",
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (!user) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-destructive mb-2">
              Access Denied
            </h1>
            <p className="text-muted-foreground">
              You need to be signed in to view this page.
            </p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-foreground">
            Settings
          </h1>
          <p className="text-muted-foreground">
            Manage your account settings and preferences.
          </p>
        </div>

        <div className="space-y-6">
          {/* Notification Settings */}
          <Card className="bg-gray-50 dark:bg-card border hover:shadow-md transition-all duration-200">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2 text-card-foreground">
                <Bell className="h-5 w-5 text-accent" />
                <span>Notification Settings</span>
              </CardTitle>
              <CardDescription className="text-muted-foreground">
                Configure how you receive notifications and updates.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form
                onSubmit={handleNotificationSubmit(onNotificationSubmit)}
                className="space-y-4"
              >
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="emailNotifications"
                      {...registerNotifications("emailNotifications")}
                      className="border-border data-[state=checked]:bg-accent data-[state=checked]:border-accent"
                    />
                    <Label
                      htmlFor="emailNotifications"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-card-foreground"
                    >
                      Email Notifications
                    </Label>
                  </div>
                  <p className="text-sm text-muted-foreground ml-6">
                    Receive important updates and notifications via email.
                  </p>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="projectUpdates"
                      {...registerNotifications("projectUpdates")}
                      className="border-border data-[state=checked]:bg-accent data-[state=checked]:border-accent"
                    />
                    <Label
                      htmlFor="projectUpdates"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-card-foreground"
                    >
                      Project Updates
                    </Label>
                  </div>
                  <p className="text-sm text-muted-foreground ml-6">
                    Get notified when projects you're working on are updated.
                  </p>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="securityAlerts"
                      {...registerNotifications("securityAlerts")}
                      className="border-border data-[state=checked]:bg-accent data-[state=checked]:border-accent"
                    />
                    <Label
                      htmlFor="securityAlerts"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-card-foreground"
                    >
                      Security Alerts
                    </Label>
                  </div>
                  <p className="text-sm text-muted-foreground ml-6">
                    Receive notifications about security-related activities on
                    your account.
                  </p>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="marketingEmails"
                      {...registerNotifications("marketingEmails")}
                      className="border-border data-[state=checked]:bg-accent data-[state=checked]:border-accent"
                    />
                    <Label
                      htmlFor="marketingEmails"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-card-foreground"
                    >
                      Marketing Emails
                    </Label>
                  </div>
                  <p className="text-sm text-muted-foreground ml-6">
                    Receive promotional emails and newsletters about new
                    features.
                  </p>
                </div>

                <div className="flex justify-end">
                  <Button
                    type="submit"
                    disabled={isLoading || !notificationDirty}
                    className="bg-accent text-accent-foreground hover:bg-accent/90 focus:ring-accent/20 border"
                  >
                    {isLoading && (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    )}
                    <Save className="mr-2 h-4 w-4" />
                    Save Notification Settings
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>

          {/* Security Settings */}
          <Card className="bg-gray-50 dark:bg-card border hover:shadow-md transition-all duration-200">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2 text-card-foreground">
                <Shield className="h-5 w-5 text-accent" />
                <span>Security Settings</span>
              </CardTitle>
              <CardDescription className="text-muted-foreground">
                Manage your account security and password.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {/* Account Status */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-card-foreground">
                    Account Status
                  </h3>
                  <div className="grid gap-4 sm:grid-cols-2">
                    <div className="flex items-center space-x-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span className="text-sm text-card-foreground">
                        Email Verified
                      </span>
                      <Badge
                        variant="outline"
                        className="text-xs border-green-500/20 text-green-600 bg-green-500/10"
                      >
                        Verified
                      </Badge>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Shield className="h-4 w-4 text-blue-500" />
                      <span className="text-sm text-card-foreground">
                        Two-Factor Auth
                      </span>
                      <Badge
                        variant="outline"
                       className="text-xs border-[color:var(--border)] text-[var(--siift-dark-main)] bg-[color:var(--siift-light-main)]/40"
                      >
                        Disabled
                      </Badge>
                    </div>
                  </div>
                </div>

                <Separator className="bg-border" />

                {/* Change Password */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-card-foreground">
                    Change Password
                  </h3>
                  <form
                    onSubmit={handleSecuritySubmit(onSecuritySubmit)}
                    className="space-y-4"
                  >
                    <div className="space-y-2">
                      <Label
                        htmlFor="currentPassword"
                        className="text-card-foreground"
                      >
                        Current Password
                      </Label>
                      <Input
                        id="currentPassword"
                        type="password"
                        {...registerSecurity("currentPassword")}
                        placeholder="Enter your current password"
                        className="border-border focus:border-accent focus:ring-accent/20"
                      />
                      {securityErrors.currentPassword && (
                        <p className="text-sm text-destructive">
                          {securityErrors.currentPassword.message}
                        </p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label
                        htmlFor="newPassword"
                        className="text-card-foreground"
                      >
                        New Password
                      </Label>
                      <div className="relative">
                        <Input
                          id="newPassword"
                          type={showPassword ? "text" : "password"}
                          {...registerSecurity("newPassword")}
                          placeholder="Enter your new password"
                          className="border-border focus:border-accent focus:ring-accent/20"
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-accent/10 hover:text-accent"
                          onClick={() => setShowPassword(!showPassword)}
                        >
                          {showPassword ? (
                            <EyeOff className="h-4 w-4" />
                          ) : (
                            <Eye className="h-4 w-4" />
                          )}
                        </Button>
                      </div>
                      {securityErrors.newPassword && (
                        <p className="text-sm text-destructive">
                          {securityErrors.newPassword.message}
                        </p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label
                        htmlFor="confirmPassword"
                        className="text-card-foreground"
                      >
                        Confirm New Password
                      </Label>
                      <Input
                        id="confirmPassword"
                        type="password"
                        {...registerSecurity("confirmPassword")}
                        placeholder="Confirm your new password"
                        className="border-border focus:border-accent focus:ring-accent/20"
                      />
                      {securityErrors.confirmPassword && (
                        <p className="text-sm text-destructive">
                          {securityErrors.confirmPassword.message}
                        </p>
                      )}
                    </div>

                    <div className="flex justify-end">
                      <Button
                        type="submit"
                        disabled={isLoading}
                        className="bg-accent text-accent-foreground hover:bg-accent/90 focus:ring-accent/20 border"
                      >
                        {isLoading && (
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        )}
                        <Save className="mr-2 h-4 w-4" />
                        Update Password
                      </Button>
                    </div>
                  </form>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Account Information */}
          <Card className="bg-gray-50 dark:bg-card border hover:shadow-md transition-all duration-200">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2 text-card-foreground">
                <Globe className="h-5 w-5 text-accent" />
                <span>Account Information</span>
              </CardTitle>
              <CardDescription className="text-muted-foreground">
                Your account details and preferences.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid gap-4 sm:grid-cols-2">
                  <div>
                    <Label className="text-sm font-medium text-card-foreground">
                      Email Address
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      {user.email}
                    </p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-card-foreground">
                      Account Created
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      {formatDate(user.createdAt)}
                    </p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-card-foreground">
                      Last Updated
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      {formatDate(user.updatedAt)}
                    </p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-card-foreground">
                      Account Status
                    </Label>
                    <Badge
                      variant="outline"
                      className="text-xs border-green-500/20 text-green-600 bg-green-500/10"
                    >
                      Active
                    </Badge>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Success/Error Message */}
        {message && (
          <div
            className={`p-4 rounded-md border ${
              message.type === "success"
                ? "bg-green-500/10 text-green-700 border-green-500/20"
                : "bg-red-500/10 text-red-700 border-red-500/20"
            }`}
          >
            <div className="flex items-center space-x-2">
              {message.type === "success" ? (
                <CheckCircle className="h-4 w-4" />
              ) : (
                <AlertCircle className="h-4 w-4" />
              )}
              <span className="text-sm font-medium">{message.text}</span>
            </div>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
}
