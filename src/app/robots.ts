import { MetadataRoute } from 'next'

export default function robots(): MetadataRoute.Robots {
  const baseUrl = 'https://siift.app' // Replace with your domain

  return {
    rules: [
      {
        userAgent: '*',
        allow: ['/', '/blog/', '/blog/*'],
        disallow: [
          '/admin/',
          '/api/',
          '/auth/',
          '/profile/',
          '/settings/',
          '/user-dashboard/',
          '/projects/*/edit',
          '/_next/',
          '/private/',
        ],
      },
      {
        userAgent: 'Googlebot',
        allow: ['/', '/blog/', '/blog/*'],
        disallow: [
          '/admin/',
          '/api/',
          '/auth/',
          '/profile/',
          '/settings/',
          '/user-dashboard/',
          '/projects/*/edit',
        ],
      },
    ],
    sitemap: `${baseUrl}/sitemap.xml`,
    host: baseUrl,
  }
}
