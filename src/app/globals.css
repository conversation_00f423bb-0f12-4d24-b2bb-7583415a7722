@import "tailwindcss";
@import "tw-animate-css";
@import "../styles/design-tokens.css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-brand: var(--brand);
  --color-brand-foreground: var(--brand-foreground);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);
  --radius: 0.3rem;
  --tracking-tighter: calc(var(--tracking-normal) - 0.05em);
  --tracking-tight: calc(var(--tracking-normal) - 0.025em);
  --tracking-wide: calc(var(--tracking-normal) + 0.025em);
  --tracking-wider: calc(var(--tracking-normal) + 0.05em);
  --tracking-widest: calc(var(--tracking-normal) + 0.1em);
  --tracking-normal: var(--tracking-normal);
  --spacing: var(--spacing);
  --letter-spacing: var(--letter-spacing);
  --shadow-offset-y: var(--shadow-offset-y);
  --shadow-offset-x: var(--shadow-offset-x);
  --shadow-spread: var(--shadow-spread);
  --shadow-blur: var(--shadow-blur);
  --shadow-opacity: var(--shadow-opacity);
  --color-shadow-color: var(--shadow-color);
  --max-width-container: 80rem;

  --keyframes-appear: {
    "0%": { opacity: "0", transform: "translateY(10px)" },
    "100%": { opacity: "1", transform: "translateY(0)" }
  };
  --keyframes-appear-zoom: {
    "0%": { opacity: "0", transform: "scale(0.95)" },
    "100%": { opacity: "1", transform: "scale(1)" }
  };
  --animation-appear: "appear 0.5s ease-out forwards";
  --animation-appear-zoom: "appear-zoom 0.5s ease-out forwards";
}

:root {
  --background: oklch(1.0000 0 0);
  --foreground: oklch(0.3211 0 0);
  --card: oklch(1.0000 0 0);
  --card-foreground: oklch(0.3211 0 0);
  --popover: oklch(1.0000 0 0);
  --popover-foreground: oklch(0.3211 0 0);
  --primary: oklch(0.7234 0.1567 142.3456);
  --primary-foreground: oklch(1.0000 0 0);
  --secondary: oklch(0.4375 0.0929 159.3902);
  --secondary-foreground: oklch(1.0000 0 0);
  --muted: oklch(0.9166 0.0148 102.4717);
  --muted-foreground: oklch(0.5382 0 0);
  --accent: oklch(0.7234 0.1567 142.3456);
  --accent-foreground: oklch(1.0000 0 0);
  --destructive: oklch(0.6766 0.1260 25.1211);
  --destructive-foreground: oklch(1.0000 0 0);
  --border: oklch(0.8699 0 0);
  --input: oklch(0.8699 0 0);
  --ring: oklch(0.7234 0.1567 142.3456);
  --chart-1: oklch(0.7234 0.1567 142.3456);
  --chart-2: oklch(0.7196 0.0906 267.0774);
  --chart-3: oklch(0.8118 0.0701 218.3524);
  --chart-4: oklch(0.6019 0.0723 251.0410);
  --chart-5: oklch(0.5737 0.1247 152.5238);
  --sidebar: oklch(1.0000 0 0);
  --sidebar-foreground: oklch(0.3211 0 0);
  --sidebar-primary: oklch(0.7234 0.1567 142.3456);
  --sidebar-primary-foreground: oklch(1.0000 0 0);
  --sidebar-accent: oklch(1.0000 0 0);
  --sidebar-accent-foreground: oklch(0.3211 0 0);
  --sidebar-border: oklch(0.8699 0 0);
  --sidebar-ring: oklch(0.7234 0.1567 142.3456);
  --brand: 142 76% 36%;
  --brand-foreground: 142 76% 46%;
  --font-sans: var(--font-inter), Inter, ui-sans-serif, system-ui, -apple-system, Segoe UI, Roboto, Helvetica Neue, Arial, "Noto Sans", "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", sans-serif;
  --font-serif: Source Serif 4, serif;
  --font-mono: JetBrains Mono, monospace;
  --radius: 0.5rem;
  --shadow-2xs: none;
  --shadow-xs: none;
  --shadow-sm: none;
  --shadow: none;
  --shadow-md: none;
  --shadow-lg: none;
  --shadow-xl: none;
  --shadow-2xl: none;
  --tracking-normal: 0em;
  --spacing: 0.25rem;
  --shadow-color: oklch(0 0 0);
  --shadow-opacity: 0;
  --shadow-blur: 0px;
  --shadow-spread: 0px;
  --shadow-offset-x: 0;
  --shadow-offset-y: 1px;
  --letter-spacing: 0em;
}

.dark {
  --background: var(--siift-darker);
  --foreground: var(--siift-lightest);
  --card: var(--siift-dark-main);
  --card-foreground: var(--siift-lightest);
  --popover: var(--siift-dark-main);
  --popover-foreground: var(--siift-lightest);
  --primary: var(--siift-mid-accent);
  --primary-foreground: var(--siift-lightest);
  --secondary: var(--siift-dark-main);
  --secondary-foreground: var(--siift-lightest);
  --muted: var(--siift-darker);
  --muted-foreground: var(--siift-light-main);
  --accent: var(--siift-mid-accent);
  --accent-foreground: var(--siift-lightest);
  --destructive: var(--siift-error);
  --destructive-foreground: var(--siift-lightest);
  --border: var(--siift-darker);
  --input: var(--siift-darker);
  --ring: var(--siift-mid-accent);
  --chart-1: var(--siift-mid-accent);
  --chart-2: var(--siift-dark-accent);
  --chart-3: var(--siift-light-accent);
  --chart-4: var(--siift-light-main);
  --chart-5: var(--siift-light-mid);
  --sidebar: var(--siift-darker);
  --sidebar-foreground: var(--siift-lightest);
  --sidebar-primary: var(--siift-mid-accent);
  --sidebar-primary-foreground: var(--siift-lightest);
  --sidebar-accent: var(--siift-mid-accent);
  --sidebar-accent-foreground: var(--siift-lightest);
  --sidebar-border: var(--siift-darker);
  --sidebar-ring: var(--siift-mid-accent);
  --brand: 142 76% 46%;
  --brand-foreground: 142 76% 36%;
  --font-sans: var(--font-inter), Inter, ui-sans-serif, system-ui, -apple-system, Segoe UI, Roboto, Helvetica Neue, Arial, "Noto Sans", "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", sans-serif;
  --font-serif: Source Serif 4, serif;
  --font-mono: JetBrains Mono, monospace;
  --radius: 0.5rem;
  --shadow-2xs: none;
  --shadow-xs: none;
  --shadow-sm: none;
  --shadow: none;
  --shadow-md: none;
  --shadow-lg: none;
  --shadow-xl: none;
  --shadow-2xl: none;
  --shadow-color: oklch(0 0 0);
  --shadow-opacity: 0;
  --shadow-blur: 0px;
  --shadow-spread: 0px;
  --shadow-offset-x: 0;
  --shadow-offset-y: 1px;
  --letter-spacing: 0em;
  --spacing: 0.25rem;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    letter-spacing: var(--tracking-normal);
  }
}

@layer utilities {
  .delay-100 {
    animation-delay: 100ms;
  }
  .delay-300 {
    animation-delay: 300ms;
  }
  .delay-700 {
    animation-delay: 700ms;
  }
  .delay-1000 {
    animation-delay: 1000ms;
  }
  
  /* Hover эффект для группы */
  .group:hover .group-hover\:translate-y-\[-2rem\] {
    --tw-translate-y: -2rem;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
      skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
      scaleY(var(--tw-scale-y));
  }
  
  .group:hover .group-hover\:opacity-100 {
    opacity: 1;
  }
  
  .max-w-container {
    max-width: var(--max-width-container);
  }
}

@layer components {
  /* Improved checkbox visibility in dark mode */
  .dark [data-state="unchecked"] {
    @apply border-zinc-600 bg-zinc-800;
  }

  .dark [data-state="unchecked"]:hover {
    @apply border-zinc-500 bg-zinc-700;
  }

  .dark [data-state="checked"] {
    @apply border-primary bg-primary;
  }

  /* Better input visibility in dark mode */
  .dark input[type="checkbox"] {
    @apply border-zinc-600 bg-zinc-800;
  }

  .dark input[type="checkbox"]:checked {
    @apply border-primary bg-primary;
  }

  /* Improved form controls */
  .dark .peer:disabled ~ * {
    @apply opacity-50;
  }

  /* Better select and input borders */
  .dark select,
  .dark input:not([type="checkbox"]):not([type="radio"]) {
    @apply border-zinc-700 bg-zinc-900;
  }

  .dark select:focus,
  .dark input:not([type="checkbox"]):not([type="radio"]):focus {
    @apply border-primary ring-primary/20;
  }
}

/* Toast Styling */
[data-sonner-toast][data-type="success"] {
  background-color: var(--primary-light) !important;
  color: var(--siift-dark-accent) !important;
  border-color: var(--siift-mid-accent) !important;
}

[data-sonner-toast][data-type="error"] {
  background-color: var(--destructive) !important;
  color: var(--destructive-foreground) !important;
  border-color: var(--destructive) !important;
}

[data-sonner-toast][data-type="error"] [data-description] {
  color: var(--destructive-foreground) !important;
}

[data-sonner-toast][data-type="info"] {
  background-color: var(--card) !important;
  color: var(--card-foreground) !important;
  border-color: var(--border) !important;
}

[data-sonner-toast][data-type="warning"] {
  background-color: var(--siift-light-mid) !important;
  color: var(--siift-darkest) !important;
  border-color: var(--border) !important;
}

/* Dark mode toast styling */
.dark [data-sonner-toast][data-type="success"] {
  background-color: var(--siift-dark-accent) !important;
  color: var(--siift-lightest) !important;
  border-color: var(--siift-mid-accent) !important;
}

.dark [data-sonner-toast][data-type="error"] {
  background-color: var(--destructive) !important;
  color: var(--destructive-foreground) !important;
  border-color: var(--destructive) !important;
}

.dark [data-sonner-toast][data-type="error"] [data-description] {
  color: var(--destructive-foreground) !important;
}

.dark [data-sonner-toast][data-type="info"] {
  background-color: var(--card) !important;
  color: var(--card-foreground) !important;
  border-color: var(--border) !important;
}

.dark [data-sonner-toast][data-type="warning"] {
  background-color: var(--siift-dark-main) !important;
  color: var(--siift-lightest) !important;
  border-color: var(--border) !important;
}

/* Custom toast classes */
.toast-success {
  background-color: var(--primary-light) !important;
  color: var(--siift-dark-accent) !important;
  border-color: var(--siift-mid-accent) !important;
}

.toast-error {
  background-color: var(--destructive) !important;
  color: var(--destructive-foreground) !important;
  border-color: var(--destructive) !important;
}

.toast-info {
  background-color: var(--card) !important;
  color: var(--card-foreground) !important;
  border-color: var(--border) !important;
}

.toast-warning {
  background-color: var(--siift-light-mid) !important;
  color: var(--siift-darkest) !important;
  border-color: var(--border) !important;
}

/* Dark mode custom toast classes */
.dark .toast-success {
  background-color: var(--siift-dark-accent) !important;
  color: var(--siift-lightest) !important;
  border-color: var(--siift-mid-accent) !important;
}

.dark .toast-error {
  background-color: var(--destructive) !important;
  color: var(--destructive-foreground) !important;
  border-color: var(--destructive) !important;
}

.dark .toast-info {
  background-color: var(--card) !important;
  color: var(--card-foreground) !important;
  border-color: var(--border) !important;
}

.dark .toast-warning {
  background-color: var(--siift-dark-main) !important;
  color: var(--siift-lightest) !important;
  border-color: var(--border) !important;
}

/* Project main content dots pattern */
.project-main-content {
  background-image: radial-gradient(circle, rgb(209 213 219 / 0.5) 1.5px, transparent 1.5px);
  background-size: 20px 20px;
  mask-image:
    linear-gradient(to right, transparent 0%, black 15%, black 85%, transparent 100%),
    linear-gradient(to bottom, transparent 0%, black 15%, black 85%, transparent 100%);
  mask-composite: intersect;
  -webkit-mask-image:
    linear-gradient(to right, transparent 0%, black 15%, black 85%, transparent 100%),
    linear-gradient(to bottom, transparent 0%, black 15%, black 85%, transparent 100%);
  -webkit-mask-composite: source-in;
}

.dark .project-main-content {
  background-image: radial-gradient(circle, rgb(75 85 99 / 0.3) 1.5px, transparent 1.5px);
  background-size: 20px 20px;
  mask-image:
    linear-gradient(to right, transparent 0%, black 15%, black 85%, transparent 100%),
    linear-gradient(to bottom, transparent 0%, black 15%, black 85%, transparent 100%);
  mask-composite: intersect;
  -webkit-mask-image:
    linear-gradient(to right, transparent 0%, black 15%, black 85%, transparent 100%),
    linear-gradient(to bottom, transparent 0%, black 15%, black 85%, transparent 100%);
  -webkit-mask-composite: source-in;
}

/* Neutral gray variant for sections view */
.project-sections-background {
  background-image: radial-gradient(circle, color-mix(in oklch, var(--siift-light-main) 60%, transparent) 1.5px, transparent 1.5px);
  background-size: 20px 20px;
  mask-image:
    linear-gradient(to right, transparent 0%, black 15%, black 85%, transparent 100%),
    linear-gradient(to bottom, transparent 0%, black 15%, black 85%, transparent 100%);
  mask-composite: intersect;
  -webkit-mask-image:
    linear-gradient(to right, transparent 0%, black 15%, black 85%, transparent 100%),
    linear-gradient(to bottom, transparent 0%, black 15%, black 85%, transparent 100%);
  -webkit-mask-composite: source-in;
}

.dark .project-sections-background {
  background-image: radial-gradient(circle, color-mix(in oklch, var(--siift-darker) 40%, transparent) 1.5px, transparent 1.5px);
  background-size: 20px 20px;
  mask-image:
    linear-gradient(to right, transparent 0%, black 15%, black 85%, transparent 100%),
    linear-gradient(to bottom, transparent 0%, black 15%, black 85%, transparent 100%);
  mask-composite: intersect;
  -webkit-mask-image:
    linear-gradient(to right, transparent 0%, black 15%, black 85%, transparent 100%),
    linear-gradient(to bottom, transparent 0%, black 15%, black 85%, transparent 100%);
  -webkit-mask-composite: source-in;
}

/* Bottom fade effect for main content */
.main-content-bottom-fade {
  position: relative;
}

.main-content-bottom-fade::after {
  content: '';
  position: fixed;
  bottom: 0;
  left: var(--sidebar-width, 45vw);
  right: 0;
  height: 120px;
  background: linear-gradient(to bottom, transparent 0%, rgb(249 250 251) 100%);
  pointer-events: none;
  z-index: 50;
}

.dark .main-content-bottom-fade::after {
  background: linear-gradient(to bottom, transparent 0%, rgb(17 24 39) 100%);
}

/* Blog content styling */
.prose {
  color: hsl(var(--foreground));
  max-width: none;
}

.prose h1,
.prose h2,
.prose h3,
.prose h4,
.prose h5,
.prose h6 {
  color: hsl(var(--foreground));
  font-weight: 600;
  margin-top: 2rem;
  margin-bottom: 1rem;
}

.prose h1 {
  font-size: 2.25rem;
  line-height: 2.5rem;
}

.prose h2 {
  font-size: 1.875rem;
  line-height: 2.25rem;
}

.prose h3 {
  font-size: 1.5rem;
  line-height: 2rem;
}

.prose p {
  margin-bottom: 1.25rem;
  line-height: 1.75;
}

.prose ul,
.prose ol {
  margin-bottom: 1.25rem;
  padding-left: 1.5rem;
}

.prose li {
  margin-bottom: 0.5rem;
}

.prose strong {
  font-weight: 600;
  color: hsl(var(--foreground));
}

.prose code {
  background-color: hsl(var(--muted));
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
}

.prose blockquote {
  border-left: 4px solid hsl(var(--border));
  padding-left: 1rem;
  margin: 1.5rem 0;
  font-style: italic;
  color: hsl(var(--muted-foreground));
}

/* Line clamp utilities */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}