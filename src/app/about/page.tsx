import { generateSEOMetadata } from "@/components/seo/SEOHead";
import { StructuredData, structuredDataSchemas } from "@/components/seo/StructuredData";
import { Header } from "@/components/layout/header";
import { Footer } from "@/components/layout/footer";
import { WaitlistSection } from "@/components/ui/waitlist-section";
import { Linkedin } from "lucide-react";
import Link from "next/link";

export const metadata = generateSEOMetadata({
  title: "About - Our Mission, Vision & Values",
  description: "Learn about Siift's mission to empower everyday innovators to build businesses & financial freedom. Discover our vision, values, and team.",
  keywords: ["about siift", "mission", "vision", "values", "team", "empowerment", "business building", "financial freedom"],
  url: "/about",
  type: "website",
});

export default function AboutPage() {
  const teamMembers = [
    {
      name: "Samim",
      role: "strategy & product",
      image: "https://cdn.prod.website-files.com/681148b70a35c1dc56aabe23/684763d2bfd60add1d2d8635_Frame%***********%20(5).avif",
      linkedin: "https://www.linkedin.com/in/samiim"
    },
    {
      name: "Aref",
      role: "full-stack dev",
      image: "https://cdn.prod.website-files.com/681148b70a35c1dc56aabe23/6847656f8d2d8ff173e5fcef_Frame%***********%20(1).avif",
      linkedin: null
    },
    {
      name: "Carter",
      role: "sales & marketing",
      image: "https://cdn.prod.website-files.com/681148b70a35c1dc56aabe23/6866ba82533ac3676be90145_Frame%***********.avif",
      linkedin: null
    }
  ];

  // Organization structured data
  const organizationData = structuredDataSchemas.organization({
    name: "Siift",
    url: "https://siift.app",
    logo: "https://siift.app/images/logo.png",
    description: "Empowering everyday innovators to build businesses & financial freedom",
    socialMedia: [
      "https://www.linkedin.com/company/siiftai",
      "https://x.com/SiiftAi",
      "https://www.youtube.com/channel/UCcm9zM8lMavWGKBEDdupRiw"
    ],
  });

  return (
    <>
      <StructuredData data={organizationData} />
      <div className="min-h-screen flex flex-col bg-background">
        <Header />
        <main className="flex-1">
          <div className="container mx-auto px-4 py-16 max-w-4xl">
            {/* Hero Section */}
            <div className="text-center mb-16">
              <h1 className="text-4xl md:text-5xl font-bold text-foreground mb-6">
                About Siift
              </h1>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                Empowering everyday innovators to build businesses & financial freedom
              </p>
            </div>

            {/* Mission Section */}
            <section className="mb-16">
              <h2 className="text-3xl font-bold text-foreground mb-6">Our Mission</h2>
              <div className="prose prose-lg max-w-none">
                <p className="text-lg text-muted-foreground leading-relaxed mb-4">
                  Empowering everyday innovators to build businesses & financial freedom.
                </p>
                <p className="text-muted-foreground leading-relaxed">
                  This may seem overly simplistic or ambitious - but we see it as the most certain & important mission of our time. 
                  There are many headwinds in our modern society that prevent people from achieving financial or other forms of success... 
                  but new tech like AI & web 3.0 <em>can</em> actually help navigate us onto the path we want to live.
                </p>
              </div>
            </section>

            {/* Vision Section */}
            <section className="mb-16">
              <h2 className="text-3xl font-bold text-foreground mb-6">Our Vision</h2>
              <div className="prose prose-lg max-w-none">
                <p className="text-muted-foreground leading-relaxed mb-4">
                  We see a future where anyone can build a viable business, without an elite network, working to the bone, or doing questionable things.
                </p>
                <p className="text-muted-foreground leading-relaxed mb-4">
                  These future businesses will be highly-profitable thanks to tech, but not necessarily billion-dollar-unicorns. 
                  Through augmentation and global reach many will be able to generate serious revenue with small teams, that will more easily 
                  bring financial independence for its <em>whole</em> team.
                </p>
                <p className="text-muted-foreground leading-relaxed mb-4">
                  We're old enough to remember when the term "lifestyle business" was a dirty word, but as the dust of SaaS-era gluttony settles, 
                  we see that sustainable, attractive opportunities exist - they are not too small or too few & they are <em>much</em> more achievable, 
                  or even desirable, than the fabled VC-rocket ships of untold stress & exploitation.
                </p>
                <p className="text-muted-foreground leading-relaxed">
                  We see a future where, across all industries including tech, people buy local more and more - even if that locale is a community 
                  of shared values or identity rather than shared geography. This mosaic of countless, diverse organizations will be the decentralization 
                  of the economy, that will champion more equitable outcomes & socially responsible practices for our collective future.
                </p>
              </div>
            </section>

            {/* Values Section */}
            <section className="mb-16">
              <h2 className="text-3xl font-bold text-foreground mb-6">Values</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div className="text-center">
                  <h3 className="text-xl font-semibold text-foreground mb-3">Empowerment</h3>
                  <p className="text-muted-foreground">
                    Giving you the confidence and tools to own your journey
                  </p>
                </div>
                <div className="text-center">
                  <h3 className="text-xl font-semibold text-foreground mb-3">Resourcefulness</h3>
                  <p className="text-muted-foreground">
                    Helping you make the most of what you got
                  </p>
                </div>
                <div className="text-center">
                  <h3 className="text-xl font-semibold text-foreground mb-3">Clarity</h3>
                  <p className="text-muted-foreground">
                    Keeping it real to help you gain clarity in a growingly noisy world
                  </p>
                </div>
              </div>
            </section>

            {/* Team Section */}
            <section className="mb-16">
              <h2 className="text-3xl font-bold text-foreground mb-8">Team</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                {teamMembers.map((member) => (
                  <div key={member.name} className="text-center">
                    <div className="relative mb-4">
                      <img
                        src={member.image}
                        alt={member.name}
                        className="w-32 h-32 rounded-full mx-auto object-cover"
                      />
                      {member.linkedin && (
                        <Link
                          href={member.linkedin}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="absolute top-0 right-1/2 transform translate-x-1/2 -translate-y-2 bg-background border rounded-full p-2 hover:bg-accent transition-colors"
                        >
                          <Linkedin className="w-4 h-4" />
                        </Link>
                      )}
                    </div>
                    <h3 className="text-lg font-semibold text-foreground mb-1">{member.name}</h3>
                    <p className="text-sm text-muted-foreground">{member.role}</p>
                  </div>
                ))}
                
                {/* Join Us Card */}
                <div className="text-center">
                  <div className="w-32 h-32 rounded-full mx-auto bg-muted flex items-center justify-center mb-4">
                    <span className="text-4xl">?</span>
                  </div>
                  <h3 className="text-lg font-semibold text-foreground mb-1">You?</h3>
                  <p className="text-sm text-muted-foreground mb-2">reach out</p>
                  <Link
                    href="mailto:<EMAIL>"
                    className="text-sm text-[#166534] hover:underline"
                  >
                    <EMAIL>
                  </Link>
                </div>
              </div>
            </section>

            {/* Waitlist Section */}
            <WaitlistSection />
          </div>
        </main>
        <Footer />
      </div>
    </>
  );
}
