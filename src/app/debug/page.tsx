"use client";

import { PostHogDebug } from "@/components/debug/PostHogDebug";
import { ClerkTokenDebug } from "@/components/debug/ClerkTokenDebug";
import { ClerkTokenExample } from "@/components/debug/ClerkTokenExample";
import { AIStreamingDebug } from "@/components/debug/AIStreamingDebug";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";

export default function DebugPage() {
  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-6xl mx-auto space-y-8">
        <div className="text-center space-y-4">
          <h1 className="text-3xl font-bold">Debug Dashboard</h1>
          <p className="text-muted-foreground">
            Debug tools for testing various integrations and features
          </p>
        </div>

        <div className="space-y-8">
          <AIStreamingDebug />
          <ClerkTokenExample />
        </div>
      </div>
    </div>
  );
}
