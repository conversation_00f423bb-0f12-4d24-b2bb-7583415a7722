"use client";

// Prevent static generation for this page
export const dynamic = 'force-dynamic';

import { ClerkTokenDebug } from "@/components/debug/ClerkTokenDebug";
import { DashboardLayout } from "@/components/layout/dashboard-layout";

export default function TokenDebugPage() {
  return (
    <DashboardLayout>
      <div className="container mx-auto p-6 space-y-6">
        <div className="space-y-2">
          <h1 className="text-3xl font-bold">Clerk Token Debug</h1>
          <p className="text-muted-foreground">
            View and debug Clerk authentication tokens. Check the browser console for detailed information.
          </p>
        </div>
        
        <ClerkTokenDebug />
      </div>
    </DashboardLayout>
  );
}
