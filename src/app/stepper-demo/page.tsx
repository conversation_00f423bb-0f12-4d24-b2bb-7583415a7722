"use client";

import { Stepper, StepperConfig } from "@/components/ui/stepper";
import { useState } from "react";

export default function StepperDemoPage() {
  const [results, setResults] = useState<Record<string, any> | null>(null);

  const demoConfig: StepperConfig = {
    questions: [
      {
        id: "singleChoice",
        title: "What's your favorite programming language?",
        subtitle: "Single choice - auto-advances",
        type: "single",
        options: [
          { value: "javascript", label: "JavaScript" },
          { value: "typescript", label: "TypeScript" },
          { value: "python", label: "Python" },
          { value: "rust", label: "Rust" },
        ],
      },
      {
        id: "multipleChoice",
        title: "Which frameworks do you use?",
        subtitle: "Multiple choice - select all that apply",
        type: "multiple",
        options: [
          { value: "react", label: "React" },
          { value: "vue", label: "Vue.js" },
          { value: "angular", label: "Angular" },
          { value: "svelte", label: "Svelte" },
          { value: "nextjs", label: "Next.js" },
        ],
      },
      {
        id: "textInput",
        title: "What's your name?",
        subtitle: "Text input field",
        type: "text",
        placeholder: "Enter your full name...",
      },
      {
        id: "textareaInput",
        title: "Tell us about your project",
        subtitle: "Textarea for longer responses",
        type: "textarea",
        placeholder: "Describe your project in detail...",
      },
      {
        id: "booleanChoice",
        title: "Are you available for freelance work?",
        subtitle: "Yes/No question",
        type: "single",
        options: [
          { value: true, label: "Yes, I'm available" },
          { value: false, label: "No, not currently" },
        ],
      },
    ],
    onComplete: (answers: Record<string, any>) => {
      console.log("Stepper completed with answers:", answers);
      setResults(answers);
    },
    onStepChange: (currentStep: number, answers: Record<string, any>) => {
      console.log(`Step ${currentStep + 1} - Current answers:`, answers);
    },
  };

  if (results) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center p-4">
        <div className="max-w-2xl w-full">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-[#166534] mb-4">
              Stepper Demo Complete! 🎉
            </h1>
            <p className="text-muted-foreground">
              Here are the results from your stepper journey:
            </p>
          </div>
          
          <div className="bg-card border rounded-lg p-6 space-y-4">
            <h2 className="text-xl font-semibold mb-4">Your Answers:</h2>
            {Object.entries(results).map(([key, value]) => (
              <div key={key} className="border-b pb-3 last:border-b-0">
                <div className="font-medium text-sm text-muted-foreground uppercase tracking-wide">
                  {key}
                </div>
                <div className="mt-1">
                  {Array.isArray(value) ? (
                    <div className="flex flex-wrap gap-2">
                      {value.map((item, index) => (
                        <span
                          key={index}
                          className="bg-[#166534] text-white px-2 py-1 rounded text-sm"
                        >
                          {item}
                        </span>
                      ))}
                    </div>
                  ) : typeof value === "boolean" ? (
                    <span className={`px-2 py-1 rounded text-sm ${
                      value 
                        ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200" 
                        : "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
                    }`}>
                      {value ? "Yes" : "No"}
                    </span>
                  ) : (
                    <span className="text-foreground">{value}</span>
                  )}
                </div>
              </div>
            ))}
          </div>
          
          <div className="text-center mt-8">
            <button
              onClick={() => setResults(null)}
              className="bg-[#166534] text-white px-6 py-3 rounded-lg hover:bg-[#166534]/90 transition-colors"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    );
  }

  return <Stepper config={demoConfig} />;
}
