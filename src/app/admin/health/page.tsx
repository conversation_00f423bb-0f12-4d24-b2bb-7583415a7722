"use client";

// Prevent static generation for this page
export const dynamic = 'force-dynamic';

import { AdminLayout } from "@/components/layout/admin-layout";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useAuth } from "@/hooks/useAuth";
import { adminApi, initializeAdminApi } from "@/lib/admin-api";
import { SessionManager } from "@/lib/session";
import { AdminHealthCheck } from "@/lib/types";
import {
  Activity,
  AlertTriangle,
  CheckCircle,
  Database,
  Loader2,
  RefreshCw,
  Server,
  Settings,
  Shield,
  XCircle,
  Zap,
} from "lucide-react";
import { useEffect, useState } from "react";

export default function SystemHealthPage() {
  const [health, setHealth] = useState<AdminHealthCheck | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());
  const { user } = useAuth();

  const fetchHealthData = async (showRefreshing = false) => {
    try {
      if (showRefreshing) {
        setIsRefreshing(true);
      } else {
        setIsLoading(true);
      }
      setError(null);

      // Get the JWT token from SessionManager
      const token = SessionManager.getAccessToken();

      if (token) {
        // Initialize admin API with real token
        initializeAdminApi(token);

        try {
          // Try to fetch real data from API
          console.log("Fetching real health data from API...");
          const realHealth = await adminApi.getHealthCheck();
          console.log("Real health API data received:", realHealth);
          setHealth(realHealth);
          setLastUpdated(new Date());
          return;
        } catch (apiError: any) {
          console.log("Health API fetch failed:", apiError.message);
          setError(
            `API Error: ${apiError.message}. Please check if the backend is running.`
          );
        }
      } else {
        console.log("No admin token found");
        setError("No authentication token found. Please log in.");
      }
    } catch (err: any) {
      console.error("Failed to fetch health data:", err);
      setError(err.message || "Failed to fetch health data");
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  useEffect(() => {
    if (user?.role === "admin") {
      fetchHealthData();
    }
  }, [user]);

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case "healthy":
      case "connected":
      case "operational":
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case "warning":
      case "degraded":
        return <AlertTriangle className="h-5 w-5 text-yellow-600" />;
      case "error":
      case "disconnected":
      case "down":
        return <XCircle className="h-5 w-5 text-red-600" />;
      default:
        return <AlertTriangle className="h-5 w-5 text-gray-600" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status.toLowerCase()) {
      case "healthy":
      case "connected":
      case "operational":
        return (
          <Badge className="bg-green-100 text-green-800 hover:bg-green-100">
            {status}
          </Badge>
        );
      case "warning":
      case "degraded":
        return (
          <Badge className="bg-[var(--siift-light-mid)] text-[var(--siift-darkest)] hover:bg-[var(--siift-light-mid)]">
            {status}
          </Badge>
        );
      case "error":
      case "disconnected":
      case "down":
        return (
          <Badge className="bg-red-100 text-red-800 hover:bg-red-100">
            {status}
          </Badge>
        );
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const handleRunMigrations = async () => {
    try {
      setIsRefreshing(true);
      await adminApi.runMigrations();
      // Refresh health data after running migrations
      await fetchHealthData();
    } catch (err: any) {
      console.error("Failed to run migrations:", err);
      setError(err.message || "Failed to run migrations");
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleCleanupSchemas = async () => {
    try {
      setIsRefreshing(true);
      await adminApi.cleanupTenantSchemas();
      // Refresh health data after cleanup
      await fetchHealthData();
    } catch (err: any) {
      console.error("Failed to cleanup schemas:", err);
      setError(err.message || "Failed to cleanup schemas");
    } finally {
      setIsRefreshing(false);
    }
  };

  if (isLoading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p className="text-muted-foreground">Loading system health...</p>
          </div>
        </div>
      </AdminLayout>
    );
  }

  if (error && !health) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <p className="text-destructive mb-4">{error}</p>
            <button
              onClick={() => fetchHealthData()}
              className="text-primary hover:underline"
            >
              Try again
            </button>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">System Health</h1>
            <p className="text-muted-foreground">
              Monitor system status and perform maintenance tasks.
            </p>
          </div>
          <Button
            onClick={() => fetchHealthData(true)}
            disabled={isRefreshing}
            variant="outline"
          >
            {isRefreshing ? (
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
            ) : (
              <RefreshCw className="h-4 w-4 mr-2" />
            )}
            Refresh
          </Button>
        </div>

        {/* Overall Status */}
        {health && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                {getStatusIcon(health.status)}
                System Status
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div>
                  <div className="flex items-center gap-2 mb-2">
                    <span className="text-lg font-semibold">
                      Overall Status:
                    </span>
                    {getStatusBadge(health.status)}
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Last updated: {lastUpdated.toLocaleString()}
                  </p>
                </div>
                <Shield className="h-12 w-12 text-muted-foreground" />
              </div>
            </CardContent>
          </Card>
        )}

        {/* Service Status */}
        {health && (
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Database className="h-5 w-5" />
                  Database
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div>
                    <div className="flex items-center gap-2 mb-2">
                      {getStatusIcon(health.services.database)}
                      {getStatusBadge(health.services.database)}
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Database connection and performance
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5" />
                  Analytics
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div>
                    <div className="flex items-center gap-2 mb-2">
                      {getStatusIcon(health.services.analytics)}
                      {getStatusBadge(health.services.analytics)}
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Analytics service and data processing
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* System Metrics */}
        <div className="grid gap-4 md:grid-cols-3">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Uptime</CardTitle>
              <Server className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">99.8%</div>
              <p className="text-xs text-muted-foreground">Last 30 days</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Response Time
              </CardTitle>
              <Zap className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">245ms</div>
              <p className="text-xs text-muted-foreground">
                Average response time
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Error Rate</CardTitle>
              <AlertTriangle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">0.2%</div>
              <p className="text-xs text-muted-foreground">Last 24 hours</p>
            </CardContent>
          </Card>
        </div>

        {/* System Management */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              System Management
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <h3 className="font-medium">Run Database Migrations</h3>
                  <p className="text-sm text-muted-foreground">
                    Apply pending database schema changes
                  </p>
                </div>
                <Button
                  onClick={handleRunMigrations}
                  disabled={isRefreshing}
                  variant="outline"
                >
                  {isRefreshing ? (
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  ) : (
                    <Database className="h-4 w-4 mr-2" />
                  )}
                  Run Migrations
                </Button>
              </div>

              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <h3 className="font-medium">Cleanup Tenant Schemas</h3>
                  <p className="text-sm text-muted-foreground">
                    Remove unused tenant database schemas
                  </p>
                </div>
                <Button
                  onClick={handleCleanupSchemas}
                  disabled={isRefreshing}
                  variant="outline"
                >
                  {isRefreshing ? (
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  ) : (
                    <Settings className="h-4 w-4 mr-2" />
                  )}
                  Cleanup Schemas
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Error Display */}
        {error && (
          <Card className="border-destructive">
            <CardHeader>
              <CardTitle className="text-destructive">Error</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-destructive">{error}</p>
            </CardContent>
          </Card>
        )}
      </div>
    </AdminLayout>
  );
}
