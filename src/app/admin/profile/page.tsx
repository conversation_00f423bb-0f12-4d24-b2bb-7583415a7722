"use client";

// Prevent static generation for this page
export const dynamic = 'force-dynamic';

import { AdminLayout } from "@/components/layout/admin-layout";
import { AdminTabbedContent } from "@/components/admin-tabbed-content";

export default function AdminProfilePage() {
  return (
    <AdminLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold">Profile</h1>
          <p className="text-muted-foreground">
            Manage your admin profile and account settings.
          </p>
        </div>
        <AdminTabbedContent activeTab="profile" />
      </div>
    </AdminLayout>
  );
}
