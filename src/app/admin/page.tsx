"use client";

// Prevent static generation for this page
export const dynamic = 'force-dynamic';

import { AdminRoute } from "@/components/auth/admin-route";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

export default function AdminDashboardPage() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to analytics summary as the main admin page
    router.replace("/admin/analytics/summary");
  }, [router]);

  return (
    <AdminRoute>
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <p className="text-muted-foreground">
            Redirecting to admin dashboard...
          </p>
        </div>
      </div>
    </AdminRoute>
  );
}
