"use client";

// Prevent static generation for this page
export const dynamic = 'force-dynamic';

import { AdminLayout } from "@/components/layout/admin-layout";
import { AdminTabbedContent } from "@/components/admin-tabbed-content";

export default function AdminSettingsTabPage() {
  return (
    <AdminLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold">Settings</h1>
          <p className="text-muted-foreground">
            Configure system settings and preferences.
          </p>
        </div>
        <AdminTabbedContent activeTab="settings" />
      </div>
    </AdminLayout>
  );
}
