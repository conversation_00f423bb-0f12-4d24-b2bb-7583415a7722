"use client";

// Prevent static generation for this page
export const dynamic = 'force-dynamic';

import { AdminLayout } from "@/components/layout/admin-layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useAuth } from "@/hooks/useAuth";
import { adminApi, initializeAdminApi } from "@/lib/admin-api";
import { SessionManager } from "@/lib/session";
import { ActivityMetrics, ActivityTrends } from "@/lib/types";
import {
  Activity,
  BarChart3,
  Calendar,
  Loader2,
  TrendingDown,
  TrendingUp,
  Users,
} from "lucide-react";
import { useEffect, useState } from "react";
import {
  Area,
  AreaChart,
  Bar,
  BarChart,
  CartesianGrid,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts";

export default function ActivityMetricsPage() {
  const [metrics, setMetrics] = useState<ActivityMetrics | null>(null);
  const [trends, setTrends] = useState<ActivityTrends | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [granularity, setGranularity] = useState<
    "day" | "week" | "month" | "year"
  >("day");
  const { user } = useAuth();

  useEffect(() => {
    const fetchActivityData = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Get the JWT token from SessionManager
        const token = SessionManager.getAccessToken();

        if (token) {
          // Initialize admin API with real token
          initializeAdminApi(token);

          try {
            // Try to fetch real data from API
            console.log("Fetching real activity data from API...");
            const realMetrics = await adminApi.getActivityMetrics({
              granularity,
            });
            const realTrends = await adminApi.getActivityTrends({
              granularity,
            });
            console.log("Real activity API data received:", {
              realMetrics,
              realTrends,
            });
            setMetrics(realMetrics);
            setTrends(realTrends);
            return;
          } catch (apiError: unknown) {
            const errorMessage =
              apiError instanceof Error ? apiError.message : "Unknown error";
            console.log("Activity API fetch failed:", errorMessage);
            setError(
              `API Error: ${errorMessage}. Please check if the backend is running.`
            );
          }
        } else {
          console.log("No admin token found");
          setError("No authentication token found. Please log in.");
        }
      } catch (err: unknown) {
        const errorMessage =
          err instanceof Error ? err.message : "Unknown error";
        console.error("Failed to fetch activity data:", err);
        setError(errorMessage || "Failed to fetch activity data");
      } finally {
        setIsLoading(false);
      }
    };

    if (user?.role === "admin") {
      fetchActivityData();
    }
  }, [user, granularity]);

  const formatChange = (change: number) => {
    const isPositive = change > 0;
    const Icon = isPositive ? TrendingUp : TrendingDown;
    const color = isPositive ? "text-green-600" : "text-red-600";

    return (
      <div className={`flex items-center gap-1 ${color}`}>
        <Icon className="h-3 w-3" />
        <span className="text-xs">{Math.abs(change).toFixed(1)}%</span>
      </div>
    );
  };

  if (isLoading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p className="text-muted-foreground">Loading activity metrics...</p>
          </div>
        </div>
      </AdminLayout>
    );
  }

  if (error) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <p className="text-destructive mb-4">{error}</p>
            <button
              onClick={() => window.location.reload()}
              className="text-primary hover:underline"
            >
              Try again
            </button>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Activity Metrics</h1>
            <p className="text-muted-foreground">
              Track user engagement and activity patterns.
            </p>
          </div>
          <Select
            value={granularity}
            onValueChange={(value: "day" | "week" | "month" | "year") =>
              setGranularity(value)
            }
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select granularity" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="day">Daily</SelectItem>
              <SelectItem value="week">Weekly</SelectItem>
              <SelectItem value="month">Monthly</SelectItem>
              <SelectItem value="year">Yearly</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Activity Metrics Cards */}
        {metrics && (
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Daily Active Users
                </CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{metrics.dau}</div>
                {formatChange(metrics.dauChange)}
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Weekly Active Users
                </CardTitle>
                <Calendar className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{metrics.wau}</div>
                {formatChange(metrics.wauChange)}
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Monthly Active Users
                </CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{metrics.mau}</div>
                {formatChange(metrics.mauChange)}
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Yearly Active Users
                </CardTitle>
                <BarChart3 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{metrics.yau}</div>
                {formatChange(metrics.yauChange)}
              </CardContent>
            </Card>
          </div>
        )}

        {/* Charts */}
        {trends && (
          <div className="grid gap-6 md:grid-cols-2">
            {/* Active Users Trend */}
            <Card>
              <CardHeader>
                <CardTitle>Active Users Trend</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={trends.data}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis
                      dataKey="date"
                      tickFormatter={(value) =>
                        new Date(value).toLocaleDateString()
                      }
                    />
                    <YAxis />
                    <Tooltip
                      labelFormatter={(value) =>
                        new Date(value).toLocaleDateString()
                      }
                    />
                    <Line
                      type="monotone"
                      dataKey="activeUsers"
                      stroke="#8884d8"
                      strokeWidth={2}
                      name="Active Users"
                    />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* New Users Trend */}
            <Card>
              <CardHeader>
                <CardTitle>New Users Trend</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={trends.data}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis
                      dataKey="date"
                      tickFormatter={(value) =>
                        new Date(value).toLocaleDateString()
                      }
                    />
                    <YAxis />
                    <Tooltip
                      labelFormatter={(value) =>
                        new Date(value).toLocaleDateString()
                      }
                    />
                    <Area
                      type="monotone"
                      dataKey="newUsers"
                      stroke="#82ca9d"
                      fill="#82ca9d"
                      name="New Users"
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Requests Trend */}
            <Card>
              <CardHeader>
                <CardTitle>API Requests Trend</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={trends.data}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis
                      dataKey="date"
                      tickFormatter={(value) =>
                        new Date(value).toLocaleDateString()
                      }
                    />
                    <YAxis />
                    <Tooltip
                      labelFormatter={(value) =>
                        new Date(value).toLocaleDateString()
                      }
                    />
                    <Bar
                      dataKey="totalRequests"
                      fill="#ffc658"
                      name="Total Requests"
                    />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Session Time Trend */}
            <Card>
              <CardHeader>
                <CardTitle>Average Session Time</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={trends.data}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis
                      dataKey="date"
                      tickFormatter={(value) =>
                        new Date(value).toLocaleDateString()
                      }
                    />
                    <YAxis />
                    <Tooltip
                      labelFormatter={(value) =>
                        new Date(value).toLocaleDateString()
                      }
                      formatter={(value: number) => [
                        `${value}m`,
                        "Session Time",
                      ]}
                    />
                    <Line
                      type="monotone"
                      dataKey="averageSessionTime"
                      stroke="#ff7300"
                      strokeWidth={2}
                      name="Avg Session Time (min)"
                    />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Summary Stats */}
        {trends && (
          <Card>
            <CardHeader>
              <CardTitle>Summary Statistics</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <div className="text-center">
                  <div className="text-2xl font-bold">
                    {trends.summary.totalDays}
                  </div>
                  <p className="text-sm text-muted-foreground">Days Analyzed</p>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">
                    {trends.summary.averageActiveUsers}
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Avg Active Users
                  </p>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">
                    {trends.summary.totalNewUsers}
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Total New Users
                  </p>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">
                    {trends.summary.growthRate}%
                  </div>
                  <p className="text-sm text-muted-foreground">Growth Rate</p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </AdminLayout>
  );
}
