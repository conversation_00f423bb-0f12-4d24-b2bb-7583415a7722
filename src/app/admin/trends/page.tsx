"use client";

// Prevent static generation for this page
export const dynamic = 'force-dynamic';

import { AdminLayout } from "@/components/layout/admin-layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useAuth } from "@/hooks/useAuth";
import { initializeAdminApi } from "@/lib/admin-api";
import { ActivityTrends } from "@/lib/types";
import { Activity, Loader2, TrendingUp, UserPlus, Users } from "lucide-react";
import { useEffect, useState } from "react";
import {
  Area,
  AreaChart,
  Bar,
  CartesianGrid,
  ComposedChart,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts";

export default function TrendsPage() {
  const [trends, setTrends] = useState<ActivityTrends | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [granularity, setGranularity] = useState<
    "day" | "week" | "month" | "year"
  >("day");
  const { user } = useAuth();

  useEffect(() => {
    const fetchTrends = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Initialize admin API with mock token
        initializeAdminApi("mock-admin-token");

        // Mock trends data with more data points
        const mockTrends: ActivityTrends = {
          data: [
            {
              date: "2024-01-10",
              activeUsers: 98,
              newUsers: 5,
              totalRequests: 1850,
              averageSessionTime: 14.2,
            },
            {
              date: "2024-01-11",
              activeUsers: 105,
              newUsers: 7,
              totalRequests: 1950,
              averageSessionTime: 15.1,
            },
            {
              date: "2024-01-12",
              activeUsers: 112,
              newUsers: 9,
              totalRequests: 2050,
              averageSessionTime: 15.8,
            },
            {
              date: "2024-01-13",
              activeUsers: 120,
              newUsers: 8,
              totalRequests: 2100,
              averageSessionTime: 16.2,
            },
            {
              date: "2024-01-14",
              activeUsers: 128,
              newUsers: 11,
              totalRequests: 2200,
              averageSessionTime: 16.8,
            },
            {
              date: "2024-01-15",
              activeUsers: 135,
              newUsers: 12,
              totalRequests: 2350,
              averageSessionTime: 17.1,
            },
            {
              date: "2024-01-16",
              activeUsers: 142,
              newUsers: 15,
              totalRequests: 2480,
              averageSessionTime: 18.3,
            },
            {
              date: "2024-01-17",
              activeUsers: 158,
              newUsers: 10,
              totalRequests: 2650,
              averageSessionTime: 19.1,
            },
            {
              date: "2024-01-18",
              activeUsers: 165,
              newUsers: 8,
              totalRequests: 2680,
              averageSessionTime: 19.1,
            },
            {
              date: "2024-01-19",
              activeUsers: 150,
              newUsers: 12,
              totalRequests: 2450,
              averageSessionTime: 18.3,
            },
            {
              date: "2024-01-20",
              activeUsers: 172,
              newUsers: 14,
              totalRequests: 2800,
              averageSessionTime: 20.1,
            },
          ],
          summary: {
            totalDays: 11,
            averageActiveUsers: 140,
            totalNewUsers: 111,
            growthRate: 12.8,
          },
        };

        setTrends(mockTrends);
      } catch (err: any) {
        console.error("Failed to fetch trends:", err);
        setError(err.message || "Failed to fetch trends data");
      } finally {
        setIsLoading(false);
      }
    };

    if (user?.role === "admin") {
      fetchTrends();
    }
  }, [user, granularity]);

  if (isLoading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p className="text-muted-foreground">Loading trends data...</p>
          </div>
        </div>
      </AdminLayout>
    );
  }

  if (error) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <p className="text-destructive mb-4">{error}</p>
            <button
              onClick={() => window.location.reload()}
              className="text-primary hover:underline"
            >
              Try again
            </button>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Growth Trends</h1>
            <p className="text-muted-foreground">
              Analyze growth patterns and user engagement trends over time.
            </p>
          </div>
          <Select
            value={granularity}
            onValueChange={(value: any) => setGranularity(value)}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select granularity" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="day">Daily</SelectItem>
              <SelectItem value="week">Weekly</SelectItem>
              <SelectItem value="month">Monthly</SelectItem>
              <SelectItem value="year">Yearly</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Summary Cards */}
        {trends && (
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Growth Rate
                </CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">
                  +{trends.summary.growthRate}%
                </div>
                <p className="text-xs text-muted-foreground">
                  Over {trends.summary.totalDays} days
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Avg Active Users
                </CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {trends.summary.averageActiveUsers}
                </div>
                <p className="text-xs text-muted-foreground">Daily average</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Total New Users
                </CardTitle>
                <UserPlus className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {trends.summary.totalNewUsers}
                </div>
                <p className="text-xs text-muted-foreground">
                  In {trends.summary.totalDays} days
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Peak Active Users
                </CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {Math.max(...trends.data.map((d) => d.activeUsers))}
                </div>
                <p className="text-xs text-muted-foreground">
                  Highest single day
                </p>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Main Trends Chart */}
        {trends && (
          <Card>
            <CardHeader>
              <CardTitle>User Growth Trends</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <ComposedChart data={trends.data}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis
                    dataKey="date"
                    tickFormatter={(value) =>
                      new Date(value).toLocaleDateString()
                    }
                  />
                  <YAxis yAxisId="left" />
                  <YAxis yAxisId="right" orientation="right" />
                  <Tooltip
                    labelFormatter={(value) =>
                      new Date(value).toLocaleDateString()
                    }
                  />
                  <Area
                    yAxisId="left"
                    type="monotone"
                    dataKey="activeUsers"
                    fill="#8884d8"
                    stroke="#8884d8"
                    fillOpacity={0.3}
                    name="Active Users"
                  />
                  <Bar
                    yAxisId="right"
                    dataKey="newUsers"
                    fill="#82ca9d"
                    name="New Users"
                  />
                </ComposedChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        )}

        {/* Additional Charts */}
        {trends && (
          <div className="grid gap-6 md:grid-cols-2">
            {/* Session Time Trend */}
            <Card>
              <CardHeader>
                <CardTitle>Session Time Trend</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={trends.data}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis
                      dataKey="date"
                      tickFormatter={(value) =>
                        new Date(value).toLocaleDateString()
                      }
                    />
                    <YAxis />
                    <Tooltip
                      labelFormatter={(value) =>
                        new Date(value).toLocaleDateString()
                      }
                      formatter={(value: any) => [`${value}m`, "Session Time"]}
                    />
                    <Line
                      type="monotone"
                      dataKey="averageSessionTime"
                      stroke="#ff7300"
                      strokeWidth={3}
                      name="Avg Session Time (min)"
                    />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* API Usage Trend */}
            <Card>
              <CardHeader>
                <CardTitle>API Usage Trend</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={trends.data}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis
                      dataKey="date"
                      tickFormatter={(value) =>
                        new Date(value).toLocaleDateString()
                      }
                    />
                    <YAxis />
                    <Tooltip
                      labelFormatter={(value) =>
                        new Date(value).toLocaleDateString()
                      }
                    />
                    <Area
                      type="monotone"
                      dataKey="totalRequests"
                      stroke="#ffc658"
                      fill="#ffc658"
                      fillOpacity={0.6}
                      name="Total Requests"
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </AdminLayout>
  );
}
