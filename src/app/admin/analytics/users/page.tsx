"use client";

// Prevent static generation for this page
export const dynamic = 'force-dynamic';

import { useState, useEffect } from "react";
import { AdminLayout } from "@/components/layout/admin-layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { adminApi, initializeAdminApi } from "@/lib/admin-api";
import {
  Loader2,
  RefreshCw,
  Users,
  Search,
  Filter,
  AlertCircle,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";

export default function UserAnalyticsPage() {
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const [filters, setFilters] = useState({
    page: 1,
    limit: 10,
    search: "",
    role: "all",
    hasSubmittedFeedback: "all",
  });

  const fetchData = async () => {
    setLoading(true);
    setError(null);

    try {
      const token =
        localStorage.getItem("siift_access_token") ||
        sessionStorage.getItem("siift_access_token");

      if (!token) {
        throw new Error("No admin token found. Please login first.");
      }

      initializeAdminApi(token);

      const filterParams: any = {
        page: filters.page,
        limit: filters.limit,
      };

      if (filters.search) filterParams.search = filters.search;
      if (filters.role && filters.role !== "all")
        filterParams.role = filters.role;
      if (
        filters.hasSubmittedFeedback &&
        filters.hasSubmittedFeedback !== "all"
      ) {
        filterParams.hasSubmittedFeedback =
          filters.hasSubmittedFeedback === "true";
      }

      const result = await adminApi.getUserAnalytics(filterParams);
      setData(result);
      setLastUpdated(new Date());
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [filters]);

  const formatDuration = (seconds: number) => {
    if (seconds < 60) return `${seconds.toFixed(1)}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds.toFixed(0)}s`;
  };

  const handleSearch = (value: string) => {
    setFilters((prev) => ({ ...prev, search: value, page: 1 }));
  };

  const handleRoleFilter = (value: string) => {
    setFilters((prev) => ({ ...prev, role: value, page: 1 }));
  };

  const handleFeedbackFilter = (value: string) => {
    setFilters((prev) => ({ ...prev, hasSubmittedFeedback: value, page: 1 }));
  };

  const handlePageChange = (newPage: number) => {
    setFilters((prev) => ({ ...prev, page: newPage }));
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">User Analytics</h1>
            <p className="text-muted-foreground">
              Detailed analytics and insights about platform users.
            </p>
          </div>
          <div className="flex items-center space-x-4">
            {lastUpdated && (
              <span className="text-sm text-muted-foreground">
                Last updated: {lastUpdated.toLocaleTimeString()}
              </span>
            )}
            <Button onClick={fetchData} disabled={loading} variant="outline">
              {loading ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <RefreshCw className="h-4 w-4 mr-2" />
              )}
              Refresh
            </Button>
          </div>
        </div>

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Filter className="h-5 w-5" />
              <span>Filters</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search users..."
                  value={filters.search}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Select value={filters.role} onValueChange={handleRoleFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Filter by role" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Roles</SelectItem>
                  <SelectItem value="user">User</SelectItem>
                  <SelectItem value="admin">Admin</SelectItem>
                  <SelectItem value="moderator">Moderator</SelectItem>
                </SelectContent>
              </Select>
              <Select
                value={filters.hasSubmittedFeedback}
                onValueChange={handleFeedbackFilter}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Filter by feedback" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Users</SelectItem>
                  <SelectItem value="true">Has Feedback</SelectItem>
                  <SelectItem value="false">No Feedback</SelectItem>
                </SelectContent>
              </Select>
              <div className="flex items-center space-x-2">
                <span className="text-sm text-muted-foreground">Per page:</span>
                <Select
                  value={filters.limit.toString()}
                  onValueChange={(value) =>
                    setFilters((prev) => ({
                      ...prev,
                      limit: parseInt(value),
                      page: 1,
                    }))
                  }
                >
                  <SelectTrigger className="w-20">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="5">5</SelectItem>
                    <SelectItem value="10">10</SelectItem>
                    <SelectItem value="25">25</SelectItem>
                    <SelectItem value="50">50</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Error State */}
        {error && (
          <Card className="border-red-200 bg-red-50">
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <AlertCircle className="h-5 w-5 text-red-600" />
                <p className="text-red-800">{error}</p>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Loading State */}
        {loading && !data && (
          <div className="flex items-center justify-center py-12">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        )}

        {/* User List */}
        {data && (
          <>
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Users className="h-5 w-5" />
                    <span>Users ({data.pagination?.total || 0})</span>
                  </div>
                  <Badge variant="outline">
                    Page {data.pagination?.page || 1} of{" "}
                    {data.pagination?.totalPages || 1}
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {data.data?.map((user: any, index: number) => (
                    <Card key={index} className="border-l-4 border-l-blue-500">
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div className="space-y-1">
                            <div className="flex items-center space-x-2">
                              <h3 className="font-semibold">
                                {user.firstName} {user.lastName}
                              </h3>
                              <Badge variant="outline">{user.role}</Badge>
                            </div>
                            <p className="text-sm text-muted-foreground">
                              {user.email}
                            </p>
                            <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                              <span>
                                Joined:{" "}
                                {new Date(user.signupDate).toLocaleDateString()}
                              </span>
                              <span>
                                Last Sign In:{" "}
                                {new Date(user.lastSignIn).toLocaleDateString()}
                              </span>
                              <span>Referral: {user.referralCode}</span>
                            </div>
                          </div>
                          <div className="text-right space-y-1">
                            <div className="flex items-center space-x-2">
                              <span className="text-sm font-medium">
                                {user.actionCount} actions
                              </span>
                              <Badge
                                variant={
                                  user.hasSubmittedFeedback
                                    ? "default"
                                    : "secondary"
                                }
                              >
                                {user.hasSubmittedFeedback
                                  ? "Has Feedback"
                                  : "No Feedback"}
                              </Badge>
                            </div>
                            <div className="text-xs text-muted-foreground">
                              <div>Sessions: {user.totalSessions}</div>
                              <div>
                                Avg Session:{" "}
                                {formatDuration(user.averageSessionTime)}
                              </div>
                            </div>
                            {user.feedbackSummary && (
                              <div className="text-xs text-muted-foreground max-w-xs truncate">
                                "{user.feedbackSummary}"
                              </div>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>

                {/* Pagination */}
                {data.pagination && data.pagination.totalPages > 1 && (
                  <div className="flex items-center justify-between mt-6">
                    <Button
                      variant="outline"
                      onClick={() => handlePageChange(data.pagination.page - 1)}
                      disabled={!data.pagination.hasPrev}
                    >
                      <ChevronLeft className="h-4 w-4 mr-2" />
                      Previous
                    </Button>
                    <span className="text-sm text-muted-foreground">
                      Showing{" "}
                      {(data.pagination.page - 1) * data.pagination.limit + 1}{" "}
                      to{" "}
                      {Math.min(
                        data.pagination.page * data.pagination.limit,
                        data.pagination.total
                      )}{" "}
                      of {data.pagination.total} users
                    </span>
                    <Button
                      variant="outline"
                      onClick={() => handlePageChange(data.pagination.page + 1)}
                      disabled={!data.pagination.hasNext}
                    >
                      Next
                      <ChevronRight className="h-4 w-4 ml-2" />
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </>
        )}
      </div>
    </AdminLayout>
  );
}
