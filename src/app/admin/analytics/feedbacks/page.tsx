"use client";

// Prevent static generation for this page
export const dynamic = 'force-dynamic';

import { AdminLayout } from "@/components/layout/admin-layout";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { adminApi, initializeAdminApi } from "@/lib/admin-api";
import {
  AlertCircle,
  ArrowDown,
  ArrowUp,
  ArrowUpDown,
  ChevronLeft,
  ChevronRight,
  Clock,
  Filter,
  Loader2,
  MessageCircle,
  MessageSquare,
  RefreshCw,
  Search,
  Star,
  User,
} from "lucide-react";
import { useEffect, useState } from "react";

export default function FeedbackPage() {
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const [filters, setFilters] = useState({
    page: 1,
    limit: 10,
    search: "",
    isHighPriority: "all",
    minSubmissionCount: "",
    startDate: "",
    endDate: "",
    sortBy: "createdAt",
    sortOrder: "DESC" as "ASC" | "DESC",
  });

  const fetchData = async () => {
    setLoading(true);
    setError(null);

    try {
      const token =
        localStorage.getItem("siift_access_token") ||
        sessionStorage.getItem("siift_access_token");

      if (!token) {
        throw new Error("No admin token found. Please login first.");
      }

      initializeAdminApi(token);

      const filterParams: any = {
        page: filters.page,
        limit: filters.limit,
        sortBy: filters.sortBy,
        sortOrder: filters.sortOrder,
      };

      if (filters.search) filterParams.search = filters.search;
      if (filters.isHighPriority && filters.isHighPriority !== "all") {
        filterParams.isHighPriority = filters.isHighPriority === "true";
      }
      if (filters.minSubmissionCount) {
        filterParams.minSubmissionCount = parseInt(filters.minSubmissionCount);
      }
      if (filters.startDate) filterParams.startDate = filters.startDate;
      if (filters.endDate) filterParams.endDate = filters.endDate;

      const result = await adminApi.getFeedback(filterParams);
      setData(result);
      setLastUpdated(new Date());
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [filters]);

  const handleFilterChange = (key: string, value: string) => {
    setFilters((prev) => ({ ...prev, [key]: value, page: 1 }));
  };

  const handlePageChange = (newPage: number) => {
    setFilters((prev) => ({ ...prev, page: newPage }));
  };

  const handleSort = (field: string) => {
    setFilters((prev) => ({
      ...prev,
      sortBy: field as any,
      sortOrder:
        prev.sortBy === field && prev.sortOrder === "ASC" ? "DESC" : "ASC",
      page: 1,
    }));
  };

  const getSortIcon = (field: string) => {
    if (filters.sortBy !== field) return <ArrowUpDown className="h-4 w-4" />;
    return filters.sortOrder === "ASC" ? (
      <ArrowUp className="h-4 w-4" />
    ) : (
      <ArrowDown className="h-4 w-4" />
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const truncateText = (text: string, maxLength: number = 100) => {
    if (!text) return "";
    return text.length > maxLength
      ? text.substring(0, maxLength) + "..."
      : text;
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">User Feedback</h1>
            <p className="text-muted-foreground">
              Monitor and analyze user feedback submissions with advanced
              filtering.
            </p>
          </div>
          <div className="flex items-center space-x-4">
            {lastUpdated && (
              <span className="text-sm text-muted-foreground">
                Last updated: {lastUpdated.toLocaleTimeString()}
              </span>
            )}
            <Button onClick={fetchData} disabled={loading} variant="outline">
              {loading ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <RefreshCw className="h-4 w-4 mr-2" />
              )}
              Refresh
            </Button>
          </div>
        </div>

        {/* Summary Stats */}
        {data && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <MessageCircle className="h-5 w-5 text-blue-600" />
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      Total Feedback
                    </p>
                    <p className="text-2xl font-bold">{data.total}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <Star className="h-5 w-5 text-yellow-600" />
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      High Priority
                    </p>
                    <p className="text-2xl font-bold">
                      {data.feedback?.filter((f: any) => f.isHighPriority)
                        .length || 0}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <User className="h-5 w-5 text-green-600" />
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      Unique Users
                    </p>
                    <p className="text-2xl font-bold">
                      {new Set(data.feedback?.map((f: any) => f.userId)).size ||
                        0}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <MessageSquare className="h-5 w-5 text-purple-600" />
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      Avg Submissions
                    </p>
                    <p className="text-2xl font-bold">
                      {data.feedback?.length > 0
                        ? (
                            data.feedback.reduce(
                              (sum: number, f: any) => sum + f.submissionCount,
                              0
                            ) / data.feedback.length
                          ).toFixed(1)
                        : "0"}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Filter className="h-5 w-5" />
              <span>Filters</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search feedback..."
                  value={filters.search}
                  onChange={(e) => handleFilterChange("search", e.target.value)}
                  className="pl-10"
                />
              </div>

              <Select
                value={filters.isHighPriority}
                onValueChange={(value) =>
                  handleFilterChange("isHighPriority", value)
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Priority" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Priority</SelectItem>
                  <SelectItem value="true">High Priority</SelectItem>
                  <SelectItem value="false">Normal Priority</SelectItem>
                </SelectContent>
              </Select>

              <Input
                type="number"
                placeholder="Min submissions"
                value={filters.minSubmissionCount}
                onChange={(e) =>
                  handleFilterChange("minSubmissionCount", e.target.value)
                }
              />

              <Input
                type="date"
                placeholder="Start date"
                value={filters.startDate}
                onChange={(e) =>
                  handleFilterChange("startDate", e.target.value)
                }
              />

              <Input
                type="date"
                placeholder="End date"
                value={filters.endDate}
                onChange={(e) => handleFilterChange("endDate", e.target.value)}
              />

              <Select
                value={filters.limit.toString()}
                onValueChange={(value) =>
                  setFilters((prev) => ({
                    ...prev,
                    limit: parseInt(value),
                    page: 1,
                  }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="5">5 per page</SelectItem>
                  <SelectItem value="10">10 per page</SelectItem>
                  <SelectItem value="25">25 per page</SelectItem>
                  <SelectItem value="50">50 per page</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Error State */}
        {error && (
          <Card className="border-red-200 bg-red-50">
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <AlertCircle className="h-5 w-5 text-red-600" />
                <p className="text-red-800">{error}</p>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Loading State */}
        {loading && !data && (
          <div className="flex items-center justify-center py-12">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        )}

        {/* Feedback Table */}
        {data && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <MessageCircle className="h-5 w-5" />
                  <span>Feedback Submissions ({data.total})</span>
                </div>
                <Badge variant="outline">
                  Page {data.page} of {data.totalPages}
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left p-3">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleSort("userEmail")}
                          className="h-auto p-0 font-semibold"
                        >
                          User {getSortIcon("userEmail")}
                        </Button>
                      </th>
                      <th className="text-left p-3">Feedback Content</th>
                      <th className="text-center p-3">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleSort("submissionCount")}
                          className="h-auto p-0 font-semibold"
                        >
                          Submissions {getSortIcon("submissionCount")}
                        </Button>
                      </th>
                      <th className="text-center p-3">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleSort("isHighPriority")}
                          className="h-auto p-0 font-semibold"
                        >
                          Priority {getSortIcon("isHighPriority")}
                        </Button>
                      </th>
                      <th className="text-right p-3">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleSort("createdAt")}
                          className="h-auto p-0 font-semibold"
                        >
                          Date {getSortIcon("createdAt")}
                        </Button>
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {data.feedback?.map((feedback: any, index: number) => (
                      <tr key={index} className="border-b hover:bg-muted/50">
                        <td className="p-3">
                          <div className="space-y-1">
                            <div className="font-medium">
                              {feedback.userFirstName} {feedback.userLastName}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {feedback.userEmail}
                            </div>
                          </div>
                        </td>
                        <td className="p-3 max-w-md">
                          <div className="space-y-2">
                            {feedback.bugsAndIssues && (
                              <div>
                                <span className="text-xs font-medium text-red-600">
                                  Bugs:{" "}
                                </span>
                                <span className="text-xs">
                                  {truncateText(feedback.bugsAndIssues)}
                                </span>
                              </div>
                            )}
                            {feedback.improvementSuggestions && (
                              <div>
                                <span className="text-xs font-medium text-blue-600">
                                  Improvements:{" "}
                                </span>
                                <span className="text-xs">
                                  {truncateText(
                                    feedback.improvementSuggestions
                                  )}
                                </span>
                              </div>
                            )}
                            {feedback.premiumFeatures && (
                              <div>
                                <span className="text-xs font-medium text-purple-600">
                                  Premium:{" "}
                                </span>
                                <span className="text-xs">
                                  {truncateText(feedback.premiumFeatures)}
                                </span>
                              </div>
                            )}
                            {feedback.additionalComments && (
                              <div>
                                <span className="text-xs font-medium text-green-600">
                                  Comments:{" "}
                                </span>
                                <span className="text-xs">
                                  {truncateText(feedback.additionalComments)}
                                </span>
                              </div>
                            )}
                          </div>
                        </td>
                        <td className="text-center p-3">
                          <Badge variant="outline">
                            {feedback.submissionCount}
                          </Badge>
                        </td>
                        <td className="text-center p-3">
                          <Badge
                            variant={
                              feedback.isHighPriority
                                ? "destructive"
                                : "secondary"
                            }
                          >
                            {feedback.isHighPriority ? "High" : "Normal"}
                          </Badge>
                        </td>
                        <td className="text-right p-3">
                          <div className="flex items-center justify-end space-x-1">
                            <Clock className="h-3 w-3 text-muted-foreground" />
                            <span className="text-xs text-muted-foreground">
                              {formatDate(feedback.createdAt)}
                            </span>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* Pagination */}
              {data.totalPages > 1 && (
                <div className="flex items-center justify-between mt-6">
                  <Button
                    variant="outline"
                    onClick={() => handlePageChange(data.page - 1)}
                    disabled={!data.hasPrev}
                  >
                    <ChevronLeft className="h-4 w-4 mr-2" />
                    Previous
                  </Button>
                  <span className="text-sm text-muted-foreground">
                    Showing {(data.page - 1) * data.limit + 1} to{" "}
                    {Math.min(data.page * data.limit, data.total)} of{" "}
                    {data.total} feedback entries
                  </span>
                  <Button
                    variant="outline"
                    onClick={() => handlePageChange(data.page + 1)}
                    disabled={!data.hasNext}
                  >
                    Next
                    <ChevronRight className="h-4 w-4 ml-2" />
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </AdminLayout>
  );
}
