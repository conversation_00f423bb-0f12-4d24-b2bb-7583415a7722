"use client";

// Prevent static generation for this page
export const dynamic = 'force-dynamic';

import { AdminLayout } from "@/components/layout/admin-layout";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { adminApi, initializeAdminApi } from "@/lib/admin-api";
import {
  Activity,
  AlertCircle,
  BarChart3,
  Loader2,
  RefreshCw,
  TrendingUp,
  Users,
} from "lucide-react";
import { useEffect, useState } from "react";

interface SummaryData {
  totalUsers?: number;
  activeToday?: number;
  newUsersThisWeek?: number;
  feedbackRate?: number;
  usersWithFeedback?: number;
  averageSessionTime?: number;
  totalRequestsToday?: number;
  averageResponseTime?: number;
  systemUptime?: number;
}

export default function AnalyticsSummaryPage() {
  const [data, setData] = useState<SummaryData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  const fetchData = async () => {
    setLoading(true);
    setError(null);

    try {
      // Get the JWT token from localStorage or sessionStorage
      const token =
        localStorage.getItem("siift_access_token") ||
        sessionStorage.getItem("siift_access_token");

      if (!token) {
        throw new Error("No admin token found. Please login first.");
      }

      // Initialize admin API with token
      initializeAdminApi(token);

      const result = await adminApi.getAnalyticsSummary();
      setData(result);
      setLastUpdated(new Date());
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : "Unknown error";
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  const formatNumber = (num: number | undefined) => {
    if (num === undefined || num === null) return "0";
    if (num >= 1000000) return (num / 1000000).toFixed(1) + "M";
    if (num >= 1000) return (num / 1000).toFixed(1) + "K";
    return num.toString();
  };

  const formatDuration = (seconds: number | undefined) => {
    if (seconds === undefined || seconds === null) return "0s";
    if (seconds < 60) return `${seconds.toFixed(1)}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds.toFixed(0)}s`;
  };

  const metrics = data
    ? [
        {
          label: "Total Users",
          value: formatNumber(data.totalUsers),
          icon: Users,
          color: "text-blue-600",
          bgColor: "bg-blue-50",
        },
        {
          label: "Active Today",
          value: formatNumber(data.activeToday),
          icon: Activity,
          color: "text-green-600",
          bgColor: "bg-green-50",
        },
        {
          label: "New This Week",
          value: formatNumber(data.newUsersThisWeek),
          icon: TrendingUp,
          color: "text-purple-600",
          bgColor: "bg-purple-50",
        },
        {
          label: "Feedback Rate",
          value: `${data.feedbackRate}%`,
          icon: BarChart3,
          color: "text-indigo-600",
          bgColor: "bg-indigo-50",
        },
      ]
    : [];

  const additionalMetrics = data
    ? [
        {
          label: "Users with Feedback",
          value: formatNumber(data.usersWithFeedback),
        },
        {
          label: "Average Session Time",
          value: formatDuration(data.averageSessionTime),
        },
        {
          label: "Total Requests Today",
          value: formatNumber(data.totalRequestsToday),
        },
        {
          label: "Average Response Time",
          value: `${(data.averageResponseTime || 0).toFixed(1)}ms`,
        },
        {
          label: "System Uptime",
          value: formatDuration(data.systemUptime),
        },
      ]
    : [];

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Analytics Summary</h1>
            <p className="text-muted-foreground">
              Overview of key platform metrics and performance indicators.
            </p>
          </div>
          <div className="flex items-center space-x-4">
            {lastUpdated && (
              <span className="text-sm text-muted-foreground">
                Last updated: {lastUpdated.toLocaleTimeString()}
              </span>
            )}
            <Button onClick={fetchData} disabled={loading} variant="outline" className="border">
              {loading ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <RefreshCw className="h-4 w-4 mr-2" />
              )}
              Refresh
            </Button>
          </div>
        </div>

        {/* Error State */}
        {error && (
          <Card className="border-red-200 bg-red-50">
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <AlertCircle className="h-5 w-5 text-red-600" />
                <p className="text-red-800">{error}</p>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Loading State */}
        {loading && !data && (
          <div className="flex items-center justify-center py-12">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        )}

        {/* Main Metrics */}
        {data && (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {metrics.map((metric, index) => (
                <Card key={index} className="bg-gray-50 dark:bg-card border hover:shadow-md transition-all duration-200">
                  <CardContent className="p-6">
                    <div className="flex items-center space-x-4">
                      <div className={`p-3 rounded-full ${metric.bgColor}`}>
                        <metric.icon className={`h-6 w-6 ${metric.color}`} />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">
                          {metric.label}
                        </p>
                        <p className="text-3xl font-bold">{metric.value}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Additional Metrics */}
            <Card>
              <CardHeader>
                <CardTitle>Additional Metrics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {additionalMetrics.map((metric, index) => (
                    <div key={index} className="space-y-2">
                      <p className="text-sm font-medium text-muted-foreground">
                        {metric.label}
                      </p>
                      <p className="text-2xl font-bold">{metric.value}</p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Status */}
            <Card>
              <CardHeader>
                <CardTitle>Status</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-2">
                  <Badge
                    variant="default"
                    className="bg-green-100 text-green-800"
                  >
                    Data Loaded Successfully
                  </Badge>
                  <span className="text-sm text-muted-foreground">
                    All metrics are up to date
                  </span>
                </div>
              </CardContent>
            </Card>
          </>
        )}
      </div>
    </AdminLayout>
  );
}
