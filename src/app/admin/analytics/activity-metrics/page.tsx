"use client";

// Prevent static generation for this page
export const dynamic = 'force-dynamic';

import { AdminLayout } from "@/components/layout/admin-layout";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { adminApi, initializeAdminApi } from "@/lib/admin-api";
import {
  Activity,
  AlertCircle,
  Calendar,
  Loader2,
  RefreshCw,
  TrendingDown,
  TrendingUp,
  Users,
} from "lucide-react";
import { useEffect, useState } from "react";

export default function ActivityMetricsPage() {
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  const fetchData = async () => {
    setLoading(true);
    setError(null);

    try {
      const token =
        localStorage.getItem("siift_access_token") ||
        sessionStorage.getItem("siift_access_token");

      if (!token) {
        throw new Error("No admin token found. Please login first.");
      }

      initializeAdminApi(token);

      const result = await adminApi.getActivityMetrics();
      setData(result);
      setLastUpdated(new Date());
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  const formatNumber = (num: number) => {
    if (num >= 1000000) return (num / 1000000).toFixed(1) + "M";
    if (num >= 1000) return (num / 1000).toFixed(1) + "K";
    return num.toString();
  };

  const getChangeIcon = (change: number) => {
    if (change > 0) {
      return <TrendingUp className="h-4 w-4 text-green-600" />;
    } else if (change < 0) {
      return <TrendingDown className="h-4 w-4 text-red-600" />;
    }
    return <Activity className="h-4 w-4 text-gray-600" />;
  };

  const getChangeColor = (change: number) => {
    if (change > 0) return "text-green-600";
    if (change < 0) return "text-red-600";
    return "text-gray-600";
  };

  const metrics = data
    ? [
        {
          label: "Daily Active Users",
          value: formatNumber(data.dau),
          change: data.dauChange,
          icon: Users,
          color: "text-blue-600",
          bgColor: "bg-blue-50",
          period: "Today",
        },
        {
          label: "Weekly Active Users",
          value: formatNumber(data.wau),
          change: data.wauChange,
          icon: Calendar,
          color: "text-green-600",
          bgColor: "bg-green-50",
          period: "This Week",
        },
        {
          label: "Monthly Active Users",
          value: formatNumber(data.mau),
          change: data.mauChange,
          icon: Activity,
          color: "text-purple-600",
          bgColor: "bg-purple-50",
          period: "This Month",
        },
        {
          label: "Yearly Active Users",
          value: formatNumber(data.yau),
          change: data.yauChange,
          icon: TrendingUp,
          color: "text-indigo-600",
          bgColor: "bg-indigo-50",
          period: "This Year",
        },
      ]
    : [];

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Activity Metrics</h1>
            <p className="text-muted-foreground">
              Track user activity patterns across different time periods.
            </p>
          </div>
          <div className="flex items-center space-x-4">
            {lastUpdated && (
              <span className="text-sm text-muted-foreground">
                Last updated: {lastUpdated.toLocaleTimeString()}
              </span>
            )}
            <Button onClick={fetchData} disabled={loading} variant="outline">
              {loading ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <RefreshCw className="h-4 w-4 mr-2" />
              )}
              Refresh
            </Button>
          </div>
        </div>

        {/* Error State */}
        {error && (
          <Card className="border-red-200 bg-red-50">
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <AlertCircle className="h-5 w-5 text-red-600" />
                <p className="text-red-800">{error}</p>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Loading State */}
        {loading && !data && (
          <div className="flex items-center justify-center py-12">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        )}

        {/* Activity Metrics */}
        {data && (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {metrics.map((metric, index) => (
                <Card key={index} className="relative overflow-hidden">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between mb-4">
                      <div className={`p-3 rounded-full ${metric.bgColor}`}>
                        <metric.icon className={`h-6 w-6 ${metric.color}`} />
                      </div>
                      <Badge variant="outline" className="text-xs">
                        {metric.period}
                      </Badge>
                    </div>

                    <div className="space-y-2">
                      <p className="text-sm font-medium text-muted-foreground">
                        {metric.label}
                      </p>
                      <p className="text-3xl font-bold">{metric.value}</p>

                      <div className="flex items-center space-x-2">
                        {getChangeIcon(metric.change)}
                        <span
                          className={`text-sm font-medium ${getChangeColor(
                            metric.change
                          )}`}
                        >
                          {metric.change > 0 ? "+" : ""}
                          {metric.change.toFixed(1)}%
                        </span>
                        <span className="text-xs text-muted-foreground">
                          vs previous period
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Summary Card */}
            <Card>
              <CardHeader>
                <CardTitle>Activity Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <h3 className="font-semibold text-lg">Current Period</h3>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">
                          Report Date:
                        </span>
                        <span className="font-medium">
                          {data.date
                            ? new Date(data.date).toLocaleDateString()
                            : "Today"}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">
                          Total Active Users:
                        </span>
                        <span className="font-medium">
                          {formatNumber(data.dau)}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <h3 className="font-semibold text-lg">Growth Trends</h3>
                    <div className="space-y-2">
                      {metrics.map((metric, index) => (
                        <div
                          key={index}
                          className="flex justify-between items-center"
                        >
                          <span className="text-muted-foreground text-sm">
                            {metric.label}:
                          </span>
                          <div className="flex items-center space-x-1">
                            {getChangeIcon(metric.change)}
                            <span
                              className={`text-sm font-medium ${getChangeColor(
                                metric.change
                              )}`}
                            >
                              {metric.change > 0 ? "+" : ""}
                              {metric.change.toFixed(1)}%
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Status */}
            <Card>
              <CardHeader>
                <CardTitle>Status</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-2">
                  <Badge
                    variant="default"
                    className="bg-green-100 text-green-800"
                  >
                    Data Loaded Successfully
                  </Badge>
                  <span className="text-sm text-muted-foreground">
                    Activity metrics are up to date
                  </span>
                </div>
              </CardContent>
            </Card>
          </>
        )}
      </div>
    </AdminLayout>
  );
}
