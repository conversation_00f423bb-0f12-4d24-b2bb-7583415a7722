"use client";

// Prevent static generation for this page
export const dynamic = 'force-dynamic';

import { AdminLayout } from "@/components/layout/admin-layout";
import { AdminTabbedContent } from "@/components/admin-tabbed-content";

export default function AdminRecentPage() {
  return (
    <AdminLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold">Recent Activity</h1>
          <p className="text-muted-foreground">
            View recent system activity and user actions.
          </p>
        </div>
        <AdminTabbedContent activeTab="recent" />
      </div>
    </AdminLayout>
  );
}
