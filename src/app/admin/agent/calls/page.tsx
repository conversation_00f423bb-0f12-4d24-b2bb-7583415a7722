"use client";

// Prevent static generation for this page
export const dynamic = 'force-dynamic';

import { AdminLayout } from "@/components/layout/admin-layout";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { adminApi, initializeAdminApi } from "@/lib/admin-api";
import {
  AlertCircle,
  ChevronLeft,
  ChevronRight,
  Clock,
  Database,
  DollarSign,
  Filter,
  Loader2,
  RefreshCw,
  Zap,
} from "lucide-react";
import { useCallback, useEffect, useState } from "react";

interface AgentCallData {
  data?: Array<{
    agentType: string;
    callType: string;
    status: string;
    createdAt: string;
    modelProvider: string;
    modelName: string;
    workflowType: string;
    totalTokens: number;
    cost: number;
    duration: number;
    inputTokens: number;
    outputTokens: number;
    toolsCalled?: string[];
    errorMessage?: string | null;
  }>;
  pagination?: {
    page: number;
    totalPages: number;
    total: number;
    limit: number;
    hasPrev: boolean;
    hasNext: boolean;
  };
}

export default function AgentCallsPage() {
  const [data, setData] = useState<AgentCallData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const [filters, setFilters] = useState({
    page: 1,
    limit: 10,
    agentType: "all",
    callType: "all",
    modelProvider: "all",
    status: "all",
  });

  const fetchData = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const token =
        localStorage.getItem("siift_access_token") ||
        sessionStorage.getItem("siift_access_token");

      if (!token) {
        throw new Error("No admin token found. Please login first.");
      }

      initializeAdminApi(token);

      const filterParams: Record<string, string | number> = {
        page: filters.page,
        limit: filters.limit,
      };

      if (filters.agentType && filters.agentType !== "all")
        filterParams.agentType = filters.agentType;
      if (filters.callType && filters.callType !== "all")
        filterParams.callType = filters.callType;
      if (filters.modelProvider && filters.modelProvider !== "all")
        filterParams.modelProvider = filters.modelProvider;
      if (filters.status && filters.status !== "all")
        filterParams.status = filters.status;

      const result = await adminApi.getAgentCalls(filterParams);
      setData(result);
      setLastUpdated(new Date());
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : "Unknown error";
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [filters]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const formatNumber = (num: number) => {
    if (num >= 1000000) return (num / 1000000).toFixed(1) + "M";
    if (num >= 1000) return (num / 1000).toFixed(1) + "K";
    return num.toString();
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 4,
    }).format(amount);
  };

  const formatDuration = (ms: number) => {
    if (ms < 1000) return `${ms}ms`;
    return `${(ms / 1000).toFixed(1)}s`;
  };

  const handleFilterChange = (key: string, value: string) => {
    setFilters((prev) => ({ ...prev, [key]: value, page: 1 }));
  };

  const handlePageChange = (newPage: number) => {
    setFilters((prev) => ({ ...prev, page: newPage }));
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "success":
        return "bg-green-100 text-green-800";
      case "error":
        return "bg-red-100 text-red-800";
      case "timeout":
        return "bg-yellow-100 text-yellow-800";
      case "cancelled":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getAgentTypeColor = (agentType: string) => {
    const colors: Record<string, string> = {
      intake: "bg-blue-100 text-blue-800",
      validate: "bg-green-100 text-green-800",
      build: "bg-purple-100 text-purple-800",
      grow: "bg-indigo-100 text-indigo-800",
      coordinator: "bg-pink-100 text-pink-800",
      topic: "bg-indigo-100 text-indigo-800",
    };
    return colors[agentType] || "bg-gray-100 text-gray-800";
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Agent Calls</h1>
            <p className="text-muted-foreground">
              Monitor and analyze individual agent execution calls.
            </p>
          </div>
          <div className="flex items-center space-x-4">
            {lastUpdated && (
              <span className="text-sm text-muted-foreground">
                Last updated: {lastUpdated.toLocaleTimeString()}
              </span>
            )}
            <Button onClick={fetchData} disabled={loading} variant="outline">
              {loading ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <RefreshCw className="h-4 w-4 mr-2" />
              )}
              Refresh
            </Button>
          </div>
        </div>

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Filter className="h-5 w-5" />
              <span>Filters</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
              <Select
                value={filters.agentType}
                onValueChange={(value) =>
                  handleFilterChange("agentType", value)
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Agent Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="intake">Intake</SelectItem>
                  <SelectItem value="validate">Validate</SelectItem>
                  <SelectItem value="build">Build</SelectItem>
                  <SelectItem value="grow">Grow</SelectItem>
                  <SelectItem value="coordinator">Coordinator</SelectItem>
                  <SelectItem value="topic">Topic</SelectItem>
                </SelectContent>
              </Select>

              <Select
                value={filters.callType}
                onValueChange={(value) => handleFilterChange("callType", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Call Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="agent_execution">
                    Agent Execution
                  </SelectItem>
                  <SelectItem value="llm_call">LLM Call</SelectItem>
                  <SelectItem value="tool_call">Tool Call</SelectItem>
                </SelectContent>
              </Select>

              <Select
                value={filters.modelProvider}
                onValueChange={(value) =>
                  handleFilterChange("modelProvider", value)
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Provider" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Providers</SelectItem>
                  <SelectItem value="openai">OpenAI</SelectItem>
                  <SelectItem value="deepseek">DeepSeek</SelectItem>
                  <SelectItem value="anthropic">Anthropic</SelectItem>
                </SelectContent>
              </Select>

              <Select
                value={filters.status}
                onValueChange={(value) => handleFilterChange("status", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="success">Success</SelectItem>
                  <SelectItem value="error">Error</SelectItem>
                  <SelectItem value="timeout">Timeout</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                </SelectContent>
              </Select>

              <Select
                value={filters.limit.toString()}
                onValueChange={(value) =>
                  setFilters((prev) => ({
                    ...prev,
                    limit: parseInt(value),
                    page: 1,
                  }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="5">5 per page</SelectItem>
                  <SelectItem value="10">10 per page</SelectItem>
                  <SelectItem value="25">25 per page</SelectItem>
                  <SelectItem value="50">50 per page</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Error State */}
        {error && (
          <Card className="border-red-200 bg-red-50">
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <AlertCircle className="h-5 w-5 text-red-600" />
                <p className="text-red-800">{error}</p>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Loading State */}
        {loading && !data && (
          <div className="flex items-center justify-center py-12">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        )}

        {/* Agent Calls List */}
        {data && (
          <>
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Zap className="h-5 w-5" />
                    <span>Agent Calls ({data.pagination?.total || 0})</span>
                  </div>
                  <Badge variant="outline">
                    Page {data.pagination?.page || 1} of{" "}
                    {data.pagination?.totalPages || 1}
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {data.data?.map((call, index: number) => (
                    <Card key={index} className="border-l-4 border-l-blue-500">
                      <CardContent className="p-4">
                        <div className="space-y-3">
                          {/* Header Row */}
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-2">
                              <Badge
                                className={getAgentTypeColor(call.agentType)}
                              >
                                {call.agentType}
                              </Badge>
                              <Badge variant="outline">{call.callType}</Badge>
                              <Badge className={getStatusColor(call.status)}>
                                {call.status}
                              </Badge>
                            </div>
                            <div className="text-right text-sm text-muted-foreground">
                              {new Date(call.createdAt).toLocaleString()}
                            </div>
                          </div>

                          {/* Model and Metrics Row */}
                          <div className="flex items-center justify-between">
                            <div className="space-y-1">
                              <p className="text-sm font-medium">
                                {call.modelProvider} - {call.modelName}
                              </p>
                              <p className="text-xs text-muted-foreground">
                                Workflow: {call.workflowType}
                              </p>
                            </div>
                            <div className="flex items-center space-x-4 text-sm">
                              <div className="flex items-center space-x-1">
                                <Database className="h-4 w-4 text-blue-600" />
                                <span>
                                  {formatNumber(call.totalTokens)} tokens
                                </span>
                              </div>
                              <div className="flex items-center space-x-1">
                                <DollarSign className="h-4 w-4 text-green-600" />
                                <span>{formatCurrency(call.cost)}</span>
                              </div>
                              <div className="flex items-center space-x-1">
                                <Clock className="h-4 w-4 text-purple-600" />
                                <span>{formatDuration(call.duration)}</span>
                              </div>
                            </div>
                          </div>

                          {/* Token Breakdown */}
                          <div className="grid grid-cols-3 gap-4 text-xs text-muted-foreground">
                            <div>
                              Input: {formatNumber(call.inputTokens)} tokens
                            </div>
                            <div>
                              Output: {formatNumber(call.outputTokens)} tokens
                            </div>
                            <div>
                              Tools: {call.toolsCalled?.length || 0} called
                            </div>
                          </div>

                          {/* Tools Called */}
                          {call.toolsCalled && call.toolsCalled.length > 0 && (
                            <div className="flex items-center space-x-2">
                              <span className="text-xs text-muted-foreground">
                                Tools:
                              </span>
                              <div className="flex flex-wrap gap-1">
                                {call.toolsCalled.map(
                                  (tool: string, toolIndex: number) => (
                                    <Badge
                                      key={toolIndex}
                                      variant="secondary"
                                      className="text-xs"
                                    >
                                      {tool}
                                    </Badge>
                                  )
                                )}
                              </div>
                            </div>
                          )}

                          {/* Error Message */}
                          {call.errorMessage && (
                            <div className="bg-red-50 border border-red-200 rounded p-2">
                              <p className="text-xs text-red-800">
                                {call.errorMessage}
                              </p>
                            </div>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>

                {/* Pagination */}
                {data.pagination &&
                  data.pagination.totalPages > 1 &&
                  (() => {
                    const pagination = data.pagination!;
                    return (
                      <div className="flex items-center justify-between mt-6">
                        <Button
                          variant="outline"
                          onClick={() => handlePageChange(pagination.page - 1)}
                          disabled={!pagination.hasPrev}
                        >
                          <ChevronLeft className="h-4 w-4 mr-2" />
                          Previous
                        </Button>
                        <span className="text-sm text-muted-foreground">
                          Showing {(pagination.page - 1) * pagination.limit + 1}{" "}
                          to{" "}
                          {Math.min(
                            pagination.page * pagination.limit,
                            pagination.total
                          )}{" "}
                          of {pagination.total} calls
                        </span>
                        <Button
                          variant="outline"
                          onClick={() => handlePageChange(pagination.page + 1)}
                          disabled={!pagination.hasNext}
                        >
                          Next
                          <ChevronRight className="h-4 w-4 ml-2" />
                        </Button>
                      </div>
                    );
                  })()}
              </CardContent>
            </Card>
          </>
        )}
      </div>
    </AdminLayout>
  );
}
