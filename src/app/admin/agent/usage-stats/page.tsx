"use client";

// Prevent static generation for this page
export const dynamic = 'force-dynamic';

import { AdminLayout } from "@/components/layout/admin-layout";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { adminApi, initializeAdminApi } from "@/lib/admin-api";
import {
  AlertCircle,
  CheckCircle,
  Clock,
  Database,
  DollarSign,
  Loader2,
  RefreshCw,
  Zap,
} from "lucide-react";
import { useEffect, useState } from "react";
import {
  Bar,
  BarChart,
  CartesianGrid,
  Cell,
  Pie,
  PieChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts";

export default function AgentUsageStatsPage() {
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  const fetchData = async () => {
    setLoading(true);
    setError(null);

    try {
      const token =
        localStorage.getItem("siift_access_token") ||
        sessionStorage.getItem("siift_access_token");

      if (!token) {
        throw new Error("No admin token found. Please login first.");
      }

      initializeAdminApi(token);

      const result = await adminApi.getAgentUsageStats();
      setData(result);
      setLastUpdated(new Date());
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  const formatNumber = (num: number) => {
    if (num >= 1000000) return (num / 1000000).toFixed(1) + "M";
    if (num >= 1000) return (num / 1000).toFixed(1) + "K";
    return num.toString();
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 4,
    }).format(amount);
  };

  const formatDuration = (ms: number) => {
    if (ms < 1000) return `${ms}ms`;
    return `${(ms / 1000).toFixed(1)}s`;
  };

  const mainStats = data
    ? [
        {
          label: "Total Calls",
          value: formatNumber(data.totalCalls),
          icon: Zap,
          color: "text-blue-600",
          bgColor: "bg-blue-50",
        },
        {
          label: "Success Rate",
          value: `${data.successRate.toFixed(1)}%`,
          icon: CheckCircle,
          color: "text-green-600",
          bgColor: "bg-green-50",
        },
        {
          label: "Total Cost",
          value: formatCurrency(data.totalCost),
          icon: DollarSign,
          color: "text-purple-600",
          bgColor: "bg-purple-50",
        },
        {
          label: "Avg Duration",
          value: formatDuration(data.averageDuration),
          icon: Clock,
          color: "text-indigo-600",
          bgColor: "bg-indigo-50",
        },
      ]
    : [];

  const additionalStats = data
    ? [
        {
          label: "Successful Calls",
          value: formatNumber(data.successfulCalls),
        },
        {
          label: "Failed Calls",
          value: formatNumber(data.failedCalls),
        },
        {
          label: "Total Tokens",
          value: formatNumber(data.totalTokens),
        },
        {
          label: "Avg Tokens/Call",
          value: formatNumber(data.averageTokensPerCall),
        },
        {
          label: "Avg Cost/Call",
          value: formatCurrency(data.averageCostPerCall),
        },
        {
          label: "Most Used Agent",
          value: data.mostUsedAgentType,
        },
        {
          label: "Most Used Model",
          value: data.mostUsedModel,
        },
      ]
    : [];

  // Prepare chart data
  const agentTypeData = data?.callsByAgentType
    ? Object.entries(data.callsByAgentType).map(([type, count]) => ({
        type,
        count,
      }))
    : [];

  const providerData = data?.callsByProvider
    ? Object.entries(data.callsByProvider).map(([provider, count]) => ({
        provider,
        count,
      }))
    : [];

  const modelCostData = data?.costByModel
    ? Object.entries(data.costByModel).map(([model, cost]) => ({ model, cost }))
    : [];

  const COLORS = [
    "#166534",
    "#7c3aed",
    "#ea580c",
    "#dc2626",
    "#0891b2",
    "#059669",
  ];

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Agent Usage Statistics</h1>
            <p className="text-muted-foreground">
              Comprehensive overview of agent performance and usage metrics.
            </p>
          </div>
          <div className="flex items-center space-x-4">
            {lastUpdated && (
              <span className="text-sm text-muted-foreground">
                Last updated: {lastUpdated.toLocaleTimeString()}
              </span>
            )}
            <Button onClick={fetchData} disabled={loading} variant="outline">
              {loading ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <RefreshCw className="h-4 w-4 mr-2" />
              )}
              Refresh
            </Button>
          </div>
        </div>

        {/* Error State */}
        {error && (
          <Card className="border-red-200 bg-red-50">
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <AlertCircle className="h-5 w-5 text-red-600" />
                <p className="text-red-800">{error}</p>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Loading State */}
        {loading && !data && (
          <div className="flex items-center justify-center py-12">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        )}

        {/* Main Statistics */}
        {data && (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {mainStats.map((stat, index) => (
                <Card key={index}>
                  <CardContent className="p-6">
                    <div className="flex items-center space-x-4">
                      <div className={`p-3 rounded-full ${stat.bgColor}`}>
                        <stat.icon className={`h-6 w-6 ${stat.color}`} />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">
                          {stat.label}
                        </p>
                        <p className="text-3xl font-bold">{stat.value}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Charts Row */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Agent Type Distribution */}
              <Card>
                <CardHeader>
                  <CardTitle>Calls by Agent Type</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-64">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart data={agentTypeData}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="type" />
                        <YAxis />
                        <Tooltip
                          formatter={(value: any) => [
                            formatNumber(value),
                            "Calls",
                          ]}
                        />
                        <Bar dataKey="count" fill="#166534" />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>

              {/* Provider Distribution */}
              <Card>
                <CardHeader>
                  <CardTitle>Calls by Provider</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-64">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={providerData}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          label={(entry: any) =>
                            `${entry.provider} ${(
                              (entry.percent || 0) * 100
                            ).toFixed(0)}%`
                          }
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="count"
                        >
                          {providerData.map((entry, index) => (
                            <Cell
                              key={`cell-${index}`}
                              fill={COLORS[index % COLORS.length]}
                            />
                          ))}
                        </Pie>
                        <Tooltip
                          formatter={(value: any) => [
                            formatNumber(value),
                            "Calls",
                          ]}
                        />
                      </PieChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Model Cost Analysis */}
            <Card>
              <CardHeader>
                <CardTitle>Cost by Model</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={modelCostData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="model" />
                      <YAxis />
                      <Tooltip
                        formatter={(value: any) => [
                          formatCurrency(value),
                          "Cost",
                        ]}
                      />
                      <Bar dataKey="cost" fill="#7c3aed" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            {/* Additional Statistics */}
            <Card>
              <CardHeader>
                <CardTitle>Detailed Statistics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  {additionalStats.map((stat, index) => (
                    <div key={index} className="space-y-2">
                      <p className="text-sm font-medium text-muted-foreground">
                        {stat.label}
                      </p>
                      <p className="text-2xl font-bold">{stat.value}</p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Performance Summary */}
            <Card>
              <CardHeader>
                <CardTitle>Performance Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <CheckCircle className="h-5 w-5 text-green-600" />
                      <span className="font-medium">Success Rate</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-green-600 h-2 rounded-full"
                        style={{ width: `${data.successRate}%` }}
                      ></div>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {data.successfulCalls} successful out of {data.totalCalls}{" "}
                      total calls
                    </p>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Database className="h-5 w-5 text-blue-600" />
                      <span className="font-medium">Token Efficiency</span>
                    </div>
                    <p className="text-2xl font-bold">
                      {formatNumber(data.averageTokensPerCall)}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      Average tokens per call
                    </p>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <DollarSign className="h-5 w-5 text-purple-600" />
                      <span className="font-medium">Cost Efficiency</span>
                    </div>
                    <p className="text-2xl font-bold">
                      {formatCurrency(data.averageCostPerCall)}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      Average cost per call
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </>
        )}
      </div>
    </AdminLayout>
  );
}
