"use client";

// Prevent static generation for this page
export const dynamic = 'force-dynamic';

import { AdminLayout } from "@/components/layout/admin-layout";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { adminApi, initializeAdminApi } from "@/lib/admin-api";
import {
  AlertCircle,
  BarChart3,
  Database,
  DollarSign,
  Loader2,
  RefreshCw,
  TrendingUp,
} from "lucide-react";
import { useEffect, useState } from "react";
import {
  Area,
  AreaChart,
  Bar,
  BarChart,
  CartesianGrid,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts";

interface TokenTrendsData {
  summary?: {
    totalDays: number;
    totalTokens: number;
    totalCost: number;
    growthRate: number;
    averageTokensPerDay: number;
    averageCostPerDay: number;
  };
  data?: Array<{
    date: string;
    totalTokens: number;
    totalCost: number;
    inputTokens: number;
    outputTokens: number;
    callCount: number;
    averageTokensPerCall: number;
  }>;
}

export default function TokenTrendsPage() {
  const [data, setData] = useState<TokenTrendsData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  const fetchData = async () => {
    setLoading(true);
    setError(null);

    try {
      const token =
        localStorage.getItem("siift_access_token") ||
        sessionStorage.getItem("siift_access_token");

      if (!token) {
        throw new Error("No admin token found. Please login first.");
      }

      initializeAdminApi(token);

      const result = await adminApi.getTokenTrends();
      setData(result);
      setLastUpdated(new Date());
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : "Unknown error";
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  const formatNumber = (num: number) => {
    if (num >= 1000000) return (num / 1000000).toFixed(1) + "M";
    if (num >= 1000) return (num / 1000).toFixed(1) + "K";
    return num.toString();
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 4,
    }).format(amount);
  };

  const summaryMetrics = data?.summary
    ? [
        {
          label: "Total Days",
          value: data.summary.totalDays,
          icon: BarChart3,
          color: "text-blue-600",
          bgColor: "bg-blue-50",
        },
        {
          label: "Total Tokens",
          value: formatNumber(data.summary.totalTokens),
          icon: Database,
          color: "text-green-600",
          bgColor: "bg-green-50",
        },
        {
          label: "Total Cost",
          value: formatCurrency(data.summary.totalCost),
          icon: DollarSign,
          color: "text-purple-600",
          bgColor: "bg-purple-50",
        },
        {
          label: "Growth Rate",
          value: `${data.summary.growthRate.toFixed(1)}%`,
          icon: TrendingUp,
          color: "text-indigo-600",
          bgColor: "bg-indigo-50",
        },
      ]
    : [];

  const dailyAverages = data?.summary
    ? [
        {
          label: "Avg Tokens/Day",
          value: formatNumber(data.summary.averageTokensPerDay),
        },
        {
          label: "Avg Cost/Day",
          value: formatCurrency(data.summary.averageCostPerDay),
        },
      ]
    : [];

  // Prepare chart data
  const chartData =
    data?.data?.map((item) => ({
      ...item,
      date: new Date(item.date).toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
      }),
    })) || [];

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Token Trends</h1>
            <p className="text-muted-foreground">
              Track token usage patterns and cost trends over time.
            </p>
          </div>
          <div className="flex items-center space-x-4">
            {lastUpdated && (
              <span className="text-sm text-muted-foreground">
                Last updated: {lastUpdated.toLocaleTimeString()}
              </span>
            )}
            <Button onClick={fetchData} disabled={loading} variant="outline">
              {loading ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <RefreshCw className="h-4 w-4 mr-2" />
              )}
              Refresh
            </Button>
          </div>
        </div>

        {/* Error State */}
        {error && (
          <Card className="border-red-200 bg-red-50">
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <AlertCircle className="h-5 w-5 text-red-600" />
                <p className="text-red-800">{error}</p>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Loading State */}
        {loading && !data && (
          <div className="flex items-center justify-center py-12">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        )}

        {/* Summary Metrics */}
        {data && (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {summaryMetrics.map((metric, index) => (
                <Card key={index}>
                  <CardContent className="p-6">
                    <div className="flex items-center space-x-4">
                      <div className={`p-3 rounded-full ${metric.bgColor}`}>
                        <metric.icon className={`h-6 w-6 ${metric.color}`} />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">
                          {metric.label}
                        </p>
                        <p className="text-3xl font-bold">{metric.value}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Token Usage Trend Chart */}
            <Card>
              <CardHeader>
                <CardTitle>Token Usage Over Time</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <AreaChart data={chartData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis />
                      <Tooltip
                        formatter={(value: number, name: string) => {
                          if (name === "totalTokens")
                            return [formatNumber(value), "Total Tokens"];
                          if (name === "inputTokens")
                            return [formatNumber(value), "Input Tokens"];
                          if (name === "outputTokens")
                            return [formatNumber(value), "Output Tokens"];
                          return [formatNumber(value), name];
                        }}
                      />
                      <Area
                        type="monotone"
                        dataKey="totalTokens"
                        stackId="1"
                        stroke="#166534"
                        fill="#166534"
                        fillOpacity={0.6}
                      />
                      <Area
                        type="monotone"
                        dataKey="inputTokens"
                        stackId="2"
                        stroke="#7c3aed"
                        fill="#7c3aed"
                        fillOpacity={0.6}
                      />
                      <Area
                        type="monotone"
                        dataKey="outputTokens"
                        stackId="3"
                        stroke="#ea580c"
                        fill="#ea580c"
                        fillOpacity={0.6}
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            {/* Cost and Call Trends */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Cost Trends</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-64">
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart data={chartData}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="date" />
                        <YAxis />
                        <Tooltip
                          formatter={(value: number) => [
                            formatCurrency(value),
                            "Total Cost",
                          ]}
                        />
                        <Line
                          type="monotone"
                          dataKey="totalCost"
                          stroke="#7c3aed"
                          strokeWidth={3}
                          dot={{ fill: "#7c3aed", strokeWidth: 2, r: 4 }}
                        />
                      </LineChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Call Volume</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-64">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart data={chartData}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="date" />
                        <YAxis />
                        <Tooltip
                          formatter={(value: number) => [
                            formatNumber(value),
                            "Calls",
                          ]}
                        />
                        <Bar dataKey="callCount" fill="#166534" />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Efficiency Metrics */}
            <Card>
              <CardHeader>
                <CardTitle>Efficiency Metrics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart data={chartData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis />
                      <Tooltip
                        formatter={(value: number) => [
                          formatNumber(value),
                          "Avg Tokens/Call",
                        ]}
                      />
                      <Line
                        type="monotone"
                        dataKey="averageTokensPerCall"
                        stroke="#ea580c"
                        strokeWidth={2}
                        dot={{ fill: "#ea580c", strokeWidth: 2, r: 4 }}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            {/* Summary Statistics */}
            <Card>
              <CardHeader>
                <CardTitle>Summary Statistics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  {summaryMetrics.map((metric, index) => (
                    <div key={index} className="space-y-2">
                      <p className="text-sm font-medium text-muted-foreground">
                        {metric.label}
                      </p>
                      <p className="text-2xl font-bold">{metric.value}</p>
                    </div>
                  ))}
                  {dailyAverages.map((metric, index) => (
                    <div key={`avg-${index}`} className="space-y-2">
                      <p className="text-sm font-medium text-muted-foreground">
                        {metric.label}
                      </p>
                      <p className="text-2xl font-bold">{metric.value}</p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Data Table */}
            <Card>
              <CardHeader>
                <CardTitle>Recent Data</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left p-2">Date</th>
                        <th className="text-right p-2">Total Tokens</th>
                        <th className="text-right p-2">Input Tokens</th>
                        <th className="text-right p-2">Output Tokens</th>
                        <th className="text-right p-2">Total Cost</th>
                        <th className="text-right p-2">Calls</th>
                        <th className="text-right p-2">Avg Tokens/Call</th>
                      </tr>
                    </thead>
                    <tbody>
                      {data.data?.slice(0, 10).map((item, index: number) => (
                        <tr key={index} className="border-b hover:bg-muted/50">
                          <td className="p-2">
                            {new Date(item.date).toLocaleDateString()}
                          </td>
                          <td className="text-right p-2">
                            {formatNumber(item.totalTokens)}
                          </td>
                          <td className="text-right p-2">
                            {formatNumber(item.inputTokens)}
                          </td>
                          <td className="text-right p-2">
                            {formatNumber(item.outputTokens)}
                          </td>
                          <td className="text-right p-2">
                            {formatCurrency(item.totalCost)}
                          </td>
                          <td className="text-right p-2">
                            {formatNumber(item.callCount)}
                          </td>
                          <td className="text-right p-2">
                            {formatNumber(item.averageTokensPerCall)}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
                {data.data && data.data.length > 10 && (
                  <div className="mt-4 text-center">
                    <Badge variant="outline">
                      Showing first 10 of {data.data.length} records
                    </Badge>
                  </div>
                )}
              </CardContent>
            </Card>
          </>
        )}
      </div>
    </AdminLayout>
  );
}
