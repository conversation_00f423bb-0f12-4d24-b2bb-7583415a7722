"use client";

// Prevent static generation for this page
export const dynamic = 'force-dynamic';

import { AdminLayout } from "@/components/layout/admin-layout";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { adminApi, initializeAdminApi } from "@/lib/admin-api";
import {
  Activity,
  BarChart3,
  Brain,
  CheckCircle,
  Clock,
  Database,
  DollarSign,
  Loader2,
  Play,
  Server,
  TrendingUp,
  Users,
  XCircle,
  Zap,
} from "lucide-react";
import { useEffect, useState } from "react";
import {
  Bar,
  BarChart,
  CartesianGrid,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  <PERSON>Axis,
} from "recharts";

interface TestResult {
  endpoint: string;
  name: string;
  category: "analytics" | "agent" | "system";
  status: "pending" | "success" | "error";
  data?: any;
  error?: string;
  duration?: number;
}

export default function ApiTestPage() {
  const [isRunning, setIsRunning] = useState(false);
  const [results, setResults] = useState<TestResult[]>([]);
  const [hasToken, setHasToken] = useState(false);
  const [currentTestIndex, setCurrentTestIndex] = useState(0);

  // Check for token on client side only
  useEffect(() => {
    // Check both localStorage and sessionStorage for the token
    const token =
      localStorage.getItem("siift_access_token") ||
      sessionStorage.getItem("siift_access_token");
    setHasToken(!!token);
  }, []);

  const endpoints = [
    // Basic Analytics
    {
      name: "Analytics Summary",
      endpoint: "/admin/analytics/summary",
      category: "analytics" as const,
      method: () => adminApi.getAnalyticsSummary(),
    },
    {
      name: "User Analytics",
      endpoint: "/admin/analytics/users",
      category: "analytics" as const,
      method: () => adminApi.getUserAnalytics({ limit: 5 }),
    },
    {
      name: "Activity Metrics",
      endpoint: "/admin/analytics/activity-metrics",
      category: "analytics" as const,
      method: () => adminApi.getActivityMetrics(),
    },
    {
      name: "Activity Trends",
      endpoint: "/admin/analytics/activity-trends",
      category: "analytics" as const,
      method: () => adminApi.getActivityTrends(),
    },

    // Agent Analytics
    {
      name: "Agent Calls",
      endpoint: "/admin/agent-analytics/calls",
      category: "agent" as const,
      method: () => adminApi.getAgentCalls({ limit: 5 }),
    },
    {
      name: "Agent Usage Stats",
      endpoint: "/admin/agent-analytics/usage-stats",
      category: "agent" as const,
      method: () => adminApi.getAgentUsageStats(),
    },
    {
      name: "Token Trends",
      endpoint: "/admin/agent-analytics/token-trends",
      category: "agent" as const,
      method: () => adminApi.getTokenTrends(),
    },
    // System
    {
      name: "Health Check",
      endpoint: "/admin/health",
      category: "system" as const,
      method: () => adminApi.getHealthCheck(),
    },
  ];

  const runTests = async () => {
    setIsRunning(true);
    setResults([]);
    setCurrentTestIndex(0);

    // Get the JWT token from localStorage or sessionStorage
    const token =
      localStorage.getItem("siift_access_token") ||
      sessionStorage.getItem("siift_access_token");

    if (!token) {
      setResults([
        {
          endpoint: "Authentication",
          name: "Authentication",
          category: "system",
          status: "error",
          error: "No admin token found. Please login first.",
        },
      ]);
      setIsRunning(false);
      return;
    }

    // Initialize admin API with token
    console.log(
      "🔑 Initializing admin API with token:",
      token ? `${token.substring(0, 20)}...` : "null"
    );
    initializeAdminApi(token);

    // Verify token was set
    console.log(
      "🔍 Admin API token after initialization:",
      adminApi.getToken() ? "✅ Set" : "❌ Not set"
    );

    const testResults: TestResult[] = [];

    for (let i = 0; i < endpoints.length; i++) {
      const test = endpoints[i];
      setCurrentTestIndex(i);
      const startTime = Date.now();

      try {
        console.log(`Testing ${test.name}...`);
        const data = await test.method();
        const duration = Date.now() - startTime;

        testResults.push({
          endpoint: test.endpoint,
          name: test.name,
          category: test.category,
          status: "success",
          data,
          duration,
        });

        console.log(`✅ ${test.name} succeeded:`, data);
      } catch (error: any) {
        const duration = Date.now() - startTime;

        testResults.push({
          endpoint: test.endpoint,
          name: test.name,
          category: test.category,
          status: "error",
          error: error.message,
          duration,
        });

        console.log(`❌ ${test.name} failed:`, error.message);
      }

      setResults([...testResults]);
    }

    setIsRunning(false);
    setCurrentTestIndex(0);
  };

  const getStatusIcon = (status: TestResult["status"]) => {
    switch (status) {
      case "success":
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case "error":
        return <XCircle className="h-5 w-5 text-red-600" />;
      case "pending":
        return <Loader2 className="h-5 w-5 animate-spin text-blue-600" />;
    }
  };

  const getStatusBadge = (status: TestResult["status"]) => {
    switch (status) {
      case "success":
        return (
          <Badge className="bg-green-100 text-green-800 hover:bg-green-100">
            Success
          </Badge>
        );
      case "error":
        return (
          <Badge className="bg-red-100 text-red-800 hover:bg-red-100">
            Error
          </Badge>
        );
      case "pending":
        return (
          <Badge className="bg-[var(--siift-light-main)] text-[var(--siift-darkest)] hover:bg-[var(--siift-light-main)]">
            Pending
          </Badge>
        );
    }
  };

  const getCategoryIcon = (category: TestResult["category"]) => {
    switch (category) {
      case "analytics":
        return <BarChart3 className="h-5 w-5" />;
      case "agent":
        return <Brain className="h-5 w-5" />;
      case "system":
        return <Server className="h-5 w-5" />;
    }
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) return (num / 1000000).toFixed(1) + "M";
    if (num >= 1000) return (num / 1000).toFixed(1) + "K";
    return num.toString();
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 4,
    }).format(amount);
  };

  const formatDuration = (ms: number) => {
    if (ms < 1000) return `${ms}ms`;
    return `${(ms / 1000).toFixed(1)}s`;
  };

  // Data visualization components
  const renderAnalyticsSummary = (data: any) => {
    if (!data) return null;

    const metrics = [
      {
        label: "Total Users",
        value: data.totalUsers,
        icon: Users,
        color: "text-blue-600",
      },
      {
        label: "Active Today",
        value: data.activeToday,
        icon: Activity,
        color: "text-green-600",
      },
      {
        label: "New This Week",
        value: data.newUsersThisWeek,
        icon: TrendingUp,
        color: "text-purple-600",
      },
      {
        label: "Feedback Rate",
        value: `${data.feedbackRate}%`,
        icon: BarChart3,
        color: "text-orange-600",
      },
    ];

    return (
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {metrics.map((metric, index) => (
          <Card key={index}>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <metric.icon className={`h-5 w-5 ${metric.color}`} />
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    {metric.label}
                  </p>
                  <p className="text-2xl font-bold">{metric.value}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  };

  const renderUserAnalytics = (data: any) => {
    if (!data?.data) return null;

    return (
      <div className="space-y-4">
        <div className="grid gap-4">
          {data.data.slice(0, 5).map((user: any, index: number) => (
            <Card key={index}>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">
                      {user.firstName} {user.lastName}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {user.email}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      Role: {user.role}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium">
                      {user.actionCount} actions
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {formatDuration(user.averageSessionTime * 1000)} avg
                      session
                    </p>
                    <Badge
                      variant={
                        user.hasSubmittedFeedback ? "default" : "secondary"
                      }
                    >
                      {user.hasSubmittedFeedback
                        ? "Has Feedback"
                        : "No Feedback"}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  };

  const renderActivityMetrics = (data: any) => {
    if (!data) return null;

    const metrics = [
      {
        label: "Daily Active Users",
        value: formatNumber(data.dau),
        change: data.dauChange,
      },
      {
        label: "Weekly Active Users",
        value: formatNumber(data.wau),
        change: data.wauChange,
      },
      {
        label: "Monthly Active Users",
        value: formatNumber(data.mau),
        change: data.mauChange,
      },
      {
        label: "Yearly Active Users",
        value: formatNumber(data.yau),
        change: data.yauChange,
      },
    ];

    return (
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {metrics.map((metric, index) => (
          <Card key={index}>
            <CardContent className="p-4">
              <div className="space-y-2">
                <p className="text-sm font-medium text-muted-foreground">
                  {metric.label}
                </p>
                <p className="text-2xl font-bold">{metric.value}</p>
                <div className="flex items-center space-x-1">
                  {metric.change > 0 ? (
                    <TrendingUp className="h-4 w-4 text-green-600" />
                  ) : (
                    <TrendingUp className="h-4 w-4 text-red-600 rotate-180" />
                  )}
                  <span
                    className={`text-sm ${
                      metric.change > 0 ? "text-green-600" : "text-red-600"
                    }`}
                  >
                    {metric.change > 0 ? "+" : ""}
                    {metric.change.toFixed(1)}%
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  };

  const renderAgentUsageStats = (data: any) => {
    if (!data) return null;

    const stats = [
      { label: "Total Calls", value: formatNumber(data.totalCalls), icon: Zap },
      {
        label: "Success Rate",
        value: `${data.successRate.toFixed(1)}%`,
        icon: CheckCircle,
      },
      {
        label: "Total Cost",
        value: formatCurrency(data.totalCost),
        icon: DollarSign,
      },
      {
        label: "Avg Duration",
        value: formatDuration(data.averageDuration),
        icon: Clock,
      },
    ];

    return (
      <div className="space-y-6">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {stats.map((stat, index) => (
            <Card key={index}>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <stat.icon className="h-5 w-5 text-blue-600" />
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      {stat.label}
                    </p>
                    <p className="text-2xl font-bold">{stat.value}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Agent Type Distribution */}
        {data.callsByAgentType && (
          <Card>
            <CardHeader>
              <CardTitle>Calls by Agent Type</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={Object.entries(data.callsByAgentType).map(
                      ([type, count]) => ({ type, count })
                    )}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="type" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="count" fill="#166534" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    );
  };

  const renderAgentCalls = (data: any) => {
    if (!data?.data) return null;

    return (
      <div className="space-y-4">
        {data.data.slice(0, 5).map((call: any, index: number) => (
          <Card key={index}>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <div className="flex items-center space-x-2">
                    <Badge variant="outline">{call.agentType}</Badge>
                    <Badge variant="secondary">{call.callType}</Badge>
                    <Badge
                      variant={
                        call.status === "success" ? "default" : "destructive"
                      }
                    >
                      {call.status}
                    </Badge>
                  </div>
                  <p className="text-sm font-medium">
                    {call.modelProvider} - {call.modelName}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    {formatNumber(call.totalTokens)} tokens •{" "}
                    {formatCurrency(call.cost)}
                  </p>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium">
                    {formatDuration(call.duration)}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    {new Date(call.createdAt).toLocaleDateString()}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  };

  const renderDataVisualization = (result: TestResult) => {
    if (!result.data) return null;

    switch (result.name) {
      case "Analytics Summary":
        return renderAnalyticsSummary(result.data);
      case "User Analytics":
        return renderUserAnalytics(result.data);
      case "Activity Metrics":
        return renderActivityMetrics(result.data);
      case "Agent Usage Stats":
        return renderAgentUsageStats(result.data);
      case "Agent Calls":
        return renderAgentCalls(result.data);
      default:
        return (
          <div className="bg-muted/50 border rounded p-3 mt-2">
            <p className="text-sm text-muted-foreground mb-2">Raw Response:</p>
            <pre className="text-xs bg-background p-2 rounded border overflow-auto max-h-32">
              {JSON.stringify(result.data, null, 2)}
            </pre>
          </div>
        );
    }
  };

  const getResultsByCategory = (category: TestResult["category"]) => {
    return results.filter((result) => result.category === category);
  };

  const getOverallStats = () => {
    const total = results.length;
    const successful = results.filter((r) => r.status === "success").length;
    const failed = results.filter((r) => r.status === "error").length;
    const avgDuration =
      results.length > 0
        ? results.reduce((sum, r) => sum + (r.duration || 0), 0) /
          results.length
        : 0;

    return { total, successful, failed, avgDuration };
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Admin Analytics Dashboard</h1>
            <p className="text-muted-foreground">
              Test and visualize admin analytics API endpoints with enhanced
              data presentation.
            </p>
          </div>
          <div className="flex items-center space-x-4">
            {isRunning && (
              <div className="flex items-center space-x-2">
                <Progress
                  value={(currentTestIndex / endpoints.length) * 100}
                  className="w-32"
                  showValue
                />
                <span className="text-sm text-muted-foreground">
                  {currentTestIndex + 1}/{endpoints.length}
                </span>
              </div>
            )}
            <Button onClick={runTests} disabled={isRunning}>
              {isRunning ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <Play className="h-4 w-4 mr-2" />
              )}
              Run Tests
            </Button>
          </div>
        </div>

        {/* Overview Stats */}
        {results.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Test Overview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-4">
                <div className="flex items-center space-x-2">
                  <Database className="h-5 w-5 text-blue-600" />
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      Total Tests
                    </p>
                    <p className="text-2xl font-bold">
                      {getOverallStats().total}
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      Successful
                    </p>
                    <p className="text-2xl font-bold">
                      {getOverallStats().successful}
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <XCircle className="h-5 w-5 text-red-600" />
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      Failed
                    </p>
                    <p className="text-2xl font-bold">
                      {getOverallStats().failed}
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Clock className="h-5 w-5 text-purple-600" />
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      Avg Duration
                    </p>
                    <p className="text-2xl font-bold">
                      {formatDuration(getOverallStats().avgDuration)}
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Configuration Info */}
        <Card>
          <CardHeader>
            <CardTitle>Configuration</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-3">
              <div>
                <p className="text-sm font-medium">Admin API URL</p>
                <p className="text-sm text-muted-foreground">
                  {process.env.NEXT_PUBLIC_ADMIN_API_URL || "/api (proxied)"}
                </p>
              </div>
              <div>
                <p className="text-sm font-medium">Auth Token</p>
                <p className="text-sm text-muted-foreground">
                  {hasToken ? "✅ Present" : "❌ Missing"}
                </p>
              </div>
              <div>
                <p className="text-sm font-medium">Backend Target</p>
                <p className="text-sm text-muted-foreground">
                  {process.env.NEXT_PUBLIC_ADMIN_API_URL ||
                    "http://localhost:3002"}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Test Results with Tabs */}
        {results.length > 0 && (
          <Tabs defaultValue="overview" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="analytics">Analytics</TabsTrigger>
              <TabsTrigger value="agent">Agent Data</TabsTrigger>
              <TabsTrigger value="system">System</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>All Test Results</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {results.map((result, index) => (
                      <div key={index} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            {getCategoryIcon(result.category)}
                            {getStatusIcon(result.status)}
                            <span className="font-medium">{result.name}</span>
                            <Badge variant="outline">{result.category}</Badge>
                          </div>
                          <div className="flex items-center gap-2">
                            {result.duration && (
                              <span className="text-sm text-muted-foreground">
                                {formatDuration(result.duration)}
                              </span>
                            )}
                            {getStatusBadge(result.status)}
                          </div>
                        </div>

                        {result.error && (
                          <div className="bg-red-50 border border-red-200 rounded p-3 mt-2">
                            <p className="text-sm text-red-800">
                              {result.error}
                            </p>
                          </div>
                        )}

                        {result.data && result.status === "success" && (
                          <div className="mt-4">
                            {renderDataVisualization(result)}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="analytics" className="space-y-4">
              {getResultsByCategory("analytics").map((result, index) => (
                <Card key={index}>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      {getStatusIcon(result.status)}
                      {result.name}
                      {getStatusBadge(result.status)}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {result.error && (
                      <div className="bg-red-50 border border-red-200 rounded p-3 mb-4">
                        <p className="text-sm text-red-800">{result.error}</p>
                      </div>
                    )}
                    {result.data &&
                      result.status === "success" &&
                      renderDataVisualization(result)}
                  </CardContent>
                </Card>
              ))}
            </TabsContent>

            <TabsContent value="agent" className="space-y-4">
              {getResultsByCategory("agent").map((result, index) => (
                <Card key={index}>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      {getStatusIcon(result.status)}
                      {result.name}
                      {getStatusBadge(result.status)}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {result.error && (
                      <div className="bg-red-50 border border-red-200 rounded p-3 mb-4">
                        <p className="text-sm text-red-800">{result.error}</p>
                      </div>
                    )}
                    {result.data &&
                      result.status === "success" &&
                      renderDataVisualization(result)}
                  </CardContent>
                </Card>
              ))}
            </TabsContent>

            <TabsContent value="system" className="space-y-4">
              {getResultsByCategory("system").map((result, index) => (
                <Card key={index}>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      {getStatusIcon(result.status)}
                      {result.name}
                      {getStatusBadge(result.status)}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {result.error && (
                      <div className="bg-red-50 border border-red-200 rounded p-3 mb-4">
                        <p className="text-sm text-red-800">{result.error}</p>
                      </div>
                    )}
                    {result.data &&
                      result.status === "success" &&
                      renderDataVisualization(result)}
                  </CardContent>
                </Card>
              ))}
            </TabsContent>
          </Tabs>
        )}
      </div>
    </AdminLayout>
  );
}
