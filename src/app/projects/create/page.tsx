"use client";

import { ProjectCreationAnimation } from "@/components/project-creation/ProjectCreationAnimation";
import { useProjectCreationStore } from "@/stores/projectCreationStore";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

export default function ProjectCreatePage() {
  const router = useRouter();
  const { currentStep, reset } = useProjectCreationStore();

  // Redirect if not in creation flow
  useEffect(() => {
    if (currentStep === 'idle') {
      router.push('/user-dashboard');
    }
  }, [currentStep, router]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      // Don't reset here as we want to maintain state during navigation
    };
  }, []);

  return <ProjectCreationAnimation />;
}
