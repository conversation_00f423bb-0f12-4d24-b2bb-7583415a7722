"use client";

// Prevent static generation for this page
export const dynamic = 'force-dynamic';

import { zodResolver } from "@hookform/resolvers/zod";
import { ArrowLeft, Eye, EyeOff, Loader2, Lock, Shield } from "lucide-react";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { Suspense, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { AuthCard } from "@/components/auth/auth-card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { showErrorToast, showSuccessToast } from "@/hooks/useToast";
import { MockAuthAPI } from "@/lib/mock-auth-api";

const resetPasswordSchema = z
  .object({
    code: z
      .string()
      .min(6, "Verification code must be 6 digits")
      .max(6, "Verification code must be 6 digits"),
    newPassword: z.string().min(8, "Password must be at least 8 characters"),
    confirmPassword: z
      .string()
      .min(8, "Password must be at least 8 characters"),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"],
  });

type ResetPasswordFormData = z.infer<typeof resetPasswordSchema>;

function ResetPasswordForm() {
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [codeDigits, setCodeDigits] = useState<string[]>([
    "",
    "",
    "",
    "",
    "",
    "",
  ]);

  const router = useRouter();
  const searchParams = useSearchParams();
  const emailFromQuery = searchParams.get("email") || "";

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    reset,
  } = useForm<ResetPasswordFormData>({
    resolver: zodResolver(resetPasswordSchema),
    defaultValues: {
      code: "",
      newPassword: "",
      confirmPassword: "",
    },
  });

  // Clear form and code digits on component mount
  useEffect(() => {
    reset();
    setCodeDigits(["", "", "", "", "", ""]);
  }, [reset]);

  const onSubmit = async (data: ResetPasswordFormData) => {
    setIsLoading(true);

    try {
      // Use mock API for development
      await MockAuthAPI.resetPassword(
        emailFromQuery,
        data.code,
        data.newPassword
      );

      showSuccessToast("Password reset successful!", {
        description: "You can now log in with your new password",
      });

      // Redirect to login page after success
      setTimeout(() => {
        router.push("/auth/login");
      }, 2000);
    } catch (err: unknown) {
      const errorMessage =
        err instanceof Error
          ? err.message
          : "Failed to reset password. Please try again.";
      showErrorToast("Password reset failed", {
        description: errorMessage,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const resendCode = async () => {
    if (!emailFromQuery) return;

    setIsLoading(true);
    setError(null);

    try {
      // Use mock API for development
      await MockAuthAPI.forgotPassword(emailFromQuery);

      // Show success message briefly
      setError("Verification code sent successfully!");
      setTimeout(() => setError(null), 3000);
    } catch (err: unknown) {
      const errorMessage =
        err instanceof Error
          ? err.message
          : "Failed to resend code. Please try again.";
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  if (success) {
    return (
      <AuthCard
        title="Password reset successful!"
        header={
          <div className="mx-auto w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center">
            <Shield className="h-6 w-6 text-green-600 dark:text-green-400" />
          </div>
        }
      >
        <div className="text-center space-y-4">
          <div className="space-y-2">
            <p className="text-sm text-muted-foreground">
              You can now sign in with your new password.
            </p>
          </div>
          <p className="text-xs text-muted-foreground">
            Redirecting to login page...
          </p>
          <Link
            href="/auth/login"
            className="font-medium text-primary hover:underline"
          >
            Continue to login
          </Link>
        </div>
      </AuthCard>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="space-y-6">
        <AuthCard
      title="Reset your password"
      description="Enter the verification code sent to your email address"
      footer={
        <div className="text-center space-y-2">
          <p className="text-sm text-muted-foreground text-center">
            Didn't receive the code?{" "}
            <Button
              variant="link"
              className="px-0 font-normal"
              onClick={resendCode}
              disabled={isLoading || !emailFromQuery}
            >
              Resend code
            </Button>
          </p>
          <div className="flex w-full">
            <Link
              href="/auth/forgot-password"
              className="flex items-center text-sm font-medium text-primary hover:underline w-full pl-0"
              style={{ marginLeft: 0 }}
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to login
            </Link>
          </div>
        </div>
      }
    >
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        {error && (
          <div
            className={`p-3 text-sm border rounded-md ${
              error.includes("successfully")
                ? "text-green-700 bg-green-50 border-green-200 dark:text-green-400 dark:bg-green-900/20 dark:border-green-800"
                : "text-destructive bg-destructive/10 border-destructive/20"
            }`}
          >
            {error}
          </div>
        )}

        <div className="space-y-2">
          <p className="text-sm text-muted-foreground text-center">
            Enter the 6-digit code sent to your email
          </p>
          <div className="flex justify-center gap-2">
            {Array.from({ length: 6 }).map((_, index) => (
              <Input
                key={index}
                type="text"
                inputMode="numeric"
                maxLength={1}
                value={codeDigits[index]}
                autoComplete="off"
                autoCorrect="off"
                autoCapitalize="off"
                spellCheck={false}
                className={`w-12 h-12 text-center text-lg font-semibold ${
                  errors.code ? "border-destructive" : ""
                }`}
                onChange={(e) => {
                  const value = e.target.value.replace(/\D/g, "");

                  // Update the digits array
                  const newDigits = [...codeDigits];
                  newDigits[index] = value;
                  setCodeDigits(newDigits);

                  // Update form value
                  const newCode = newDigits.join("");
                  setValue("code", newCode);

                  // Auto-focus next input
                  if (index < 5 && value) {
                    const nextInput = (e.target as HTMLInputElement)
                      .parentElement?.children[index + 1] as HTMLInputElement;
                    nextInput?.focus();
                  }
                }}
                onKeyDown={(e) => {
                  if (e.key === "Backspace") {
                    if (!e.currentTarget.value && index > 0) {
                      // Move to previous input if current is empty
                      const prevInput = (e.target as HTMLInputElement)
                        .parentElement?.children[index - 1] as HTMLInputElement;
                      prevInput?.focus();
                    } else if (e.currentTarget.value) {
                      // Clear current input
                      const newDigits = [...codeDigits];
                      newDigits[index] = "";
                      setCodeDigits(newDigits);
                      setValue("code", newDigits.join(""));
                    }
                  }
                }}
                onPaste={(e) => {
                  e.preventDefault();
                  const pastedData = e.clipboardData
                    .getData("text")
                    .replace(/\D/g, "");
                  if (pastedData.length <= 6) {
                    const newDigits = pastedData
                      .split("")
                      .concat(Array(6).fill(""))
                      .slice(0, 6);
                    setCodeDigits(newDigits);
                    setValue("code", pastedData);

                    // Focus the next empty input or the last input
                    const nextEmptyIndex = Math.min(pastedData.length, 5);
                    const nextInput = (e.target as HTMLInputElement)
                      .parentElement?.children[
                      nextEmptyIndex
                    ] as HTMLInputElement;
                    nextInput?.focus();
                  }
                }}
              />
            ))}
          </div>
          <input type="hidden" {...register("code")} />
          {errors.code && (
            <p className="text-sm text-destructive text-center">
              {errors.code.message}
            </p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="newPassword">New password</Label>
          <div className="relative">
            <Lock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              id="newPassword"
              type={showNewPassword ? "text" : "password"}
              placeholder="Enter new password"
              autoComplete="new-password"
              {...register("newPassword")}
              className={`pl-10 pr-10 ${
                errors.newPassword ? "border-destructive" : ""
              }`}
            />
            <Button
              type="button"
              variant="ghost"
              size="sm"
              className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
              onClick={() => setShowNewPassword(!showNewPassword)}
            >
              {showNewPassword ? (
                <EyeOff className="h-4 w-4 text-muted-foreground" />
              ) : (
                <Eye className="h-4 w-4 text-muted-foreground" />
              )}
            </Button>
          </div>
          {errors.newPassword && (
            <p className="text-sm text-destructive">
              {errors.newPassword.message}
            </p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="confirmPassword">Confirm new password</Label>
          <div className="relative">
            <Lock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              id="confirmPassword"
              type={showConfirmPassword ? "text" : "password"}
              placeholder="Confirm new password"
              autoComplete="new-password"
              {...register("confirmPassword")}
              className={`pl-10 pr-10 ${
                errors.confirmPassword ? "border-destructive" : ""
              }`}
            />
            <Button
              type="button"
              variant="ghost"
              size="sm"
              className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
            >
              {showConfirmPassword ? (
                <EyeOff className="h-4 w-4 text-muted-foreground" />
              ) : (
                <Eye className="h-4 w-4 text-muted-foreground" />
              )}
            </Button>
          </div>
          {errors.confirmPassword && (
            <p className="text-sm text-destructive">
              {errors.confirmPassword.message}
            </p>
          )}
        </div>

        <Button type="submit" className="w-full" disabled={isLoading}>
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Resetting password...
            </>
          ) : (
            "Reset password"
          )}
        </Button>
      </form>
        </AuthCard>
      </div>
    </div>
  );
}

export default function ResetPasswordPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <ResetPasswordForm />
    </Suspense>
  );
}
