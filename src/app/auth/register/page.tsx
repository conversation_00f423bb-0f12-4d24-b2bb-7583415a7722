"use client";

// Prevent static generation for this page
export const dynamic = 'force-dynamic';

import { useSignUp } from "@clerk/nextjs";
import { zodResolver } from "@hookform/resolvers/zod";
import { Eye, EyeOff, Loader2, Mail, Lock, User, Home } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { AuthCard } from "@/components/auth/auth-card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import { Button } from "../../../components/ui/button";
import { SidebarButton } from "../../../components/ui/sidebar-button";
import { useSyncUserToBackend } from "@/hooks/mutations/useUserMutations";

const registerSchema = z.object({
  firstName: z.string().min(1, "First name is required"),
  lastName: z.string().min(1, "Last name is required"),
  email: z.string().email("Please enter a valid email address"),
  password: z.string().min(8, "Password must be at least 8 characters"),
});

type RegisterFormData = z.infer<typeof registerSchema>;

export default function RegisterPage() {
  const { isLoaded, signUp, setActive } = useSignUp();
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const router = useRouter();
  const syncUser = useSyncUserToBackend();

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<RegisterFormData>({
    resolver: zodResolver(registerSchema),
  });

  const onSubmit = async (data: RegisterFormData) => {
    if (!isLoaded) return;

    setIsLoading(true);
    try {
      console.log("Creating sign up with data:", {
        firstName: data.firstName,
        lastName: data.lastName,
        emailAddress: data.email,
        // Don't log password for security
      });

      // Try with just email and password first
      const result = await signUp.create({
        emailAddress: data.email,
        password: data.password,
      });

      // If successful, try to update the user profile with names
      if (result && (data.firstName || data.lastName)) {
        try {
          await signUp.update({
            firstName: data.firstName,
            lastName: data.lastName,
          });
          console.log("User profile updated with names");
        } catch (updateErr: any) {
          console.warn("Could not update user profile with names:", updateErr);
          // Don't fail the whole process if name update fails
        }
      }

      console.log("Sign up created successfully:", result);

      // Complete the sign up process directly (no email verification)
      if (result.status === "complete") {
        await setActive({ session: result.createdSessionId });

        // Sync user to backend (like Google login flow)
        try {
          console.log("Syncing user to backend...");
          await syncUser.mutateAsync();
          console.log("User synced to backend successfully");
        } catch (syncError: any) {
          console.warn("Failed to sync user to backend:", syncError);

          // Check if user already exists in backend
          if (syncError.message && syncError.message.includes("already exists")) {
            console.log("User already exists in backend, proceeding to dashboard");
            // User already exists in backend, that's fine - proceed normally
          } else {
            // Other sync errors - log but don't fail the process
            console.error("Unexpected sync error:", syncError);
          }
        }

        toast.success("Account created successfully!");
        router.push("/user-dashboard");
      } else {
        // If sign up is not complete, check what's needed
        console.log("Sign up status:", result.status);
        console.log("Sign up result:", result);

        // Handle different statuses
        if (result.status === "missing_requirements") {
          toast.error("Please complete all required fields and try again.");
        } else if (result.status === "abandoned") {
          toast.error("Sign up was abandoned. Please try again.");
        } else {
          // For any other status, just redirect to dashboard
          // The user might be created but needs additional verification
          console.log("Redirecting to dashboard despite incomplete status");
          toast.success("Account created successfully!");
          router.push("/user-dashboard");
        }
      }
    } catch (err: any) {
      console.error("Sign up error:", err);
      console.error("Error details:", {
        errors: err.errors,
        message: err.message,
        status: err.status,
      });

      // Enhanced user-friendly error handling
      let userMessage = "Sign up failed. Please try again.";

      if (err.errors && err.errors.length > 0) {
        const error = err.errors[0];

        // Handle specific error types with user-friendly messages
        switch (error.code) {
          case "form_identifier_exists":
            userMessage = "An account with this email already exists. Please try signing in instead.";
            break;
          case "form_password_pwned":
            userMessage = "This password has been found in a data breach. Please choose a stronger password.";
            break;
          case "form_password_length_too_short":
            userMessage = "Password must be at least 8 characters long.";
            break;
          case "form_password_validation_failed":
            userMessage = "Password is too weak. Please include uppercase, lowercase, numbers, and special characters.";
            break;
          case "form_param_format_invalid":
            if (error.meta?.param_name === "email_address") {
              userMessage = "Please enter a valid email address.";
            } else {
              userMessage = `Invalid ${error.meta?.param_name || 'field'}. Please check your input.`;
            }
            break;
          case "form_param_nil":
            userMessage = `${error.meta?.param_name || 'Required field'} is required.`;
            break;
          default:
            // Use the error message from Clerk if it's user-friendly
            if (error.message && !error.message.includes("first_name") && !error.message.includes("last_name")) {
              userMessage = error.message;
            } else if (error.message && (error.message.includes("first_name") || error.message.includes("last_name"))) {
              userMessage = "There was an issue with the name fields. Please try again or contact support.";
            }
        }
      } else if (err.message) {
        userMessage = err.message;
      }

      toast.error(userMessage);
    } finally {
      setIsLoading(false);
    }
  };



  const handleSocialSignUp = async (strategy: "oauth_google") => {
    if (!isLoaded) return;

    try {
      await signUp.authenticateWithRedirect({
        strategy,
        redirectUrl: "/sso-callback",
        redirectUrlComplete: "/user-dashboard",
      });
    } catch (err: any) {
      console.error("Social sign up error:", err);
      toast.error("Social sign up failed. Please try again.");
    }
  };



  return (
    <div className="min-h-screen flex flex-col items-center justify-center">
      <AuthCard
        title="Create your account"
        description="Get started with your free account"
        footer={
          <div className="text-center text-sm text-muted-foreground">
            Already have an account?{" "}
            <Link
              href="/auth/login"
              className="text-primary hover:text-primary/90 font-medium"
            >
              Sign in
            </Link>
          </div>
        }
      >
        {/* Social Sign Up Buttons */}
        <div className="w-full">
          <SidebarButton
            variant="outline"
            className="w-full"
            layout="horizontal"
            hoverColor="green"
            hoverScale={true}
            showBorder={true}
            borderClassName="grey border-1"
            onClick={() => handleSocialSignUp("oauth_google")}
            disabled={!isLoaded}
          >
            <svg className="mr-2 h-4 w-4" viewBox="0 0 24 24">
              <path
                fill="currentColor"
                d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
              />
              <path
                fill="currentColor"
                d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
              />
              <path
                fill="currentColor"
                d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
              />
              <path
                fill="currentColor"
                d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
              />
            </svg>
            Google
          </SidebarButton>
        </div>

        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <Separator className="w-full" />
          </div>
          <div className="relative flex justify-center text-xs uppercase">
            <span className="bg-background px-2 text-muted-foreground">
              Or continue with email
            </span>
          </div>
        </div>

        {/* Registration Form */}
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="firstName">First name</Label>
              <div className="relative">
                <User className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  id="firstName"
                  type="text"
                  placeholder="John"
                  {...register("firstName")}
                  className={`pl-10 ${errors.firstName ? "border-destructive" : ""}`}
                />
              </div>
              {errors.firstName && (
                <p className="text-sm text-destructive">{errors.firstName.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="lastName">Last name</Label>
              <div className="relative">
                <User className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  id="lastName"
                  type="text"
                  placeholder="Doe"
                  {...register("lastName")}
                  className={`pl-10 ${errors.lastName ? "border-destructive" : ""}`}
                />
              </div>
              {errors.lastName && (
                <p className="text-sm text-destructive">{errors.lastName.message}</p>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="email">Email address</Label>
            <div className="relative">
              <Mail className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                {...register("email")}
                className={`pl-10 ${errors.email ? "border-destructive" : ""}`}
              />
            </div>
            {errors.email && (
              <p className="text-sm text-destructive">{errors.email.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="password">Password</Label>
            <div className="relative">
              <Lock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                id="password"
                type={showPassword ? "text" : "password"}
                placeholder="Create a strong password"
                {...register("password")}
                className={`pl-10 pr-10 ${errors.password ? "border-destructive" : ""}`}
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1.5 h-4 w-4 text-muted-foreground hover:text-foreground"
              >
                {showPassword ? <EyeOff /> : <Eye />}
              </button>
            </div>
            {errors.password && (
              <p className="text-sm text-destructive">{errors.password.message}</p>
            )}
          </div>



          <Button type="submit" className="w-full" disabled={isLoading || !isLoaded}>
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Creating account...
              </>
            ) : (
              "Create account"
            )}
          </Button>
        </form>
      </AuthCard>

      {/* Home link - Outside the card */}
      <div className="text-center mt-6">
        <Link
          href="/"
          className="inline-flex items-center gap-2 text-sm text-muted-foreground hover:text-foreground transition-colors"
        >
          <Home className="w-4 h-4" />
          Back to home
        </Link>
      </div>
    </div>
  );
}
