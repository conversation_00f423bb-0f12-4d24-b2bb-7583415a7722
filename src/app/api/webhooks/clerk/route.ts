import { headers } from "next/headers";
import { NextRequest, NextResponse } from "next/server";
import { Webhook } from "svix";

const webhookSecret = process.env.CLERK_WEBHOOK_SECRET;

async function handler(req: NextRequest) {
  if (!webhookSecret) {
    throw new Error("Please add CLERK_WEBHOOK_SECRET from Clerk Dashboard to .env or .env.local");
  }

  // Get the headers
  const headerPayload = await headers();
  const svix_id = headerPayload.get("svix-id");
  const svix_timestamp = headerPayload.get("svix-timestamp");
  const svix_signature = headerPayload.get("svix-signature");

  // If there are no headers, error out
  if (!svix_id || !svix_timestamp || !svix_signature) {
    return new Response("Error occured -- no svix headers", {
      status: 400,
    });
  }

  // Get the body
  const payload = await req.json();
  const body = JSON.stringify(payload);

  // Create a new Svix instance with your secret.
  const wh = new Webhook(webhookSecret);

  let evt: any;

  // Verify the payload with the headers
  try {
    evt = wh.verify(body, {
      "svix-id": svix_id,
      "svix-timestamp": svix_timestamp,
      "svix-signature": svix_signature,
    }) as any;
  } catch (err) {
    console.error("Error verifying webhook:", err);
    return new Response("Error occured", {
      status: 400,
    });
  }

  // Handle the webhook
  const eventType = evt.type;
  
  if (eventType === "user.created") {
    const { id, email_addresses, first_name, last_name, image_url } = evt.data;
    
    // Create user in your backend
    try {
      const userData = {
        email: email_addresses[0]?.email_address || "",
        firstName: first_name || "",
        lastName: last_name || "",
        clerkId: id,
        role: "user",
        status: "active",
        avatarUrl: image_url || "",
        bio: "",
        timezone: "UTC",
        preferences: {
          notifications: true,
          theme: "system",
          language: "en"
        }
      };

      // Send to your backend API
      const backendUrl = process.env.NEXT_PUBLIC_ADMIN_API_URL || "http://localhost:3002";
      const response = await fetch(`${backendUrl}/api/users`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          // Add any authentication headers your backend requires
        },
        body: JSON.stringify(userData),
      });

      if (!response.ok) {
        console.error("Failed to create user in backend:", await response.text());
      } else {
        console.log("User created successfully in backend");
      }
    } catch (error) {
      console.error("Error creating user in backend:", error);
    }
  }

  if (eventType === "user.updated") {
    const { id, email_addresses, first_name, last_name, image_url } = evt.data;

    // Update user in your backend
    try {
      // First, get the user ID from your backend using Clerk ID
      const backendUrl = process.env.NEXT_PUBLIC_ADMIN_API_URL || "http://localhost:3000";

      // Get user by Clerk ID to find the internal user ID
      const getUserResponse = await fetch(`${backendUrl}/api/users/clerk/${id}`);

      if (getUserResponse.ok) {
        const existingUser = await getUserResponse.json();

        const userData = {
          email: email_addresses[0]?.email_address || "",
          firstName: first_name || "",
          lastName: last_name || "",
          clerkId: id,
          role: existingUser.role || "user",
          status: existingUser.status || "active",
          avatarUrl: image_url || "",
          bio: existingUser.bio || "",
          timezone: existingUser.timezone || "UTC",
          preferences: existingUser.preferences || {
            notifications: true,
            theme: "system",
            language: "en"
          }
        };

        // Update user using the internal user ID
        const response = await fetch(`${backendUrl}/api/users/${existingUser.id}`, {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
            // Add any authentication headers your backend requires
          },
          body: JSON.stringify(userData),
        });

        if (!response.ok) {
          console.error("Failed to update user in backend:", await response.text());
        } else {
          console.log("User updated successfully in backend");
        }
      } else {
        console.error("User not found in backend for Clerk ID:", id);
      }
    } catch (error) {
      console.error("Error updating user in backend:", error);
    }
  }

  if (eventType === "user.deleted") {
    const { id } = evt.data;
    
    // Delete user from your backend
    try {
      const backendUrl = process.env.NEXT_PUBLIC_ADMIN_API_URL || "http://localhost:3002";
      const response = await fetch(`${backendUrl}/api/users/clerk/${id}`, {
        method: "DELETE",
        headers: {
          // Add any authentication headers your backend requires
        },
      });

      if (!response.ok) {
        console.error("Failed to delete user in backend:", await response.text());
      } else {
        console.log("User deleted successfully in backend");
      }
    } catch (error) {
      console.error("Error deleting user in backend:", error);
    }
  }

  return NextResponse.json({ message: "Webhook processed successfully" });
}

export const GET = handler;
export const POST = handler;
export const PUT = handler;
