import { NextRequest, NextResponse } from 'next/server';

// Mock user data
const mockUsers = [
  {
    id: "user1",
    email: "<EMAIL>",
    name: "<PERSON>",
    role: "user",
    password: "password123", // In real app, this would be hashed
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-20')
  },
  {
    id: "admin1",
    email: "<EMAIL>",
    name: "Admin User",
    role: "admin",
    password: "123456789@", // In real app, this would be hashed
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-20')
  }
];

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email, password } = body;

    // Validate required fields
    if (!email || !password) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Email and password are required' 
        },
        { status: 400 }
      );
    }

    // Find user by email
    const user = mockUsers.find(u => u.email === email);
    
    if (!user || user.password !== password) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid credentials' 
        },
        { status: 401 }
      );
    }

    // Generate mock tokens (in real app, use proper JWT)
    const accessToken = `mock_access_token_${user.id}_${Date.now()}`;
    const refreshToken = `mock_refresh_token_${user.id}_${Date.now()}`;

    // Return user data without password
    const { password: _, ...userWithoutPassword } = user;

    return NextResponse.json({
      success: true,
      data: {
        user: userWithoutPassword,
        accessToken,
        refreshToken
      }
    });
  } catch (error) {
    console.error('Error signing in:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to sign in' 
      },
      { status: 500 }
    );
  }
}
