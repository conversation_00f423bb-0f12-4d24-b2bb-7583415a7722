import { NextRequest, NextResponse } from 'next/server';

// Mock user data
const mockUser = {
  id: "user1",
  email: "<EMAIL>",
  name: "<PERSON>",
  role: "user",
  createdAt: new Date('2024-01-01'),
  updatedAt: new Date('2024-01-20')
};

export async function GET(request: NextRequest) {
  try {
    // In a real app, you would:
    // 1. Extract and verify JWT token from Authorization header
    // 2. Get user ID from token
    // 3. Fetch user from database
    
    const authHeader = request.headers.get('authorization');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Unauthorized' 
        },
        { status: 401 }
      );
    }

    // For now, return mock user data
    return NextResponse.json({
      success: true,
      data: mockUser
    });
  } catch (error) {
    console.error('Error fetching user:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch user' 
      },
      { status: 500 }
    );
  }
}
