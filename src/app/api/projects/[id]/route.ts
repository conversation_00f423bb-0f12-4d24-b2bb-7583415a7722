import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';

const BACKEND_URL = process.env.NEXT_PUBLIC_ADMIN_API_URL || process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3002';
const STRICT_BACKEND = process.env.NEXT_PUBLIC_STRICT_BACKEND !== 'false';

// No mock fallback data allowed

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { getToken } = await auth();
    const token = await getToken();

    if (!token) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Unauthorized' 
        },
        { status: 401 }
      );
    }

    const { id } = await params;

    // Try to fetch from real backend
    try {
      const response = await fetch(`${BACKEND_URL}/api/projects/${id}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const project = await response.json();
        return NextResponse.json({
          success: true,
          data: project
        });
      } else if (STRICT_BACKEND) {
        return NextResponse.json(
          { success: false, error: 'Backend error' },
          { status: response.status || 502 }
        );
      }
    } catch (backendError) {
      console.warn('Backend unavailable:', backendError);
      if (STRICT_BACKEND) {
        return NextResponse.json(
          { success: false, error: 'Backend unavailable' },
          { status: 502 }
        );
      }
    }
    
    return NextResponse.json(
      { success: false, error: 'Backend unavailable' },
      { status: 502 }
    );
  } catch (error) {
    console.error('Error fetching project:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch project' 
      },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { getToken } = await auth();
    const token = await getToken();

    if (!token) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Unauthorized' 
        },
        { status: 401 }
      );
    }

    const { id } = await params;
    const body = await request.json();

    // Try to update in real backend
    try {
      const response = await fetch(`${BACKEND_URL}/api/projects/${id}`, {
        method: 'PATCH', // Backend uses PATCH according to OpenAPI spec
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
      });

      if (response.ok) {
        const project = await response.json();
        return NextResponse.json({
          success: true,
          data: project
        });
      } else if (STRICT_BACKEND) {
        return NextResponse.json(
          { success: false, error: 'Backend error' },
          { status: response.status || 502 }
        );
      }
    } catch (backendError) {
      console.warn('Backend unavailable:', backendError);
      if (STRICT_BACKEND) {
        return NextResponse.json(
          { success: false, error: 'Backend unavailable' },
          { status: 502 }
        );
      }
    }
    
    return NextResponse.json(
      { success: false, error: 'Backend unavailable' },
      { status: 502 }
    );
  } catch (error) {
    console.error('Error updating project:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to update project' 
      },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { getToken } = await auth();
    const token = await getToken();

    if (!token) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Unauthorized' 
        },
        { status: 401 }
      );
    }

    const { id } = await params;

    // Try to delete from real backend
    try {
      const response = await fetch(`${BACKEND_URL}/api/projects/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        return NextResponse.json({
          success: true,
          message: 'Project deleted successfully'
        });
      } else if (STRICT_BACKEND) {
        return NextResponse.json(
          { success: false, error: 'Backend error' },
          { status: response.status || 502 }
        );
      }
    } catch (backendError) {
      console.warn('Backend unavailable:', backendError);
      if (STRICT_BACKEND) {
        return NextResponse.json(
          { success: false, error: 'Backend unavailable' },
          { status: 502 }
        );
      }
    }
    
    return NextResponse.json(
      { success: false, error: 'Backend unavailable' },
      { status: 502 }
    );
  } catch (error) {
    console.error('Error deleting project:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to delete project' 
      },
      { status: 500 }
    );
  }
}
