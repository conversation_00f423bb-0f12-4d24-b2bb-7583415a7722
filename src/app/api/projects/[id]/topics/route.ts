import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';

const BACKEND_URL = process.env.NEXT_PUBLIC_ADMIN_API_URL || process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3002';
const STRICT_BACKEND = process.env.NEXT_PUBLIC_STRICT_BACKEND !== 'false';

export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { getToken } = await auth();
    const token = await getToken();
    if (!token) {
      return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await params;
    const url = new URL(_request.url);
    const search = url.search; // includes leading '?', empty if none
    try {
      const response = await fetch(`${BACKEND_URL}/api/projects/${id}/topics${search}`, {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        return NextResponse.json({ success: true, data });
      } else if (STRICT_BACKEND) {
        return NextResponse.json({ success: false, error: 'Backend error' }, { status: response.status || 502 });
      }
    } catch (err) {
      if (STRICT_BACKEND) {
        return NextResponse.json({ success: false, error: 'Backend unavailable' }, { status: 502 });
      }
    }

    return NextResponse.json({ success: false, error: 'Fallback disabled' }, { status: 502 });
  } catch (error) {
    return NextResponse.json({ success: false, error: 'Failed to fetch topics' }, { status: 500 });
  }
}


