import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';

const BACKEND_URL = process.env.NEXT_PUBLIC_ADMIN_API_URL || process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3002';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ projectId: string }> }
) {
  try {
    const { getToken } = await auth();
    const token = await getToken();
    if (!token) return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 });

    const { projectId } = await params;
    console.log('[AI Project Sessions] Getting sessions for project:', projectId);

    const resp = await fetch(`${BACKEND_URL}/api/ai-chat/projects/${projectId}/sessions`, {
      method: 'GET',
      headers: { 
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
    });

    console.log('[AI Project Sessions] Backend response status:', resp.status, resp.statusText);
    
    if (resp.ok) {
      const data = await resp.json();
      console.log('[AI Project Sessions] Backend response data:', data);
      return NextResponse.json(data);
    } else {
      const errorText = await resp.text();
      console.error('[AI Project Sessions] Backend error:', errorText);
      return NextResponse.json({ 
        success: false, 
        error: `Backend error: ${resp.status} ${resp.statusText}` 
      }, { status: resp.status });
    }
  } catch (e) {
    console.error('[AI Project Sessions] Error:', e);
    return NextResponse.json({ 
      success: false, 
      error: 'Failed to get project sessions' 
    }, { status: 500 });
  }
}
