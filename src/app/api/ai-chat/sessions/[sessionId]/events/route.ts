import { NextRequest } from 'next/server';
import { auth } from '@clerk/nextjs/server';

const BACKEND_URL = process.env.NEXT_PUBLIC_ADMIN_API_URL || process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3002';

// Proxy SSE so we can rely on cookie-based auth (Clerk) without needing custom headers support
export async function GET(_request: NextRequest, { params }: { params: Promise<{ sessionId: string }> }) {
  const { getToken } = await auth();
  const token = await getToken();

  const { sessionId } = await params;

  const targetUrl = `${BACKEND_URL}/api/ai-chat/sessions/${sessionId}/events`;

  const responseStream = new ReadableStream({
    async start(controller) {
      const abort = new AbortController();
      try {
        const resp = await fetch(targetUrl, {
          method: 'GET',
          headers: token ? { Authorization: `Bearer ${token}` } : undefined,
          signal: abort.signal,
        });

        if (!resp.ok || !resp.body) {
          controller.close();
          return;
        }

        const reader = resp.body.getReader();
        while (true) {
          const { value, done } = await reader.read();
          if (done) break;
          if (value) controller.enqueue(value);
        }
        controller.close();
      } catch (_e) {
        controller.close();
      }
      return () => abort.abort();
    },
  });

  return new Response(responseStream, {
    headers: {
      'Content-Type': 'text/event-stream; charset=utf-8',
      'Cache-Control': 'no-cache, no-transform',
      Connection: 'keep-alive',
      'X-Accel-Buffering': 'no',
    },
  });
}


