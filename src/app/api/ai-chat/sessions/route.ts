import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';

const BACKEND_URL = process.env.NEXT_PUBLIC_ADMIN_API_URL || process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3002';

export async function POST(request: NextRequest) {
  try {
    const { getToken } = await auth();
    const token = await getToken();
    if (!token) return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 });

    const raw = await request.json().catch(() => ({} as any));
    // Normalize expected payload; backend expects { projectId: string }
    const projectId = raw?.projectId || raw?.project_id || raw?.id;
    console.log('[AI Sessions] Creating session for project:', projectId);
    console.log('[AI Sessions] Backend URL:', BACKEND_URL);
    
    if (!projectId || typeof projectId !== 'string') {
      return NextResponse.json({ success: false, error: 'projectId is required' }, { status: 400 });
    }

    const resp = await fetch(`${BACKEND_URL}/api/ai-chat/sessions`, {
      method: 'POST',
      headers: { Authorization: `Bearer ${token}`, 'Content-Type': 'application/json' },
      body: JSON.stringify({ projectId }),
    });
    
    console.log('[AI Sessions] Backend response status:', resp.status, resp.statusText);
    const data = await resp.json().catch(() => ({}));
    console.log('[AI Sessions] Backend response data:', data);
    return NextResponse.json(data, { status: resp.status });
  } catch (e) {
    return NextResponse.json({ success: false, error: 'Failed to create session' }, { status: 500 });
  }
}

export async function GET() {
  try {
    const { getToken } = await auth();
    const token = await getToken();
    if (!token) return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 });

    const resp = await fetch(`${BACKEND_URL}/api/ai-chat/sessions`, {
      method: 'GET',
      headers: { Authorization: `Bearer ${token}` },
    });
    const data = await resp.json().catch(() => ({}));
    return NextResponse.json(data, { status: resp.status });
  } catch (e) {
    return NextResponse.json({ success: false, error: 'Failed to get sessions' }, { status: 500 });
  }
}


