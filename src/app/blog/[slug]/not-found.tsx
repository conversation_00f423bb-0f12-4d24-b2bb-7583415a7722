import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON> } from "@/components/layout/header";
import { Footer } from "@/components/layout/footer";
import { ArrowLeft, FileText } from "lucide-react";

export default function BlogPostNotFound() {
  return (
    <div className="min-h-screen flex flex-col bg-background">
      <Header />
      <main className="flex-1 flex items-center justify-center">
        <div className="container mx-auto px-4 py-16 text-center">
          <div className="max-w-md mx-auto">
            <div className="w-24 h-24 mx-auto mb-6 rounded-full bg-muted flex items-center justify-center">
              <FileText className="w-12 h-12 text-muted-foreground" />
            </div>
            
            <h1 className="text-3xl font-bold text-foreground mb-4">
              Blog Post Not Found
            </h1>
            
            <p className="text-muted-foreground mb-8">
              The blog post you're looking for doesn't exist or may have been moved.
            </p>
            
            <div className="space-y-4">
              <Link href="/blog">
                <Button className="bg-[#166534] hover:bg-[#166534]/90 text-white">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Blog
                </Button>
              </Link>
              
              <div>
                <Link href="/">
                  <Button variant="outline">
                    Go to Homepage
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
}
