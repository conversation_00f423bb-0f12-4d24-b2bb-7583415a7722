import { notFound } from "next/navigation";
import { getPostBySlug, getAllPosts } from "@/data/blog-posts";
import { generateSEOMetadata } from "@/components/seo/SEOHead";
import { StructuredData, structuredDataSchemas } from "@/components/seo/StructuredData";
import { Header } from "@/components/layout/header";
import { Footer } from "@/components/layout/footer";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Calendar, Clock, ArrowLeft, Share2 } from "lucide-react";
import Link from "next/link";
import Image from "next/image";

interface BlogPostPageProps {
  params: Promise<{
    slug: string;
  }>;
}

// Generate static params for all blog posts
export async function generateStaticParams() {
  const posts = getAllPosts();
  return posts.map((post) => ({
    slug: post.slug,
  }));
}

// Generate metadata for each blog post
export async function generateMetadata({ params }: BlogPostPageProps) {
  const { slug } = await params;
  const post = getPostBySlug(slug);
  
  if (!post) {
    return generateSEOMetadata({
      title: "Post Not Found",
      description: "The requested blog post could not be found.",
      noIndex: true,
    });
  }

  return generateSEOMetadata({
    title: post.title,
    description: post.description,
    keywords: ["business", "entrepreneurship", "startup", "founders"],
    url: `/blog/${slug}`,
    type: "article",
    publishedTime: post.publishedAt,
    modifiedTime: post.updatedAt,
    author: "Siift Team",
    image: post.image,
  });
}

export default async function BlogPostPage({ params }: BlogPostPageProps) {
  const { slug } = await params;
  const post = getPostBySlug(slug);

  if (!post) {
    notFound();
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  // Article structured data
  const articleStructuredData = structuredDataSchemas.article({
    title: post.title,
    description: post.description,
    author: "Siift Team",
    publishedTime: post.publishedAt,
    modifiedTime: post.updatedAt || post.publishedAt,
    image: post.image || "https://siift.app/images/og-image.png",
    url: `https://siift.app/blog/${slug}`,
    publisher: {
      name: "Siift",
      logo: "https://siift.app/images/logo.png",
    },
  });

  // Breadcrumb structured data
  const breadcrumbStructuredData = structuredDataSchemas.breadcrumb([
    { name: "Home", url: "https://siift.app" },
    { name: "Blog", url: "https://siift.app/blog" },
    { name: post.title, url: `https://siift.app/blog/${slug}` },
  ]);

  return (
    <>
      <StructuredData data={articleStructuredData} />
      <StructuredData data={breadcrumbStructuredData} />
      <div className="min-h-screen flex flex-col bg-background">
        <Header />
        <main className="flex-1">
          <article className="container mx-auto px-4 py-12 max-w-4xl">
            {/* Back to Blog */}
            <div className="mb-8">
              <Link href="/blog">
                <Button variant="sidebar-outline" className="pl-0" >
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Blog
                </Button>
              </Link>
            </div>

            {/* Featured Image */}
            {post.image && (
              <div className="relative w-full h-64 md:h-96 mb-8 rounded-lg overflow-hidden">
                <Image
                  src={post.image}
                  alt={post.title}
                  fill
                  className="object-cover"
                  priority
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 1200px"
                />
              </div>
            )}

            {/* Article Header */}
            <header className="mb-8">
              {post.featured && (
                <Badge className="mb-4 bg-[#166534] hover:bg-[#166534]/90">
                  Featured Post
                </Badge>
              )}
              
              <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-foreground mb-6 leading-tight">
                {post.title}
              </h1>
              
              <p className="text-xl text-muted-foreground mb-6 leading-relaxed">
                {post.description}
              </p>

              {/* Meta Information */}
              <div className="flex flex-wrap items-center gap-6 text-sm text-muted-foreground mb-6">
                <div className="flex items-center gap-2">
                  <Calendar className="w-4 h-4" />
                  <span>{formatDate(post.publishedAt)}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Clock className="w-4 h-4" />
                  <span>{post.readTime} min read</span>
                </div>
              </div>


            </header>

            {/* Article Content */}
            <div className="prose prose-lg max-w-none">
              <div className="whitespace-pre-wrap">
                {post.content}
              </div>
            </div>

            {/* Article Footer */}
            <footer className="mt-12 pt-8 border-t">
              <div className="text-center">
                <Link href="/blog">
                  <Button className="bg-[#166534] hover:bg-[#166534]/90 text-white">
                    Read More Articles
                  </Button>
                </Link>
              </div>
            </footer>
          </article>
        </main>
        <Footer />
      </div>
    </>
  );
}
