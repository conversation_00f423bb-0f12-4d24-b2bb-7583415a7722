/**
 * Mock data for business item questions and answers
 * Each business item type has specific questions and default answers
 */

export interface BusinessItemQD {
  question: string;
  description: string;
}

// Minimal hardcoded map for questions/descriptions. Replace with backend later.
export const HARD_CODED_BUSINESS_ITEM_QD: Record<string, BusinessItemQD> = {
  problem: {
    question: "What does {PROJECT NAME} solve?",
    description:
      "Define the core problem your users face and why it matters. Clarify the pain, scope, frequency, and context so the solution is anchored to a real need.",
  },
  "unique-value-proposition": {
    question: "What’s unique about what {PROJECT NAME} offers?",
    description:
      "Explain the differentiators that make your product difficult to copy. Focus on defensibility, clear value, and why users switch or adopt.",
  },
  audience: {
    question: "Who is {PROJECT NAME} for?",
    description:
      "Identify the ideal customers and segments. Include characteristics, jobs-to-be-done, and key triggers that lead them to your solution.",
  },
  alternatives: {
    question: "What do users do today instead of {PROJECT NAME}?",
    description:
      "List existing behaviors and comparable tools. Capture tradeoffs and reasons users stick with the status quo.",
  },
  "market-size": {
    question: "How big is the opportunity for {PROJECT NAME}?",
    description:
      "Estimate the addressable market and growth vectors. Include assumptions and the near-term reachable segment.",
  },
  trends: {
    question: "What market signals support {PROJECT NAME} now?",
    description:
      "Show shifts in behavior, technology, regulation, or costs that make your solution timely.",
  },
  why: {
    question: "Why build {PROJECT NAME} now?",
    description:
      "Articulate the founder insight and motivation. Tie to observed patterns and a durable opportunity.",
  },
  advantages: {
    question: "What advantages does {PROJECT NAME} have?",
    description:
      "Highlight unfair advantages: distribution, data, partnerships, or IP that compound over time.",
  },
  product: {
    question: "What does {PROJECT NAME} deliver?",
    description:
      "Outline the core product, use cases, and the narrow wedge you’ll win first.",
  },
  tech: {
    question: "What powers {PROJECT NAME}?",
    description:
      "Summarize the technical approach, constraints, and enabling infrastructure.",
  },
  packages: {
    question: "What are the core packages or modules?",
    description:
      "List user-facing components and internal building blocks that create the product experience.",
  },
  positioning: {
    question: "How is {PROJECT NAME} positioned?",
    description:
      "Define the who, what, and why you win. Clarify the category, promise, and narrative.",
  },
  channels: {
    question: "How will {PROJECT NAME} reach users?",
    description:
      "Outline acquisition channels, go-to-market motions, and early experiments to validate fit.",
  },
  messaging: {
    question: "What should {PROJECT NAME} say?",
    description:
      "Provide messaging pillars and proof points. Keep it simple, specific, and memorable.",
  },
};
