import { clerkMiddleware, createRouteMatcher } from "@clerk/nextjs/server";
import { NextResponse } from "next/server";

const isProtectedRoute = createRouteMatcher([
  "/user-dashboard(.*)",
  "/admin(.*)",
  "/profile(.*)",
  "/projects(.*)",
  "/settings(.*)",
]);

export default clerkMiddleware(async (auth, req) => {
  const url = new URL(req.url);

  // Require beta gate for auth and protected routes
  const requiresBeta =
    url.pathname.startsWith("/auth") ||
    url.pathname.startsWith("/user-dashboard") ||
    url.pathname.startsWith("/admin") ||
    url.pathname.startsWith("/profile") ||
    url.pathname.startsWith("/projects") ||
    url.pathname.startsWith("/settings");

  if (requiresBeta) {
    const betaAccess = req.cookies.get("beta_access")?.value;
    if (betaAccess !== "1") {
      const redirectUrl = new URL("/", req.url);
      return NextResponse.redirect(redirectUrl);
    }
  }

  if (isProtectedRoute(req)) {
    await auth.protect();
  }
});

export const config = {
  matcher: ["/((?!.*\\..*|_next).*)", "/", "/(api|trpc)(.*)"],
};
