# Design System Documentation

## Overview

This document outlines the centralized design system for the Siift application, ensuring consistency across all components and themes.

## Brand Colors (From Logo)

Our brand colors are derived directly from the logo design:

| Color | Hex | CSS Variable | Usage |
|-------|-----|--------------|-------|
| Light Green Ring | `#b4fd98` | `--brand-light` | Hover states, highlights |
| Border Green | `#73ed47` | `--brand-medium` | Primary actions, main brand color |
| Dark Green Crescent | `#0A4000` | `--brand-dark` | Emphasis, important sections |
| White Circle | `#fff` | `--brand-white` | Text on dark backgrounds |

## Color System

### Primary Colors
- `--primary`: Main brand green (`#73ed47`)
- `--primary-light`: Light brand green (`#b4fd98`)
- `--primary-dark`: Dark brand green (`#0A4000`)
- `--primary-foreground`: White text on primary

### Progress Bar Colors
- `--progress-bg`: Background color (light in light theme, dark in dark theme)
- `--progress-fill`: Fill color (brand green)
- `--progress-border`: Border color (brand green)
- `--progress-text`: Text color (dark in light theme, light in dark theme)

### Semantic Colors
- `--destructive`: Red for errors/danger
- `--destructive-foreground`: White text on destructive
- `--accent`: Brand green for highlights
- `--accent-foreground`: White text on accent

## Best Practices

### 1. Use CSS Variables
Always use CSS variables instead of hardcoded colors:

```css
/* ✅ Good */
.button {
  background-color: var(--primary);
  color: var(--primary-foreground);
}

/* ❌ Bad */
.button {
  background-color: #73ed47;
  color: #fff;
}
```

### 2. Theme Consistency
All colors automatically adapt to light/dark themes:

```css
/* Automatically handles both themes */
.progress-bar {
  background-color: var(--progress-bg);
  border-color: var(--progress-border);
}
```

### 3. Component-Specific Colors
Use semantic color variables for specific use cases:

```css
/* Progress bars */
.progress {
  background-color: var(--progress-bg);
  border-color: var(--progress-border);
}

.progress-fill {
  background-color: var(--progress-fill);
}

.progress-text {
  color: var(--progress-text);
}
```

## File Structure

```
src/styles/
├── design-tokens.css    # Main design tokens (colors, typography, spacing)
├── design-system.md     # This documentation file
└── globals.css         # Global styles and utilities
```

## Usage Guidelines

### For Developers

1. **Always use CSS variables** from `design-tokens.css`
2. **Test in both light and dark themes**
3. **Use semantic color names** (e.g., `--primary` instead of `--brand-medium`)
4. **Follow the component patterns** established in the codebase

### For Designers

1. **Update colors in `design-tokens.css`** to change the entire system
2. **Use OKLCH color space** for better perceptual uniformity
3. **Maintain contrast ratios** for accessibility
4. **Test color changes** across all components

## Component Examples

### Progress Bar
```tsx
// Using the centralized progress colors
<div className="w-full bg-[var(--progress-bg)] border border-[var(--progress-border)] rounded-full h-2">
  <div 
    className="bg-[var(--progress-fill)] h-2 rounded-full transition-all"
    style={{ width: `${percentage}%` }}
  />
</div>
```

### Button
```tsx
// Using primary brand colors
<button className="bg-[var(--primary)] hover:bg-[var(--primary-dark)] text-[var(--primary-foreground)]">
  Click me
</button>
```

### Card
```tsx
// Using surface colors that adapt to theme
<div className="bg-[var(--card)] text-[var(--card-foreground)] border border-[var(--border)]">
  Card content
</div>
```

## Migration Guide

### From Hardcoded Colors
If you find hardcoded colors in the codebase:

1. **Identify the color's purpose** (primary, accent, background, etc.)
2. **Replace with appropriate CSS variable**:
   - `#73ed47` → `var(--primary)`
   - `#b4fd98` → `var(--primary-light)`
   - `#0A4000` → `var(--primary-dark)`
3. **Test in both themes**
4. **Update any related components**

### Example Migration
```css
/* Before */
.button {
  background-color: #73ed47;
  color: #fff;
}

/* After */
.button {
  background-color: var(--primary);
  color: var(--primary-foreground);
}
```

## Accessibility

### Color Contrast
- All color combinations meet WCAG AA standards
- OKLCH color space ensures perceptual uniformity
- High contrast ratios maintained in both themes

### Focus States
- Use `--ring` color for focus indicators
- Ensure focus rings are clearly visible
- Test with keyboard navigation

## Future Considerations

### Adding New Colors
1. Add to `design-tokens.css` with OKLCH values
2. Include both light and dark theme variants
3. Update this documentation
4. Test across all components

### Color Palette Expansion
When expanding the color palette:
1. Maintain brand consistency
2. Use semantic naming
3. Ensure accessibility compliance
4. Document usage guidelines

## Resources

- [OKLCH Color Space](https://oklch.com/) - Understanding the color format
- [WCAG Guidelines](https://www.w3.org/WAI/WCAG21/quickref/) - Accessibility standards
- [CSS Custom Properties](https://developer.mozilla.org/en-US/docs/Web/CSS/Using_CSS_custom_properties) - CSS variables documentation

---

**Last Updated**: [Current Date]
**Version**: 1.0
**Maintainer**: Development Team 