"use client";

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { 
  BarChart3, 
  Users, 
  Calendar, 
  Target, 
  Zap, 
  Shield, 
  Clock, 
  TrendingUp,
  MessageSquare,
  FileText,
  Settings,
  Globe
} from "lucide-react";

const features = [
  {
    icon: BarChart3,
    title: "Advanced Analytics",
    description: "Get deep insights into your project performance with comprehensive analytics and reporting tools."
  },
  {
    icon: Users,
    title: "Team Collaboration",
    description: "Work seamlessly with your team using real-time collaboration features and communication tools."
  },
  {
    icon: Calendar,
    title: "Smart Scheduling",
    description: "Intelligent scheduling system that adapts to your team's availability and project deadlines."
  },
  {
    icon: Target,
    title: "Goal Tracking",
    description: "Set, track, and achieve your project goals with our comprehensive goal management system."
  },
  {
    icon: Zap,
    title: "Automation",
    description: "Automate repetitive tasks and workflows to increase productivity and reduce manual work."
  },
  {
    icon: Shield,
    title: "Enterprise Security",
    description: "Bank-level security with end-to-end encryption to keep your data safe and compliant."
  },
  {
    icon: Clock,
    title: "Time Tracking",
    description: "Accurate time tracking with detailed reports to optimize your team's productivity."
  },
  {
    icon: TrendingUp,
    title: "Performance Insights",
    description: "Monitor team performance and project progress with actionable insights and recommendations."
  },
  {
    icon: MessageSquare,
    title: "Real-time Chat",
    description: "Built-in messaging system for instant communication and quick decision making."
  },
  {
    icon: FileText,
    title: "Document Management",
    description: "Centralized document storage with version control and collaborative editing features."
  },
  {
    icon: Settings,
    title: "Custom Workflows",
    description: "Create custom workflows that match your team's unique processes and requirements."
  },
  {
    icon: Globe,
    title: "Global Access",
    description: "Access your projects from anywhere with our cloud-based platform and mobile apps."
  }
];

export function FeatureGrid() {
  return (
    <section className="py-20 bg-muted/30">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
            Everything you need to succeed
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Powerful features designed to streamline your workflow and boost your team's productivity.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {features.map((feature, index) => {
            const Icon = feature.icon;
            return (
              <Card
                key={index}
                className="bg-gray-50 dark:bg-card hover:shadow-md hover:bg-gray-100 dark:hover:bg-accent/50 transition-all duration-200 cursor-pointer border"
              >
                <CardHeader className="pb-4">
                  <div className="w-12 h-12 rounded-lg bg-[#166534]/10 flex items-center justify-center mb-3">
                    <Icon className="w-6 h-6 text-[#166534]" />
                  </div>
                  <CardTitle className="text-lg font-semibold">
                    {feature.title}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-sm leading-relaxed">
                    {feature.description}
                  </CardDescription>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>
    </section>
  );
}
