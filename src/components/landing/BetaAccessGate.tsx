"use client";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useRouter } from "next/navigation";
import { useCallback, useMemo, useState } from "react";
import { toast } from "sonner";

const EXPECTED_SHA256_HEX =
  "f4340be4842ad8dc1b863a3386dcf0015abc063f60c76e495421407768a7e31f";

function bytesToHexString(bytes: ArrayBuffer): string {
  const byteArray = new Uint8Array(bytes);
  const hexCodes: string[] = [];
  for (let i = 0; i < byteArray.length; i += 1) {
    const hexCode = byteArray[i].toString(16).padStart(2, "0");
    hexCodes.push(hexCode);
  }
  return hexCodes.join("");
}

async function sha256Hex(input: string): Promise<string> {
  const encoder = new TextEncoder();
  const data = encoder.encode(input);
  const hashBuffer = await crypto.subtle.digest("SHA-256", data);
  return bytesToHexString(hashBuffer);
}

export function BetaAccessGate() {
  const router = useRouter();
  const [password, setPassword] = useState<string>("");
  const [isVerifying, setIsVerifying] = useState<boolean>(false);

  const canSubmit = useMemo(
    () => password.trim().length > 0 && !isVerifying,
    [password, isVerifying]
  );

  const setBetaCookieAndRedirect = useCallback(() => {
    const maxAgeDays = 7;
    const maxAgeSeconds = maxAgeDays * 24 * 60 * 60;
    const cookie = `beta_access=1; Path=/; Max-Age=${maxAgeSeconds}; SameSite=Lax`;
    // Avoid always setting Secure so it also works on localhost during development
    document.cookie = cookie;
    router.push("/auth/login");
  }, [router]);

  const handleSubmit = useCallback(
    async (e?: React.FormEvent) => {
      e?.preventDefault();
      if (!canSubmit) return;
      try {
        setIsVerifying(true);
        const hash = await sha256Hex(password.trim());
        if (hash === EXPECTED_SHA256_HEX) {
          setBetaCookieAndRedirect();
        } else {
          toast.error("Invalid access code. Please try again.");
        }
      } catch (err) {
        console.error("Beta access verification failed:", err);
        toast.error("Something went wrong. Please try again.");
      } finally {
        setIsVerifying(false);
      }
    },
    [canSubmit, password, setBetaCookieAndRedirect]
  );

  return (
    <div className="w-full max-w-sm mx-auto">
      <form
        onSubmit={handleSubmit}
        className="group relative bg-card/80 hover:bg-card/90 backdrop-blur border rounded-2xl p-6 space-y-4 shadow-xl overflow-hidden transition-all duration-300"
      >
        {/* Gradient overlay to match dashboard style */}
        <div className="absolute inset-0 bg-gradient-to-br from-[var(--primary)]/5 via-transparent to-[var(--primary-light)]/5 pointer-events-none" />

        <div className="relative z-10">
          <div className="text-center space-y-2 mb-4">
            <h2 className="text-xl font-semibold">Beta access required</h2>
            <p className="text-sm text-muted-foreground">
              Enter the beta access code to continue to sign in.
            </p>
          </div>

          <div className="space-y-3">
            <Input
              type="password"
              autoComplete="off"
              placeholder="Enter access code"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="w-full bg-background/50 backdrop-blur focus:ring-[var(--primary)]/20 focus:border-[var(--primary)] transition-all duration-300 hover:shadow-lg"
            />
            <Button
              type="submit"
              disabled={!canSubmit}
              className="w-full bg-[var(--primary)] hover:bg-[var(--primary-dark)] text-[var(--primary-foreground)] shadow-lg transition-all duration-300"
            >
              {isVerifying ? "Verifying..." : "Unlock beta"}
            </Button>
          </div>

          <div className="mt-3 text-xs text-muted-foreground text-center">
            Your access is stored for 7 days on this device.
          </div>
        </div>
      </form>
    </div>
  );
}

export default BetaAccessGate;
