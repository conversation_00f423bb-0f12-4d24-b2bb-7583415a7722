"use client";

import { But<PERSON> } from "@/components/ui/button";
import { BlogSectionProps } from "@/types/blog";
import { ArrowRight } from "lucide-react";
import Link from "next/link";
import { BlogCard } from "./blog-card";

export function BlogSection({
  posts,
  showViewAll = true,
  className = "",
}: BlogSectionProps) {
  return (
    <section className={`py-20  ${className}`}>
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
            Latest Insights & Updates
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Stay informed with our latest articles on project management,
            productivity, and industry trends.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          {posts.map((post) => (
            <BlogCard key={post.id} post={post} />
          ))}
        </div>

        {showViewAll && (
          <div className="text-center">
            <Link href="/blog">
              <Button
                size="lg"
                className="bg-[#166534] hover:bg-[#166534]/90 text-white border border-[#166534]"
              >
                View All Posts
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            </Link>
          </div>
        )}
      </div>
    </section>
  );
}
