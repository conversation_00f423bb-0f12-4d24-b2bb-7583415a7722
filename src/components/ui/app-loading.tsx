"use client";

import React from "react";
import { Logo } from "@/components/ui/logo";

interface AppLoadingProps {
  message?: string;
  size?: "sm" | "md" | "lg";
  fullScreen?: boolean;
}

export const AppLoading: React.FC<AppLoadingProps> = ({
  message = "Loading...",
  size = "md",
  fullScreen = true,
}) => {
  // Size configurations - max 64px
  const sizeConfig = {
    sm: {
      circle: "w-12 h-12", // 48px
      text: "text-sm",
      spacing: "space-y-3",
    },
    md: {
      circle: "w-16 h-16", // 64px
      text: "text-base",
      spacing: "space-y-4",
    },
    lg: {
      circle: "w-16 h-16", // 64px (max)
      text: "text-lg",
      spacing: "space-y-6",
    },
  };

  const config = sizeConfig[size];

  const containerClasses = fullScreen
    ? "fixed inset-0 z-50 flex items-center justify-center"
    : "flex items-center justify-center w-full h-full";

  return (
    <div className={containerClasses}>
      <div className={`text-center ${config.spacing}`}>
        {/* Simple circular spinner */}
        <div className="relative flex items-center justify-center">
          <div
            className={`${config.circle} border-4 border-gray-200 border-t-green-500 rounded-full animate-spin`}
          />
        </div>

        {/* Loading message */}
        {message && (
          <p
            className={`text-gray-600 dark:text-gray-400 font-medium ${config.text}`}
          >
            {message}
          </p>
        )}
      </div>
    </div>
  );
};

// Convenience components for different use cases
export const FullScreenLoading: React.FC<{ message?: string }> = ({
  message,
}) => <AppLoading message={message} size="md" fullScreen={true} />;

export const InlineLoading: React.FC<{
  message?: string;
  size?: "sm" | "md" | "lg";
}> = ({ message, size = "md" }) => (
  <AppLoading message={message} size={size} fullScreen={false} />
);

// Page-level loading component - simple centered loading
export const PageLoading: React.FC<{ message?: string }> = ({ message }) => (
  <div className="min-h-screen flex items-center justify-center">
    <AppLoading message={message} size="md" fullScreen={false} />
  </div>
);
