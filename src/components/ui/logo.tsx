"use client";

import React from "react";
import Link from "next/link";

interface LogoProps {
  size?: number; // px
  className?: string;
  animated?: boolean;
  showText?: boolean;
  href?: string;
  textSize?: number;
  animationSpeed?: number ; // 0.5 = slow, 1 = normal, 2 = fast
}

export const Logo: React.FC<LogoProps> = ({
  size = 48,
  textSize = 24,
  className = "",
  animated = false,
  showText = false,
  href,
  animationSpeed = 5,
}) => {
  // Base sizes (for 48px logo)
  const baseSize = 48;
  const baseInnerSize = 32; // Dark green crescent size
  const baseWhiteSize = 20; // White circle size
  const borderWidth = 1;
  
  // Scale factor
  const scale = size / baseSize;
  
  // Scaled sizes
  const innerSize = baseInnerSize * scale;
  const whiteSize = baseWhiteSize * scale;
  const scaledBorderWidth = borderWidth * scale;
  const padding = 1.25 * scale;

  // Calculate animation durations based on speed
  const floatDuration = 3 / animationSpeed;
  const rotateDuration = 8 / animationSpeed;
  const pulseDuration = 2 / animationSpeed;
  const glowDuration = 4 / animationSpeed;
  const textGlowDuration = 3 / animationSpeed;
  const dotPulseDuration = 1.5 / animationSpeed;

  const logoIcon = (
    <div
      style={{
        position: "relative",
        width: size,
        height: size,
        display: "inline-block",
        animation: animated ? `logoFloat ${floatDuration}s ease-in-out infinite` : "none",
      }}
    >
      {/* Outer light green ring */}
      <div
        style={{
          position: "absolute",
          width: size,
          height: size,
          borderRadius: "50%",
          background: "#b4fd98",
          border: `${scaledBorderWidth}px solid #73ed47`,
          left: 0,
          top: 0,
          zIndex: 1,
          transform: "rotate(70deg)",
          animation: animated ? `logoRotate ${rotateDuration}s linear infinite` : "none",
        }}
      >
        {/* Dark green crescent */}
        <div
          style={{
            position: "absolute",
            width: innerSize,
            height: innerSize,
            borderRadius: "50%",
            background: "#0A4000",
            border: `${scaledBorderWidth}px solid #73ed47`,
            left: size - innerSize - scaledBorderWidth - padding,
            top: scaledBorderWidth + padding,
            zIndex: 2,
            transform: "rotate(280deg)",
            animation: animated ? `logoPulse ${pulseDuration}s ease-in-out infinite` : "none",
          }}
        >
          {/* White circle creating crescent effect */}
          <div
            style={{
              position: "absolute",
              width: whiteSize,
              height: whiteSize,
              borderRadius: "50%",
              background: "#fff", 
              border: `${scaledBorderWidth}px solid #73ed47`,
              left: innerSize - whiteSize - scaledBorderWidth - padding,
              top: scaledBorderWidth + padding,
              zIndex: 3,
              animation: animated ? `logoGlow ${glowDuration}s ease-in-out infinite` : "none",
            }}
          />
        </div>
      </div>
      
      {/* CSS Animations */}
      {animated && (
        <style jsx>{`
          
          
          @keyframes logoRotate {
            0% {
              transform: rotate(70deg);
            }
            100% {
              transform: rotate(430deg);
            }
          }
           
        `}</style>
      )}
    </div>
  );
 
  const logoWithText = (
    <div className={`flex items-center gap-3 ${className}`}>
      {logoIcon}
      <span 
        className="font-bold text-foreground" 
        style={{ 
          fontSize: `${textSize}px`,
          position: 'relative',
          animation: animated ? `textGlow ${textGlowDuration}s ease-in-out infinite` : "none",
        }}
      >
        siift.ai
      </span>
     
    </div>
  );

  const content = showText ? logoWithText : logoIcon;

  if (href) {
    return (
      <Link href={href} className="hover:opacity-80 transition-opacity">
        {content}
      </Link>
    );
  }

  return content;
};
