import React from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils";

interface FormInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label: string;
  error?: string;
  icon?: React.ReactNode;
  endIcon?: React.ReactNode;
}

export const FormInput = React.forwardRef<HTMLInputElement, FormInputProps>(
  ({ label, error, icon, endIcon, className, id, ...props }, ref) => {
    const inputId = id || label.toLowerCase().replace(/\s+/g, "-");

    return (
      <div className="space-y-2">
        <Label htmlFor={inputId}>{label}</Label>
        <div className="relative">
          {icon && (
            <div className="absolute left-3 top-3 h-4 w-4 text-muted-foreground">
              {icon}
            </div>
          )}
          <Input
            id={inputId}
            ref={ref}
            className={cn(
              icon && "pl-10",
              endIcon && "pr-10",
              error && "border-destructive",
              className
            )}
            {...props}
          />
          {endIcon && (
            <div className="absolute right-3 top-3 h-4 w-4">
              {endIcon}
            </div>
          )}
        </div>
        {error && (
          <p className="text-sm text-destructive">
            {error}
          </p>
        )}
      </div>
    );
  }
);

FormInput.displayName = "FormInput";
