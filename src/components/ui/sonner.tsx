"use client";

import { useTheme } from "next-themes";
import { Toaster as Son<PERSON>, ToasterProps } from "sonner";

const Toaster = ({ ...props }: ToasterProps) => {
  const { theme = "system" } = useTheme();

  return (
    <Sonner
      theme={theme as ToasterProps["theme"]}
      className="toaster group"
      toastOptions={{
        classNames: {
          toast:
            "group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg",
          description: "group-[.toast]:text-muted-foreground",
          actionButton:
            "group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",
          cancelButton:
            "group-[.toast]:bg-muted group-[.toast]:text-muted-foreground",
          error:
            "!group-[.toaster]:bg-[color:var(--destructive)] !group-[.toaster]:text-[color:var(--destructive-foreground)] !group-[.toaster]:border-[color:var(--destructive)]",
          success:
            "!group-[.toaster]:bg-[color:var(--primary-light)] !group-[.toaster]:text-[color:var(--siift-dark-accent)] !group-[.toaster]:border-[color:var(--siift-mid-accent)]",
          warning:
            "!group-[.toaster]:bg-[color:var(--siift-light-mid)] !group-[.toaster]:text-[color:var(--siift-darkest)] !group-[.toaster]:border-[color:var(--border)] dark:!group-[.toaster]:bg-[color:var(--siift-dark-main)] dark:!group-[.toaster]:text-[color:var(--siift-lightest)] dark:!group-[.toaster]:border-[color:var(--border)]",
          info: "!group-[.toaster]:bg-[color:var(--card)] !group-[.toaster]:text-[color:var(--card-foreground)] !group-[.toaster]:border-[color:var(--border)]",
        },
      }}
      {...props}
    />
  );
};

export { Toaster };
