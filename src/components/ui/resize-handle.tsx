import React from 'react';
import { cn } from '@/lib/utils';

interface ResizeHandleProps {
  onMouseDown: (e: React.MouseEvent) => void;
  isDragging: boolean;
  className?: string;
}

export function ResizeHandle({ onMouseDown, isDragging, className }: ResizeHandleProps) {
  return (
    <div
      className={cn(
        "absolute right-0 top-0 bottom-0 w-1 cursor-col-resize group hover:w-2 transition-all duration-200",
        "bg-transparent hover:bg-border/50",
        isDragging && "bg-primary/50 w-2",
        className
      )}
      onMouseDown={onMouseDown}
    >
      {/* Visual indicator */}
      <div
        className={cn(
          "absolute right-0 top-1/2 -translate-y-1/2 w-1 h-8 bg-border/30 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200",
          isDragging && "opacity-100 bg-primary/70"
        )}
      />
    </div>
  );
}
