import Script from "next/script";

interface StructuredDataProps {
  data: Record<string, any> | Record<string, any>[];
}

export function StructuredData({ data }: StructuredDataProps) {
  return (
    <Script
      id="structured-data"
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(data),
      }}
    />
  );
}

// Common structured data schemas
export const structuredDataSchemas = {
  // Organization schema
  organization: (org: {
    name: string;
    url: string;
    logo: string;
    description: string;
    socialMedia?: string[];
    contactPoint?: {
      telephone: string;
      contactType: string;
      email?: string;
    };
  }) => ({
    "@context": "https://schema.org",
    "@type": "Organization",
    name: org.name,
    url: org.url,
    logo: {
      "@type": "ImageObject",
      url: org.logo,
    },
    description: org.description,
    sameAs: org.socialMedia || [],
    contactPoint: org.contactPoint ? {
      "@type": "ContactPoint",
      telephone: org.contactPoint.telephone,
      contactType: org.contactPoint.contactType,
      email: org.contactPoint.email,
    } : undefined,
  }),

  // Website schema
  website: (site: {
    name: string;
    url: string;
    description: string;
    searchUrl?: string;
  }) => ({
    "@context": "https://schema.org",
    "@type": "WebSite",
    name: site.name,
    url: site.url,
    description: site.description,
    potentialAction: site.searchUrl ? {
      "@type": "SearchAction",
      target: site.searchUrl,
      "query-input": "required name=search_term_string",
    } : undefined,
  }),

  // Software Application schema
  softwareApplication: (app: {
    name: string;
    description: string;
    url: string;
    category: string;
    operatingSystem?: string;
    price?: string;
    priceCurrency?: string;
  }) => ({
    "@context": "https://schema.org",
    "@type": "SoftwareApplication",
    name: app.name,
    description: app.description,
    url: app.url,
    applicationCategory: app.category,
    operatingSystem: app.operatingSystem || "Web Browser",
    offers: app.price ? {
      "@type": "Offer",
      price: app.price,
      priceCurrency: app.priceCurrency || "USD",
    } : undefined,
  }),

  // Article schema
  article: (article: {
    title: string;
    description: string;
    author: string;
    publishedTime: string;
    modifiedTime?: string;
    image: string;
    url: string;
    publisher: {
      name: string;
      logo: string;
    };
  }) => ({
    "@context": "https://schema.org",
    "@type": "Article",
    headline: article.title,
    description: article.description,
    author: {
      "@type": "Person",
      name: article.author,
    },
    datePublished: article.publishedTime,
    dateModified: article.modifiedTime || article.publishedTime,
    image: {
      "@type": "ImageObject",
      url: article.image,
    },
    url: article.url,
    publisher: {
      "@type": "Organization",
      name: article.publisher.name,
      logo: {
        "@type": "ImageObject",
        url: article.publisher.logo,
      },
    },
  }),

  // Blog schema
  blog: (blog: {
    name: string;
    description: string;
    url: string;
    posts: Array<{
      title: string;
      description: string;
      url: string;
      publishedTime: string;
      author: string;
      image: string;
    }>;
  }) => ({
    "@context": "https://schema.org",
    "@type": "Blog",
    name: blog.name,
    description: blog.description,
    url: blog.url,
    blogPost: blog.posts.map(post => ({
      "@type": "BlogPosting",
      headline: post.title,
      description: post.description,
      url: post.url,
      datePublished: post.publishedTime,
      author: {
        "@type": "Person",
        name: post.author,
      },
      image: {
        "@type": "ImageObject",
        url: post.image,
      },
    })),
  }),

  // FAQ schema
  faq: (faqs: Array<{ question: string; answer: string }>) => ({
    "@context": "https://schema.org",
    "@type": "FAQPage",
    mainEntity: faqs.map(faq => ({
      "@type": "Question",
      name: faq.question,
      acceptedAnswer: {
        "@type": "Answer",
        text: faq.answer,
      },
    })),
  }),

  // Breadcrumb schema
  breadcrumb: (breadcrumbs: Array<{ name: string; url: string }>) => ({
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    itemListElement: breadcrumbs.map((crumb, index) => ({
      "@type": "ListItem",
      position: index + 1,
      name: crumb.name,
      item: crumb.url,
    })),
  }),

  // Product schema
  product: (product: {
    name: string;
    description: string;
    image: string;
    brand: string;
    price: string;
    priceCurrency: string;
    availability: string;
    rating?: {
      value: number;
      count: number;
    };
  }) => ({
    "@context": "https://schema.org",
    "@type": "Product",
    name: product.name,
    description: product.description,
    image: product.image,
    brand: {
      "@type": "Brand",
      name: product.brand,
    },
    offers: {
      "@type": "Offer",
      price: product.price,
      priceCurrency: product.priceCurrency,
      availability: `https://schema.org/${product.availability}`,
    },
    aggregateRating: product.rating ? {
      "@type": "AggregateRating",
      ratingValue: product.rating.value,
      reviewCount: product.rating.count,
    } : undefined,
  }),
};
