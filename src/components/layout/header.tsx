"use client";

import React from "react";
import { MainNav } from "@/components/navigation/main-nav";
import { MobileNav } from "@/components/navigation/mobile-nav";
import { UserMenu } from "@/components/navigation/user-menu";
import { ThemeToggle } from "@/components/theme-toggle";
import { Logo } from "@/components/ui/logo";

export function Header() {
  return (
    <header className="w-full sticky top-0 z-30 bg-background/80 backdrop-blur border-b border-border">
      <div className="container mx-auto px-4 flex h-16 items-center justify-between">
        {/* Left: Logo and Navigation */}
        <div className="flex items-center">
          {/* Mobile Nav Button */}
          <div className="flex items-center mr-4 md:hidden">
            <MobileNav />
          </div>

          {/* Logo and App Title */}
          <Logo size={32} animated={false} showText={true} href="/" />

          {/* Desktop Navigation - Adjacent to logo */}
          <div className="hidden md:flex ml-6">
            <MainNav />
          </div>
        </div>

        {/* Right: Theme Toggle and User Menu */}
        <div className="flex items-center gap-3">
          <ThemeToggle />
          <UserMenu />
        </div>
      </div>
    </header>
  );
}
