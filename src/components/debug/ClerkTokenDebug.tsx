"use client";

import { useEffect, useState } from "react";
import { useAuth, useUser, useSession } from "@clerk/nextjs";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Co<PERSON>, Eye, EyeOff, RefreshCw } from "lucide-react";
import { toast } from "sonner";

export function ClerkTokenDebug() {
  const { isSignedIn, getToken, userId } = useAuth();
  const { user } = useUser();
  const { session } = useSession();
  const [tokens, setTokens] = useState<{
    jwt?: string;
    template?: string;
    custom?: string;
  }>({});
  const [showTokens, setShowTokens] = useState(false);
  const [loading, setLoading] = useState(false);

  const fetchTokens = async () => {
    if (!isSignedIn) return;
    
    setLoading(true);
    try {
      // Get different types of tokens
      const jwtToken = await getToken();
      const templateToken = await getToken({ template: "integration_firebase" }); // Example template
      
      const tokenData = {
        jwt: jwtToken || undefined,
        template: templateToken || undefined,
        custom: (await getToken({ template: "custom" })) || undefined, // Custom template if you have one
      };

      setTokens(tokenData);

      // Log to console with detailed information
      console.group("🔐 Clerk Token Information");
      console.log("📋 User ID:", userId);
      console.log("👤 User Email:", user?.emailAddresses[0]?.emailAddress);
      console.log("🎫 Session ID:", session?.id);
      console.log("⏰ Session Last Active:", session?.lastActiveAt);
      console.log("📅 Session Expires:", session?.expireAt);
      
      console.group("🔑 Tokens");
      console.log("JWT Token:", jwtToken);
      console.log("Template Token:", templateToken);
      console.log("Token Length:", jwtToken?.length);
      
      if (jwtToken) {
        // Decode JWT payload (just for debugging - don't do this in production)
        try {
          const payload = JSON.parse(atob(jwtToken.split('.')[1]));
          console.log("JWT Payload:", payload);
          console.log("Token Issued At:", new Date(payload.iat * 1000));
          console.log("Token Expires At:", new Date(payload.exp * 1000));
        } catch (e) {
          console.log("Could not decode JWT payload");
        }
      }
      console.groupEnd();
      console.groupEnd();

    } catch (error) {
      console.error("❌ Error fetching tokens:", error);
      toast.error("Failed to fetch tokens");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (isSignedIn) {
      fetchTokens();
    }
  }, [isSignedIn]);

  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text);
    toast.success(`${label} copied to clipboard`);
  };

  const formatToken = (token: string) => {
    if (!token) return "No token";
    return showTokens ? token : `${token.substring(0, 20)}...${token.substring(token.length - 20)}`;
  };

  if (!isSignedIn) {
    return (
      <Card className="w-full max-w-2xl">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            🔐 Clerk Token Debug
            <Badge variant="secondary">Not Signed In</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">Please sign in to view Clerk tokens.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-4xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          🔐 Clerk Token Debug
          <Badge variant="default">Signed In</Badge>
        </CardTitle>
        <div className="flex gap-2">
          <Button onClick={fetchTokens} disabled={loading} size="sm">
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh Tokens
          </Button>
          <Button 
            onClick={() => setShowTokens(!showTokens)} 
            variant="outline" 
            size="sm"
          >
            {showTokens ? <EyeOff className="h-4 w-4 mr-2" /> : <Eye className="h-4 w-4 mr-2" />}
            {showTokens ? 'Hide' : 'Show'} Tokens
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* User Info */}
        <div className="grid grid-cols-2 gap-4 p-4 bg-muted rounded-lg">
          <div>
            <p className="text-sm font-medium">User ID</p>
            <p className="text-sm text-muted-foreground font-mono">{userId}</p>
          </div>
          <div>
            <p className="text-sm font-medium">Email</p>
            <p className="text-sm text-muted-foreground">{user?.emailAddresses[0]?.emailAddress}</p>
          </div>
          <div>
            <p className="text-sm font-medium">Session ID</p>
            <p className="text-sm text-muted-foreground font-mono">{session?.id}</p>
          </div>
          <div>
            <p className="text-sm font-medium">Last Active</p>
            <p className="text-sm text-muted-foreground">{session?.lastActiveAt?.toLocaleString()}</p>
          </div>
        </div>

        {/* JWT Token */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">JWT Token</h3>
            {tokens.jwt && (
              <Button 
                onClick={() => copyToClipboard(tokens.jwt!, "JWT Token")} 
                size="sm" 
                variant="outline"
              >
                <Copy className="h-4 w-4 mr-2" />
                Copy
              </Button>
            )}
          </div>
          <div className="p-3 bg-muted rounded-lg">
            <code className="text-sm break-all">
              {tokens.jwt ? formatToken(tokens.jwt) : "Loading..."}
            </code>
          </div>
        </div>

        {/* Template Token */}
        {tokens.template && (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">Template Token</h3>
              <Button 
                onClick={() => copyToClipboard(tokens.template!, "Template Token")} 
                size="sm" 
                variant="outline"
              >
                <Copy className="h-4 w-4 mr-2" />
                Copy
              </Button>
            </div>
            <div className="p-3 bg-muted rounded-lg">
              <code className="text-sm break-all">
                {formatToken(tokens.template)}
              </code>
            </div>
          </div>
        )}

        {/* Instructions */}
      <div className="p-4 bg-[var(--siift-light-main)]/40 dark:bg-[color:var(--siift-darker)]/50 rounded-lg">
          <h4 className="font-semibold text-blue-900 dark:text-blue-100">How to use these tokens:</h4>
          <ul className="mt-2 text-sm text-blue-800 dark:text-blue-200 space-y-1">
            <li>• <strong>JWT Token:</strong> Use for API authentication with your backend</li>
            <li>• <strong>Template Token:</strong> Custom tokens with specific claims</li>
            <li>• Check the browser console for detailed token information</li>
            <li>• Tokens are automatically refreshed by Clerk</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}
