"use client";

import { useState } from "react";
import { usePostHog } from "posthog-js/react";
import { useAnalytics } from "@/hooks/useAnalytics";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

export function PostHogDebug() {
  const posthog = usePostHog();
  const { trackClick, trackCustomEvent, identifyUser } = useAnalytics();
  const [customEventName, setCustomEventName] = useState("test_event");
  const [customEventData, setCustomEventData] = useState("{}");
  const [userId, setUserId] = useState("");
  const [lastEvent, setLastEvent] = useState<string | null>(null);

  const handleTestClick = () => {
    trackClick("debug-test-button", "posthog-debug");
    setLastEvent("button_clicked");
  };

  const handleCustomEvent = () => {
    try {
      const eventData = JSON.parse(customEventData);
      trackCustomEvent(customEventName, eventData);
      setLastEvent(customEventName);
    } catch (error) {
      console.error("Invalid JSON in custom event data:", error);
      setLastEvent("error: invalid JSON");
    }
  };

  const handleIdentifyUser = () => {
    if (userId.trim()) {
      identifyUser(userId, { debug_session: true });
      setLastEvent(`identified user: ${userId}`);
    }
  };

  const handleDirectPostHog = () => {
    posthog?.capture("direct_posthog_test", {
      timestamp: new Date().toISOString(),
      source: "debug_component"
    });
    setLastEvent("direct_posthog_test");
  };

  const isPostHogLoaded = !!posthog;
  const postHogConfig = posthog?.config;

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          PostHog Debug Panel
          <Badge variant={isPostHogLoaded ? "default" : "destructive"}>
            {isPostHogLoaded ? "Loaded" : "Not Loaded"}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* PostHog Status */}
        <div className="space-y-2">
          <h3 className="font-semibold">PostHog Status</h3>
          <div className="text-sm space-y-1">
            <p><strong>Loaded:</strong> {isPostHogLoaded ? "✅ Yes" : "❌ No"}</p>
            <p><strong>API Host:</strong> {postHogConfig?.api_host || "Not available"}</p>
            <p><strong>Project Key:</strong> {process.env.NEXT_PUBLIC_POSTHOG_KEY ? "✅ Set" : "❌ Missing"}</p>
            <p><strong>Debug Mode:</strong> {postHogConfig?.debug ? "✅ Enabled" : "❌ Disabled"}</p>
          </div>
        </div>

        {/* Test Buttons */}
        <div className="space-y-4">
          <h3 className="font-semibold">Test Events</h3>
          
          <div className="grid grid-cols-2 gap-2">
            <Button onClick={handleTestClick} variant="outline">
              Test Click Event
            </Button>
            <Button onClick={handleDirectPostHog} variant="outline">
              Direct PostHog Test
            </Button>
          </div>

          {/* Custom Event */}
          <div className="space-y-2">
            <Label htmlFor="eventName">Custom Event Name</Label>
            <Input
              id="eventName"
              value={customEventName}
              onChange={(e) => setCustomEventName(e.target.value)}
              placeholder="event_name"
            />
            <Label htmlFor="eventData">Event Data (JSON)</Label>
            <Input
              id="eventData"
              value={customEventData}
              onChange={(e) => setCustomEventData(e.target.value)}
              placeholder='{"key": "value"}'
            />
            <Button onClick={handleCustomEvent} variant="outline" className="w-full">
              Send Custom Event
            </Button>
          </div>

          {/* User Identification */}
          <div className="space-y-2">
            <Label htmlFor="userId">User ID</Label>
            <Input
              id="userId"
              value={userId}
              onChange={(e) => setUserId(e.target.value)}
              placeholder="user123"
            />
            <Button onClick={handleIdentifyUser} variant="outline" className="w-full">
              Identify User
            </Button>
          </div>
        </div>

        {/* Last Event */}
        {lastEvent && (
          <div className="space-y-2">
            <h3 className="font-semibold">Last Event Sent</h3>
            <Badge variant="secondary">{lastEvent}</Badge>
            <p className="text-xs text-muted-foreground">
              Check your browser's Network tab or PostHog dashboard to verify the event was sent.
            </p>
          </div>
        )}

        {/* Instructions */}
        <div className="space-y-2">
          <h3 className="font-semibold">Debug Instructions</h3>
          <div className="text-sm text-muted-foreground space-y-1">
            <p>1. Open browser DevTools (F12)</p>
            <p>2. Go to Network tab</p>
            <p>3. Filter by "posthog" or "batch"</p>
            <p>4. Click test buttons and watch for network requests</p>
            <p>5. Check PostHog dashboard for events (may take a few minutes)</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
