"use client";

import { useState } from "react";
import { useAuth, useUser } from "@clerk/nextjs";
import { useClerkA<PERSON> } from "@/hooks/useClerkApi";
import { useClerkToken } from "@/hooks/useClerkToken";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Copy, Eye, EyeOff } from "lucide-react";
import { toast } from "sonner";

export function ClerkTokenExample() {
  const { isSignedIn, getToken } = useAuth();
  const { user } = useUser();
  const { get, post, isSignedIn: apiIsSignedIn } = useClerkApi();
  const { token, fetchAndLogToken, copyTokenToClipboard } = useClerkToken();
  
  const [currentToken, setCurrentToken] = useState<string>("");
  const [showToken, setShowToken] = useState(false);
  const [apiEndpoint, setApiEndpoint] = useState("/api/auth/me");
  const [apiResponse, setApiResponse] = useState<string>("");
  const [loading, setLoading] = useState(false);

  // Get fresh token
  const handleGetToken = async () => {
    try {
      setLoading(true);
      const freshToken = await getToken();
      if (freshToken) {
        setCurrentToken(freshToken);
        toast.success("Token retrieved successfully!");
        console.log("🎫 Fresh Clerk Token:", freshToken);
      } else {
        toast.error("Failed to get token");
      }
    } catch (error) {
      console.error("Error getting token:", error);
      toast.error("Error getting token");
    } finally {
      setLoading(false);
    }
  };

  // Test API call with token
  const handleTestApiCall = async () => {
    try {
      setLoading(true);
      setApiResponse("");
      
      // Using the useClerkApi hook (recommended)
      const response = await get(apiEndpoint);
      setApiResponse(JSON.stringify(response, null, 2));
      toast.success("API call successful!");
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      setApiResponse(`Error: ${errorMessage}`);
      toast.error("API call failed");
      console.error("API call error:", error);
    } finally {
      setLoading(false);
    }
  };

  // Manual API call with token
  const handleManualApiCall = async () => {
    try {
      setLoading(true);
      setApiResponse("");
      
      const token = await getToken();
      if (!token) {
        throw new Error("No token available");
      }

      const baseUrl = process.env.NEXT_PUBLIC_ADMIN_API_URL || "http://localhost:3002";
      const response = await fetch(`${baseUrl}${apiEndpoint}`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      setApiResponse(JSON.stringify(data, null, 2));
      toast.success("Manual API call successful!");
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      setApiResponse(`Error: ${errorMessage}`);
      toast.error("Manual API call failed");
      console.error("Manual API call error:", error);
    } finally {
      setLoading(false);
    }
  };

  const copyToken = () => {
    if (currentToken) {
      navigator.clipboard.writeText(currentToken);
      toast.success("Token copied to clipboard!");
    }
  };

  const decodeToken = (tokenToDecode: string) => {
    try {
      const payload = JSON.parse(atob(tokenToDecode.split('.')[1]));
      return {
        userId: payload.sub,
        email: payload.email,
        issuedAt: new Date(payload.iat * 1000),
        expiresAt: new Date(payload.exp * 1000),
        issuer: payload.iss,
        fullPayload: payload
      };
    } catch (error) {
      return null;
    }
  };

  const tokenInfo = currentToken ? decodeToken(currentToken) : null;

  if (!isSignedIn) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Clerk Token Example</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">Please sign in to test Clerk token functionality.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            Clerk Token Management
            <Badge variant={isSignedIn ? "default" : "destructive"}>
              {isSignedIn ? "Signed In" : "Not Signed In"}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* User Info */}
          <div className="space-y-2">
            <h4 className="font-semibold">Current User</h4>
            <div className="text-sm space-y-1">
              <p><strong>Email:</strong> {user?.emailAddresses[0]?.emailAddress}</p>
              <p><strong>User ID:</strong> {user?.id}</p>
              <p><strong>Name:</strong> {user?.firstName} {user?.lastName}</p>
            </div>
          </div>

          {/* Get Token */}
          <div className="space-y-2">
            <Button onClick={handleGetToken} disabled={loading} className="w-full">
              {loading ? "Getting Token..." : "Get Fresh Clerk Token"}
            </Button>
          </div>

          {/* Display Token */}
          {currentToken && (
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <h4 className="font-semibold">Current Token</h4>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowToken(!showToken)}
                >
                  {showToken ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
                <Button variant="outline" size="sm" onClick={copyToken}>
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
              
              <div className="bg-muted p-3 rounded-lg">
                <code className="text-xs break-all">
                  {showToken ? currentToken : `${currentToken.substring(0, 50)}...`}
                </code>
              </div>

              {/* Token Info */}
              {tokenInfo && (
                <div className="text-sm space-y-1">
                  <p><strong>User ID:</strong> {tokenInfo.userId}</p>
                  <p><strong>Issued:</strong> {tokenInfo.issuedAt.toLocaleString()}</p>
                  <p><strong>Expires:</strong> {tokenInfo.expiresAt.toLocaleString()}</p>
                  <p><strong>Issuer:</strong> {tokenInfo.issuer}</p>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* API Testing */}
      <Card>
        <CardHeader>
          <CardTitle>Test Backend API Calls</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="endpoint">API Endpoint</Label>
            <Input
              id="endpoint"
              value={apiEndpoint}
              onChange={(e) => setApiEndpoint(e.target.value)}
              placeholder="/api/auth/me"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
            <Button onClick={handleTestApiCall} disabled={loading} variant="default">
              {loading ? "Calling..." : "Test with useClerkApi Hook"}
            </Button>
            <Button onClick={handleManualApiCall} disabled={loading} variant="outline">
              {loading ? "Calling..." : "Test Manual API Call"}
            </Button>
          </div>

          {/* API Response */}
          {apiResponse && (
            <div className="space-y-2">
              <h4 className="font-semibold">API Response</h4>
              <Textarea
                value={apiResponse}
                readOnly
                className="min-h-[200px] font-mono text-xs"
              />
            </div>
          )}
        </CardContent>
      </Card>

      {/* Code Examples */}
      <Card>
        <CardHeader>
          <CardTitle>Code Examples</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <h4 className="font-semibold">1. Using useClerkApi Hook (Recommended)</h4>
            <pre className="bg-muted p-3 rounded-lg text-xs overflow-x-auto">
{`import { useClerkApi } from '@/hooks/useClerkApi';

const { get, post, put, delete: del } = useClerkApi();

// GET request
const data = await get('/api/users/me');

// POST request
const result = await post('/api/projects', { name: 'My Project' });`}
            </pre>
          </div>

          <div className="space-y-2">
            <h4 className="font-semibold">2. Manual Token Usage</h4>
            <pre className="bg-muted p-3 rounded-lg text-xs overflow-x-auto">
{`import { useAuth } from '@clerk/nextjs';

const { getToken } = useAuth();

const token = await getToken();
const response = await fetch('/api/endpoint', {
  headers: {
    'Authorization': \`Bearer \${token}\`,
    'Content-Type': 'application/json'
  }
});`}
            </pre>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
