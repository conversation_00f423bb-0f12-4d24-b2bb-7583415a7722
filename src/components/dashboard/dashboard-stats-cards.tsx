"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import {
  FolderOpen,
  CheckCircle,
  Clock,
  TrendingUp
} from "lucide-react";

export function DashboardStatsCards() {

  const stats = [
    {
      title: "Active Projects",
      value: "4",
      description: "Currently in progress",
      icon: FolderOpen,
      color: "text-blue-600",
      bgColor: "bg-blue-100 dark:bg-blue-900/20"
    },
    {
      title: "Completed Tasks",
      value: "23",
      description: "This month",
      icon: CheckCircle,
      color: "text-green-600",
      bgColor: "bg-green-100 dark:bg-green-900/20"
    },
    {
      title: "Hours Saved",
      value: "47",
      description: "Through automation",
      icon: Clock,
      color: "text-purple-600",
      bgColor: "bg-purple-100 dark:bg-purple-900/20"
    },
    {
      title: "Team Efficiency",
      value: "94%",
      description: "+12% from last month",
      icon: TrendingUp,
      color: "text-indigo-600",
      bgColor: "bg-indigo-100 dark:bg-indigo-900/20"
    }
  ];

  // const quickActions = [
  //   {
  //     title: "Team Members",
  //     value: "8",
  //     description: "Active collaborators",
  //     icon: Users,
  //     action: "Manage Team"
  //   },
  //   {
  //     title: "Upcoming Deadlines",
  //     value: "3",
  //     description: "Next 7 days",
  //     icon: Calendar,
  //     action: "View Calendar"
  //   },
  //   {
  //     title: "Goals Progress",
  //     value: "67%",
  //     description: "Monthly targets",
  //     icon: Target,
  //     action: "View Goals"
  //   },
  //   {
  //     title: "AI Insights",
  //     value: "5",
  //     description: "New recommendations",
  //     icon: Zap,
  //     action: "View Insights"
  //   }
  // ];

  return (
    <div className="space-y-6">
      {/* Main Stats */}
      <div className="flex flex-col gap-10">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {stats.map((stat, index) => (
            <Card key={index} className="bg-gray-50 dark:bg-card hover:shadow-md hover:bg-gray-100 dark:hover:bg-accent/50 transition-all duration-200 cursor-pointer">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-1">
                <CardTitle className="text-sm font-medium">
                  {stat.title}
                </CardTitle>
                <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                  <stat.icon className={`h-4 w-4 ${stat.color}`} />
                </div>
              </CardHeader>
              <CardContent className="pt-1">
                <div className="text-2xl font-bold">{stat.value}</div>
                <p className="text-xs text-muted-foreground">
                  {stat.description}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Quick Actions */}
      {/* <div>
        <h2 className="text-2xl font-bold tracking-tight mb-4">Quick Actions</h2>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {quickActions.map((action, index) => (
            <Card key={index} className="bg-gray-50 dark:bg-card hover:shadow-md hover:bg-gray-100 dark:hover:bg-accent/50 transition-all duration-200 cursor-pointer group">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-1">
                <CardTitle className="text-sm font-medium">
                  {action.title}
                </CardTitle>
                <action.icon className="h-4 w-4 text-muted-foreground group-hover:text-[#166534] transition-colors" />
              </CardHeader>
              <CardContent className="pt-1">
                <div className="text-2xl font-bold mb-1">{action.value}</div>
                <p className="text-xs text-muted-foreground mb-2">
                  {action.description}
                </p>
                <p className="text-xs text-[#166534] font-medium group-hover:underline">
                  {action.action} →
                </p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div> */}
    </div>
  );
}
