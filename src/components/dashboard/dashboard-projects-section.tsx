"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useProjects } from "@/hooks/queries/useProjects";
import { ArrowRight, MoreHorizontal } from "lucide-react";
import { useRouter } from "next/navigation";

// Updated: Removed add new project button, removed progress, reduced padding, added hover effects

export function DashboardProjectsSection() {
  const router = useRouter();
  const { data, isLoading, error } = useProjects();
  const projects = Array.isArray(data) ? data : [];

  const handleViewAllProjects = () => {
    router.push("/projects");
  };

  return (
    <div className="space-y-6">
      <div className="max-w-6xl mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left side - Recent Projects (1 per row) */}
          <div className="lg:col-span-1">
            <Card className="bg-gray-50 dark:bg-card">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      Recent Projects
                    </CardTitle>
                    <CardDescription>
                      Your active projects and quick access to create new ones.
                    </CardDescription>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleViewAllProjects}
                    className="border-2 hover:bg-primary/10 hover:text-primary transition-all duration-200"
                  >
                    <ArrowRight className="mr-2 h-4 w-4" />
                    View All
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                {isLoading && (
                  <div className="text-sm text-muted-foreground">
                    Loading projects...
                  </div>
                )}
                {error && (
                  <div className="text-sm text-destructive">
                    Failed to load projects
                  </div>
                )}
                <div className="grid gap-4 grid-cols-1">
                  {/* Project Cards */}
                  {projects.slice(0, 4).map((project: any) => (
                    <Card
                      key={project.id}
                      className="bg-secondary/10 dark:bg-secondary/90 hover:shadow-md hover:bg-gray-100 dark:hover:bg-accent/50 transition-all duration-200 cursor-pointer group"
                      onClick={() => router.push(`/projects/${project.id}`)}
                    >
                      <CardContent className="px-4 py-3">
                        <div className="flex items-start justify-between">
                          <h3 className="font-medium">{project.name}</h3>
                          <div className="flex items-center gap-2">
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-6 w-6"
                            >
                              <MoreHorizontal className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Right side - Project Info */}
          <div className="lg:col-span-1">
            <Card className="bg-gray-50 dark:bg-card">
              <CardHeader>
                <CardTitle className="text-lg">Project Overview</CardTitle>
                <CardDescription>
                  Quick insights about your projects
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">
                      Total Projects
                    </span>
                    <span className="font-semibold">{projects.length}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">
                      Active
                    </span>
                    <span className="font-semibold text-green-600">
                      {
                        projects.filter((p: any) => p.status === "active")
                          .length
                      }
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">
                      Completed
                    </span>
                    <span className="font-semibold text-blue-600">
                      {
                        projects.filter((p: any) => p.status === "completed")
                          .length
                      }
                    </span>
                  </div>
                </div>

                <div className="pt-4 border-t border-border">
                  <h4 className="text-sm font-medium mb-2">Recent Activity</h4>
                  <div className="space-y-2">
                    <div className="text-xs text-muted-foreground">
                      • Project created 2 hours ago
                    </div>
                    <div className="text-xs text-muted-foreground">
                      • Task completed in "Marketing Plan"
                    </div>
                    <div className="text-xs text-muted-foreground">
                      • New insights generated
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
