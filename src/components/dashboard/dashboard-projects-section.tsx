"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useProjects } from "@/hooks/queries/useProjects";
import { ArrowRight, MoreHorizontal } from "lucide-react";
import { useRouter } from "next/navigation";

// Updated: Removed add new project button, removed progress, reduced padding, added hover effects

export function DashboardProjectsSection() {
  const router = useRouter();
  const { data, isLoading, error } = useProjects();
  const projects = Array.isArray(data) ? data : [];

  const handleViewAllProjects = () => {
    router.push("/projects");
  };

  return (
    <div className="space-y-6">
      <Card className="bg-gray-50 dark:bg-card">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                Recent Projects
              </CardTitle>
              <CardDescription>
                Your active projects and quick access to create new ones.
              </CardDescription>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={handleViewAllProjects}
              className="border-2 hover:bg-primary/10 hover:text-primary transition-all duration-200"
            >
              <ArrowRight className="mr-2 h-4 w-4" />
              View All
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {isLoading && (
            <div className="text-sm text-muted-foreground">
              Loading projects...
            </div>
          )}
          {error && (
            <div className="text-sm text-destructive">
              Failed to load projects
            </div>
          )}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {/* Project Cards */}
            {projects.slice(0, 6).map((project: any) => (
              <Card
                key={project.id}
                className="bg-secondary/10 dark:bg-secondary/90 hover:shadow-md hover:bg-gray-100 dark:hover:bg-accent/50 transition-all duration-200 cursor-pointer group"
                onClick={() => router.push(`/projects/${project.id}`)}
              >
                <CardContent className="px-4 py-0">
                  <div className="flex items-start justify-between">
                    <h3 className="font-medium">{project.name}</h3>
                    <div className="flex items-center gap-2">
                      <Button variant="ghost" size="icon" className="h-6 w-6">
                        <MoreHorizontal className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
