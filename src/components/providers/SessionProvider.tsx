"use client";

import { FullScreenLoading } from "@/components/ui/app-loading";
import { useSessionStore } from "@/stores/sessionStore";
import { usePathname } from "next/navigation";
import { useEffect } from "react";

interface SessionProviderProps {
  children: React.ReactNode;
}

export function SessionProvider({ children }: SessionProviderProps) {
  const { actions, isInitialized, isLoading } = useSessionStore();
  const pathname = usePathname();

  // Don't show loading screen for auth pages
  const isAuthPage = pathname?.startsWith("/auth/");

  useEffect(() => {
    // Initialize session on component mount with timeout fallback
    const initSession = async () => {
      try {
        await actions.initializeSession();
      } catch (error) {
        console.error("Session initialization failed:", error);
        // Force initialization to complete even if it fails
        setTimeout(() => {
          useSessionStore.setState({ isInitialized: true, isLoading: false });
        }, 100);
      }
    };

    // For auth pages, initialize in background without blocking
    if (isAuthPage) {
      // Set as initialized immediately for auth pages to prevent blocking
      if (!isInitialized) {
        useSessionStore.setState({ isInitialized: true, isLoading: false });
      }
      // Still run initialization in background
      initSession();
    } else {
      // For non-auth pages, wait for initialization
      initSession();

      // Fallback timeout to prevent infinite loading
      const fallbackTimeout = setTimeout(() => {
        if (!isInitialized) {
          console.warn("Session initialization timeout, forcing completion");
          useSessionStore.setState({ isInitialized: true, isLoading: false });
        }
      }, 5000); // 5 second timeout

      return () => clearTimeout(fallbackTimeout);
    }
  }, [actions, isInitialized, isAuthPage]);

  // Show loading state while initializing (but not for auth pages)
  if ((!isInitialized || isLoading) && !isAuthPage) {
    return <FullScreenLoading message="Initializing..." />;
  }

  return <>{children}</>;
}
