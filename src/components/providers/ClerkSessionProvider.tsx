"use client";

import { useUser, useAuth } from "@clerk/nextjs";
import { useEffect } from "react";
import { useSessionStore } from "@/stores/sessionStore";
import { useRouter } from "next/navigation";
import { useUserSync } from "@/hooks/useUserSync";
import { useAnalytics } from "@/hooks/useAnalytics";

interface ClerkSessionProviderProps {
  children: React.ReactNode;
}

export function ClerkSessionProvider({ children }: ClerkSessionProviderProps) {
  const { user, isLoaded: userLoaded } = useUser();
  const { isSignedIn, getToken } = useAuth();
  const { actions, isAuthenticated } = useSessionStore();
  const { identifyUser, setUserProperties } = useAnalytics();
  const router = useRouter();
  // Temporarily disabled to prevent infinite loop
  // const { isUserSynced, isSyncing } = useUserSync();

  useEffect(() => {
    const syncClerkWithSession = async () => {
      if (!userLoaded) return;

      if (isSignedIn && user) {
        // User is signed in with Clerk
        if (!isAuthenticated) {
          // Sync Clerk user with session store
          try {
            // Get Clerk token
            const token = await getToken();

            // Log token information to console
            console.group("🔐 Clerk Authentication Success");
            console.log("👤 User:", user.emailAddresses[0]?.emailAddress);
            console.log("🎫 Token:", token);
            console.log("📏 Token Length:", token?.length);
            console.log("🆔 User ID:", user.id);
            if (token) {
              try {
                const payload = JSON.parse(atob(token.split('.')[1]));
                console.log("⏰ Token Expires:", new Date(payload.exp * 1000));
                console.log("🏷️ Token Subject:", payload.sub);
              } catch (e) {
                console.log("Could not decode token payload");
              }
            }
            console.groupEnd();

            // Create user object compatible with your session store
            const sessionUser = {
              id: user.id,
              email: user.emailAddresses[0]?.emailAddress || "",
              firstName: user.firstName || "",
              lastName: user.lastName || "",
              name: `${user.firstName || ""} ${user.lastName || ""}`.trim(),
              avatarUrl: user.imageUrl || "",
              isEmailVerified: user.emailAddresses[0]?.verification?.status === "verified",
              role: "user" as const,
              status: "active" as const,
              bio: "",
              timezone: "UTC",
              preferences: {
                notifications: true,
                theme: "system" as const,
                language: "en",
              },
              createdAt: user.createdAt?.toISOString() || new Date().toISOString(),
              updatedAt: user.updatedAt?.toISOString() || new Date().toISOString(),
            };

            // Create tokens object
            const tokens = {
              accessToken: token || "",
              refreshToken: "", // Clerk handles refresh automatically
              expiresAt: Date.now() + 60 * 60 * 1000, // 1 hour from now
            };

            // Update session store
            actions.setTokens(tokens);
            actions.updateUser(sessionUser);
            
            // Mark as authenticated
            useSessionStore.setState({
              isAuthenticated: true,
              user: sessionUser,
              tokens: tokens,
              isInitialized: true
            });

            // Identify user in PostHog
            identifyUser(user.id, {
              email: sessionUser.email,
              name: sessionUser.name,
              firstName: sessionUser.firstName,
              lastName: sessionUser.lastName,
              isEmailVerified: sessionUser.isEmailVerified,
              createdAt: sessionUser.createdAt,
              clerk_user_id: user.id,
            });

          } catch (error) {
            console.error("Error syncing Clerk user with session:", error);
          }
        }
      } else {
        // User is not signed in with Clerk
        if (isAuthenticated) {
          // Clear session store
          actions.logout();
        }
      }
    };

    syncClerkWithSession();
  }, [isSignedIn, user, userLoaded, isAuthenticated, actions, getToken]);

  // Update token when Clerk token changes
  useEffect(() => {
    const updateToken = async () => {
      if (isSignedIn && isAuthenticated) {
        try {
          const token = await getToken();
          if (token) {
            const tokens = {
              accessToken: token,
              refreshToken: "",
              expiresAt: Date.now() + 60 * 60 * 1000, // 1 hour from now
            };
            actions.setTokens(tokens);
          }
        } catch (error) {
          console.error("Error updating token:", error);
        }
      }
    };

    // Update token every 30 minutes
    const interval = setInterval(updateToken, 30 * 60 * 1000);
    
    // Update token immediately
    updateToken();

    return () => clearInterval(interval);
  }, [isSignedIn, isAuthenticated, getToken, actions]);

  return <>{children}</>;
}
