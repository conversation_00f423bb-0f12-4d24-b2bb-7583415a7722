"use client";

import { useSidebar } from "@/components/ui/sidebar";

interface ProgressBarProps {
  progress: number;
}

export function ProgressBar({ progress }: ProgressBarProps) {
  const { isMobile } = useSidebar();

  if (isMobile) {
    // Mobile version - square progress icon
    return (
      <div className="w-10 h-10 rounded-sm border border-[var(--progress-border)] bg-[var(--progress-bg)] hover:bg-[var(--primary-light)] flex items-center justify-center   transition-colors duration-200">
        <span className="text-xs font-medium text-[var(--progress-text)]">
          {progress}%
        </span>
      </div>
    );
  }

  return (
    <div className="w-full h-20 border-[var(--progress-border)] relative flex items-center">
      <div className="w-full max-w-md">
        {/* Progress title and percentage */}
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Progress
          </span>
          <span className="text-xs  text-[var(--progress-text)]">
            {progress}%
          </span>
        </div>
        {/* Progress bar - 75% thinner (2.5px instead of 10px) */}
        <div className="w-full h-2.5 rounded-sm border border-[var(--progress-border)] bg-[var(--progress-bg)] cursor-pointer relative overflow-hidden">
          {/* Progress fill with striped pattern */}
          <div
            className="h-full rounded-sm transition-all duration-300 relative"
            style={{
              width: `${progress}%`,
              backgroundColor: "var(--progress-fill)",
            }}
          >
            {/* Horizontal striped pattern on progress fill */}
            <div
              className="absolute inset-0 opacity-20"
              style={{
                backgroundImage: `repeating-linear-gradient(
                    45deg,
                    transparent,
                    transparent 1px,
                    rgba(255, 255, 255, 0.3) 1px,
                    rgba(255, 255, 255, 0.3) 2px
                  )`,
              }}
            />
          </div>

          {/* Horizontal lines with angles on empty space */}
          <div
            className="absolute inset-0 opacity-30"
            style={{
              backgroundImage: `repeating-linear-gradient(
                  45deg,
                  transparent,
                  transparent 1.5px,
                  rgba(156, 163, 175, 0.4) 1.5px,
                  rgba(156, 163, 175, 0.4) 3px
                )`,
            }}
          />
        </div>
      </div>
    </div>
  );
}
