"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from "@/components/ui/card";
import { useProjectTopics } from "@/hooks/queries/useTopics";

type TopicsSectionProps = {
  projectId: string;
};

export function TopicsSection({ projectId }: TopicsSectionProps) {
  const { data = {}, isLoading, error } = useProjectTopics(projectId);

  const groupEntries = Object.entries(data ?? {});
  console.log("[TopicsSection] topics", {
    groups: groupEntries.map(([g, arr]) => ({ group: g, count: Array.isArray(arr) ? arr.length : 0 })),
  });

  if (isLoading) {
    return (
      <div className="mt-6 text-sm text-muted-foreground">Loading topics…</div>
    );
  }

  if (error) {
    return (
      <div className="mt-6 text-sm text-red-600">Failed to load topics</div>
    );
  }

  if (!groupEntries.length) {
    return null;
  }

  return (
    <div className="mt-6">
      <h2 className="text-base font-semibold mb-2">Topics</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
        {groupEntries.map(([group, topics]) => (
          <Card key={group} className="bg-white dark:bg-card border border-border/60">
            <CardHeader className="py-2 px-4">
              <CardTitle className="text-sm font-medium truncate">{group}</CardTitle>
            </CardHeader>
            <CardContent className="py-2 px-4">
              <ul className="space-y-1">
                {Array.isArray(topics) && topics.map((t: { id: number | string; title: string }) => (
                  <li key={t.id} className="text-sm text-gray-700 dark:text-gray-300 truncate">
                    {t.title}
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}


