"use client";

import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Info } from "lucide-react";
import { HARD_CODED_BUSINESS_ITEM_QD } from "@/mockdata/businessItemQuestions";

export function BusinessItemQuestionHeader({
  itemId,
  itemTitle,
  projectName = "siift",
  className,
}: {
  itemId: string;
  itemTitle?: string;
  projectName?: string;
  className?: string;
}) {
  const titleKey = (() => {
    if (!itemTitle) return undefined;
    const t = itemTitle.toLowerCase();
    if (t.includes("unique value") || t.includes("uvp")) return "unique-value-proposition";
    if (t.includes("market size") || t.includes("tam") || t.includes("market")) return "market-size";
    if (t.includes("problem")) return "problem";
    if (t.includes("audience") || t.includes("customer") || t.includes("users")) return "audience";
    if (t.includes("alternative") || t.includes("competitor")) return "alternatives";
    if (t.includes("trend") || t.includes("signal")) return "trends";
    if (t.includes("why")) return "why";
    if (t.includes("advantage")) return "advantages";
    if (t.includes("product") || t.includes("service")) return "product";
    if (t.includes("tech") || t.includes("architecture") || t.includes("technical")) return "tech";
    if (t.includes("package") || t.includes("module") || t.includes("feature")) return "packages";
    if (t.includes("positioning")) return "positioning";
    if (t.includes("channel")) return "channels";
    if (t.includes("messaging") || t.includes("message")) return "messaging";
    return undefined;
  })();

  const qd = HARD_CODED_BUSINESS_ITEM_QD[itemId] || (titleKey ? HARD_CODED_BUSINESS_ITEM_QD[titleKey] : undefined);
  if (!qd) return null;

  return (
    <div className={`flex items-center gap-2 ${className || ""}`}>
      <div className="text-sm font-semibold text-[var(--brand-dark)] dark:text-[var(--primary)]">
        {qd.question.replace("{PROJECT NAME}", projectName)}
      </div>
      <Popover>
        <PopoverTrigger>
          <span className="text-muted-foreground hover:text-foreground inline-flex">
          <Info className="w-4 h-4" />
          </span>
        </PopoverTrigger>
        <PopoverContent align="start" side="bottom" className="w-80">
          <div className="space-y-1">
            <div className="text-sm font-medium">Description</div>
            <p className="text-xs text-muted-foreground">{qd.description}</p>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
}


