"use client";

import { ChatBubble, ChatBubbleMessage } from "@/components/ui/chat-bubble";
import { Logo } from "@/components/ui/logo";
import { Progress } from "@/components/ui/progress";
import type { ChatMessage } from "@/types/Chat.types";
import { AnimatePresence, motion } from "framer-motion";
import { useEffect, useState } from "react";

interface SimulatedOnboardingWorkspaceProps {
  projectName: string;
  onComplete: () => void;
}

// Onboarding messages that appear every 10 seconds
const ONBOARDING_MESSAGES = [
  "Welcome to your project workspace!",
  'The "sift-table" on the right is where all your business information will be organized and acted upon, step by step.',
  "As you ideate, iterate and validate topics, more will unlock and your progress bar will increase.",
  "Our bots are currently researching your idea on the web, to get a big picture view of the opportunity - this may take a minute!",
  "Once the first round of research is done, you'll see the first few topics fill in -->",
];

// Mock business items that appear progressively
const MOCK_BUSINESS_ITEMS = [
  {
    id: "problem",
    title: "Problem",
    description: "Define the core problem you're solving",
    status: "idea",
    priority: "high",
    items: [
      {
        id: "p1",
        title: "Market pain point",
        description: "Identify the main issue customers face",
      },
      {
        id: "p2",
        title: "Problem validation",
        description: "Confirm the problem exists",
      },
    ],
  },
  {
    id: "audience",
    title: "Audience",
    description: "Identify your target customers",
    status: "action",
    priority: "high",
    items: [
      {
        id: "a1",
        title: "Customer segments",
        description: "Define primary customer groups",
      },
      {
        id: "a2",
        title: "User personas",
        description: "Create detailed user profiles",
      },
    ],
  },
  {
    id: "solution",
    title: "Solution",
    description: "Outline your proposed solution",
    status: "idea",
    priority: "medium",
    items: [
      {
        id: "s1",
        title: "Core features",
        description: "List essential product features",
      },
      {
        id: "s2",
        title: "Value proposition",
        description: "Define unique value offered",
      },
    ],
  },
];

export function SimulatedOnboardingWorkspace({
  projectName,
  onComplete,
}: SimulatedOnboardingWorkspaceProps) {
  const [currentMessageIndex, setCurrentMessageIndex] = useState(0);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [visibleBusinessItems, setVisibleBusinessItems] = useState<
    typeof MOCK_BUSINESS_ITEMS
  >([]);
  const [progress, setProgress] = useState(0);
  const [isTyping, setIsTyping] = useState(false);
  const [isCompleting, setIsCompleting] = useState(false);

  // Add messages every 10 seconds
  useEffect(() => {
    if (currentMessageIndex >= ONBOARDING_MESSAGES.length) return;

    const timer = setTimeout(
      () => {
        setIsTyping(true);

        // Simulate typing delay
        setTimeout(() => {
          const newMessage: ChatMessage = {
            id: `onboarding-${currentMessageIndex}`,
            user: "Siift AI",
            avatar: "",
            message: ONBOARDING_MESSAGES[currentMessageIndex],
            timestamp: new Date().toLocaleTimeString([], {
              hour: "2-digit",
              minute: "2-digit",
            }),
            isCurrentUser: false,
          };

          setMessages((prev) => [...prev, newMessage]);
          setCurrentMessageIndex((prev) => prev + 1);
          setIsTyping(false);

          // Update progress
          const newProgress =
            ((currentMessageIndex + 1) / ONBOARDING_MESSAGES.length) * 60; // 60% for messages
          setProgress(newProgress);

          // Add business items progressively
          if (currentMessageIndex === 1) {
            setVisibleBusinessItems([MOCK_BUSINESS_ITEMS[0]]);
          } else if (currentMessageIndex === 2) {
            setVisibleBusinessItems([
              MOCK_BUSINESS_ITEMS[0],
              MOCK_BUSINESS_ITEMS[1],
            ]);
          } else if (currentMessageIndex === 3) {
            setVisibleBusinessItems(MOCK_BUSINESS_ITEMS);
            setProgress(100); // Complete progress when all items are shown
          }
        }, 2000); // 2 second typing simulation
      },
      currentMessageIndex === 0 ? 1000 : 10000
    ); // First message after 1s, others after 10s

    return () => clearTimeout(timer);
  }, [currentMessageIndex]);

  // Auto-complete after all messages are shown and some time has passed
  useEffect(() => {
    if (currentMessageIndex >= ONBOARDING_MESSAGES.length) {
      // Show completion message first
      const completionTimer = setTimeout(() => {
        setIsCompleting(true);

        // Add final completion message
        const completionMessage: ChatMessage = {
          id: "completion",
          user: "Siift AI",
          avatar: "",
          message:
            "Perfect! Your workspace is ready. Taking you to your project now...",
          timestamp: new Date().toLocaleTimeString([], {
            hour: "2-digit",
            minute: "2-digit",
          }),
          isCurrentUser: false,
        };

        setMessages((prev) => [...prev, completionMessage]);

        // Navigate after showing completion message
        setTimeout(() => {
          onComplete();
        }, 3000); // Wait 3 seconds after completion message
      }, 2000); // Wait 2 seconds after last onboarding message

      return () => clearTimeout(completionTimer);
    }
  }, [currentMessageIndex, onComplete]);

  return (
    <div className="flex h-screen bg-background">
      {/* Chat Sidebar - Left Side */}
      <div className="w-96 border-r border-border bg-background flex flex-col">
        <div className="p-4 border-b border-border">
          <h3 className="font-semibold">Project Chat</h3>
        </div>

        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          {messages.map((msg) => (
            <ChatBubble key={msg.id} variant="received">
              <div className="h-8 w-8 shrink-0 bg-primary/10 rounded-lg flex items-center justify-center">
                <Logo size={20} />
              </div>
              <ChatBubbleMessage variant="received">
                <div className="text-sm leading-relaxed whitespace-pre-line">
                  {msg.message}
                </div>
              </ChatBubbleMessage>
            </ChatBubble>
          ))}

          {isTyping && (
            <ChatBubble variant="received">
              <div className="h-8 w-8 shrink-0 bg-primary/10 rounded-lg flex items-center justify-center">
                <Logo size={20} />
              </div>
              <ChatBubbleMessage variant="received" isLoading />
            </ChatBubble>
          )}
        </div>

        <div className="p-4 border-t border-border bg-muted/30">
          <div className="bg-gray-200/60 dark:bg-gray-700/60 rounded-xl px-3 py-2 border border-gray-300/50 dark:border-gray-600/50">
            <p className="text-sm text-muted-foreground">
              {isCompleting
                ? "Redirecting to your workspace..."
                : "Onboarding in progress... Please wait while we set up your workspace."}
            </p>
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <div className="border-b border-border p-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold">{projectName}</h1>
              <p className="text-muted-foreground">
                {isCompleting
                  ? "Workspace ready! Redirecting..."
                  : "Getting your workspace ready..."}
              </p>
            </div>
            <div className="w-64">
              <div className="flex items-center gap-2 mb-1">
                <span className="text-sm text-muted-foreground">Progress</span>
                <span className="text-sm font-medium">
                  {Math.round(progress)}%
                </span>
              </div>
              <Progress value={progress} className="h-2" />
            </div>
          </div>
        </div>

        {/* Business Items Grid */}
        <div className="flex-1 p-6 overflow-y-auto bg-gray-50 dark:bg-gray-900">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-xl font-semibold mb-6">Business Sections</h2>

            <AnimatePresence>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {visibleBusinessItems.map((item, index) => (
                  <motion.div
                    key={item.id}
                    initial={{ opacity: 0, y: 20, scale: 0.9 }}
                    animate={{ opacity: 1, y: 0, scale: 1 }}
                    transition={{
                      duration: 0.5,
                      delay: index * 0.2,
                      ease: "easeOut",
                    }}
                    className="bg-white dark:bg-gray-800 rounded-lg border border-border p-6 shadow-sm hover:shadow-md transition-shadow"
                  >
                    <div className="flex items-start justify-between mb-4">
                      <h3 className="text-lg font-semibold">{item.title}</h3>
                      <div
                        className={`px-2 py-1 rounded-full text-xs font-medium ${
                          item.status === "idea"
                            ? "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                            : "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"
                        }`}
                      >
                        {item.status}
                      </div>
                    </div>

                    <p className="text-muted-foreground text-sm mb-4">
                      {item.description}
                    </p>

                    <div className="space-y-2">
                      {item.items.map((subItem) => (
                        <div
                          key={subItem.id}
                          className="p-3 bg-gray-50 dark:bg-gray-700 rounded border"
                        >
                          <div className="font-medium text-sm">
                            {subItem.title}
                          </div>
                          <div className="text-xs text-muted-foreground mt-1">
                            {subItem.description}
                          </div>
                        </div>
                      ))}
                    </div>
                  </motion.div>
                ))}
              </div>
            </AnimatePresence>

            {visibleBusinessItems.length === 0 && (
              <div className="text-center py-12">
                <div className="animate-pulse">
                  <div className="w-16 h-16 bg-gray-200 dark:bg-gray-700 rounded-full mx-auto mb-4"></div>
                  <p className="text-muted-foreground">
                    Analyzing your project...
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Chat Sidebar */}
      <div className="w-96 border-l border-border bg-background flex flex-col">
        <div className="p-4 border-b border-border">
          <h3 className="font-semibold">Project Chat</h3>
        </div>

        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          {messages.map((msg) => (
            <ChatBubble key={msg.id} variant="received">
              <div className="h-8 w-8 shrink-0 bg-primary/10 rounded-lg flex items-center justify-center">
                <Logo size={20} />
              </div>
              <ChatBubbleMessage variant="received">
                <div className="text-sm leading-relaxed whitespace-pre-line">
                  {msg.message}
                </div>
              </ChatBubbleMessage>
            </ChatBubble>
          ))}

          {isTyping && (
            <ChatBubble variant="received">
              <div className="h-8 w-8 shrink-0 bg-primary/10 rounded-lg flex items-center justify-center">
                <Logo size={20} />
              </div>
              <ChatBubbleMessage variant="received" isLoading />
            </ChatBubble>
          )}
        </div>

        <div className="p-4 border-t border-border bg-muted/30">
          <div className="bg-gray-200/60 dark:bg-gray-700/60 rounded-xl px-3 py-2 border border-gray-300/50 dark:border-gray-600/50">
            <p className="text-sm text-muted-foreground">
              {isCompleting
                ? "Redirecting to your workspace..."
                : "Onboarding in progress... Please wait while we set up your workspace."}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
