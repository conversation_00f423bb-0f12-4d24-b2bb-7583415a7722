"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { SidebarButton } from "@/components/ui/sidebar-button";
import { Plus, X } from "lucide-react";

interface ContentSectionsProps {
  activeContent: "drafts" | "files" | null;
  setActiveContent: (content: "drafts" | "files" | null) => void;
  mockDraftItems: any[];
  mockFileItems: any[];
}

export function ContentSections({
  activeContent,
  setActiveContent,
  mockDraftItems,
  mockFileItems,
}: ContentSectionsProps) {
  if (activeContent === "drafts") {
    return null;
  }

  if (activeContent === "files") {
    return null;
  }

  return null;
}
