"use client";

import * as React from "react";
import { Moon, Sun } from "lucide-react";
import { useTheme } from "next-themes";

import { SidebarButton } from "@/components/ui/sidebar-button";
import { ICON_SIZES } from "@/lib/constants";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

export function ThemeToggle() {
  const { setTheme } = useTheme();

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <div>
          <SidebarButton
            icon={Sun}
            variant="ghost"
            size="lg"
            layout="icon-only"
            showBorder={true}
            hoverColor="green"
            hoverScale={true}
            iconClassName={ICON_SIZES.lg}
            className="dark:hidden"
          />
          <SidebarButton
            icon={Moon}
            variant="ghost"
            size="lg"
            layout="icon-only"
            showBorder={true}
            hoverColor="grey"
            hoverScale={true}
            iconClassName={ICON_SIZES.md}
            className="hidden dark:block"
          />
          <span className="sr-only">Toggle theme</span>
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="bg-background border-border">
        <DropdownMenuItem
          onClick={() => setTheme("light")}
          className="hover:bg-primary/10 hover:text-primary focus:bg-primary/10 focus:text-primary"
        >
          Light
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => setTheme("dark")}
          className="hover:bg-primary/10 hover:text-primary focus:bg-primary/10 focus:text-primary"
        >
          Dark
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => setTheme("system")}
          className="hover:bg-primary/10 hover:text-primary focus:bg-primary/10 focus:text-primary"
        >
          System
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
