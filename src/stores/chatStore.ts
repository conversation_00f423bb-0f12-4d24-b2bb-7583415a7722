import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import type { ChatStore, ChatMessage, ChatSession } from '../types/Chat.types';
import { safeLocalStorage } from '../lib/storage';

export const useChatStore = create<ChatStore>()(
  devtools(
    (set, get) => ({
      messages: [],
      chatSession: null,
      isLoading: false,
      isStreaming: false,
      projectLoadingStates: {},
      projectStreamingStates: {},

      setMessages: (messages: ChatMessage[]) => {
        set({ messages });
        // Save to localStorage whenever messages are set
        const state = get();
        if (state.chatSession?.projectId) {
          safeLocalStorage.setJSON(`chat_messages_${state.chatSession.projectId}`, messages);
        }
      },

      addMessage: (message: ChatMessage) => {
        set((state: ChatStore) => {
          const newMessages = [...state.messages, message];
          // Save to localStorage when adding message
          if (state.chatSession?.projectId) {
            safeLocalStorage.setJSON(`chat_messages_${state.chatSession.projectId}`, newMessages);
          }
          return { messages: newMessages };
        });
      },
      
      addCtaMessage: (text: string, cta: { type: 'refetch_topics'; label?: string }) => {
        set((state: ChatStore) => {
          const message: ChatMessage = {
            id: `ai-${Date.now()}`,
            user: 'Siift AI',
            avatar: '',
            message: text,
            timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
            isCurrentUser: false,
            cta,
          };
          const newMessages = [...state.messages, message];
          if (state.chatSession?.projectId) {
            safeLocalStorage.setJSON(`chat_messages_${state.chatSession.projectId}`, newMessages);
          }
          return { messages: newMessages };
        });
      },

      updateLastMessage: (content: string) => {
        set((state: ChatStore) => {
          let newMessages;
          if (state.messages.length === 0 || state.messages[state.messages.length - 1].isCurrentUser) {
            // Create new AI message
            const newMessage: ChatMessage = {
              id: `ai-${Date.now()}`,
              user: "Siift AI",
              avatar: "",
              message: content,
              timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
              isCurrentUser: false,
            };
            newMessages = [...state.messages, newMessage];
          } else {
            // Update last AI message
            const lastMessage = state.messages[state.messages.length - 1];
            const updatedLastMessage = { ...lastMessage, message: content };
            newMessages = [...state.messages.slice(0, -1), updatedLastMessage];
          }
          
          // Save to localStorage when updating message
          if (state.chatSession?.projectId) {
            safeLocalStorage.setJSON(`chat_messages_${state.chatSession.projectId}`, newMessages);
          }
          
          return { messages: newMessages };
        });
      },

      appendToLastMessage: (contentChunk: string) => {
        set((state: ChatStore) => {
          if (state.messages.length === 0 || state.messages[state.messages.length - 1].isCurrentUser) {
            // nothing to append to yet
            return {} as any;
          }
          const last = state.messages[state.messages.length - 1];
          const updatedLast = { ...last, message: (last.message || '') + contentChunk };
          const newMessages = [...state.messages.slice(0, -1), updatedLast];
          if (state.chatSession?.projectId) {
            safeLocalStorage.setJSON(`chat_messages_${state.chatSession.projectId}`, newMessages);
          }
          return { messages: newMessages } as any;
        });
      },
      
      setChatSession: (session: ChatSession | null) => {
        set({ chatSession: session });
      },

      setIsLoading: (isLoading: boolean) => {
        set({ isLoading });
      },
      
      setIsStreaming: (isStreaming: boolean) => {
        set({ isStreaming });
      },

      setProjectLoading: (projectId: string, isLoading: boolean) => {
        set((state) => ({
          projectLoadingStates: { ...state.projectLoadingStates, [projectId]: isLoading }
        }));
      },

      setProjectStreaming: (projectId: string, isStreaming: boolean) => {
        set((state) => ({
          projectStreamingStates: { ...state.projectStreamingStates, [projectId]: isStreaming }
        }));
      },

      getProjectLoading: (projectId: string) => {
        const state = get();
        return state.projectLoadingStates[projectId] || false;
      },

      getProjectStreaming: (projectId: string) => {
        const state = get();
        return state.projectStreamingStates[projectId] || false;
      },

      clearChat: () => {
        set({ messages: [], chatSession: null });
      },
    }),
    {
      name: 'chat-store',
    }
  )
);