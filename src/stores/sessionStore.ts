import { create } from "zustand";
import { devtools, persist, subscribeWithSelector } from "zustand/middleware";

import { initializeAdminApi } from "@/lib/admin-api";
import { MockAuthAPI } from "@/lib/mock-auth-api";
import { TokenStorage } from "@/lib/tokenStorage";
import type {
  AuthError,
  AuthTokens,
  LoginCredentials,
  SessionData,
  SessionStore,
  SignupCredentials,
  User,
} from "@/types/Session.types";

// Initial state
const initialState: SessionData = {
  user: null,
  tokens: null,
  isAuthenticated: false,
  isInitialized: false,
  pendingEmailVerification: undefined,
  emailVerificationSent: false,
};

// Token refresh timer
let refreshTimer: NodeJS.Timeout | null = null;

// Setup token refresh
const setupTokenRefresh = (expiresAt: number) => {
  if (refreshTimer) {
    clearTimeout(refreshTimer);
  }

  const now = Date.now();
  const timeUntilExpiry = expiresAt - now;
  const refreshTime = Math.max(timeUntilExpiry - 5 * 60 * 1000, 60 * 1000); // 5 minutes before expiry, minimum 1 minute

  if (refreshTime > 0) {
    refreshTimer = setTimeout(() => {
      useSessionStore.getState().actions.refreshToken();
    }, refreshTime);
  }
};

export const useSessionStore = create<SessionStore>()(
  devtools(
    subscribeWithSelector(
      persist(
        (set, get) => ({
          ...initialState,
          isLoading: false,
          error: null,

          actions: {
            login: async (credentials: LoginCredentials) => {
              set({ isLoading: true, error: null });

              try {
                // Use mock API for development
                const response = await MockAuthAPI.login(
                  credentials.email,
                  credentials.password
                );

                // Store tokens securely
                TokenStorage.setTokens(response.tokens);
                TokenStorage.setUser(response.user);

                // Initialize admin API if user has access token
                if (response.tokens.accessToken) {
                  try {
                    initializeAdminApi(response.tokens.accessToken);
                  } catch (error) {
                    console.warn("Failed to initialize admin API:", error);
                  }
                }

                set({
                  user: response.user,
                  tokens: response.tokens,
                  isAuthenticated: true,
                  isLoading: false,
                  error: null,
                });

                // Set up token refresh timer
                setupTokenRefresh(response.tokens.expiresAt);
              } catch (error) {
                const authError: AuthError = {
                  code: "LOGIN_FAILED",
                  message:
                    error instanceof Error ? error.message : "Login failed",
                };
                set({ error: authError, isLoading: false });
                throw error;
              }
            },

            signup: async (credentials: SignupCredentials) => {
              set({ isLoading: true, error: null });

              try {
                // Use mock API for development
                const response = await MockAuthAPI.register(
                  credentials.email,
                  credentials.password,
                  credentials.name.split(" ")[0] || "User",
                  credentials.name.split(" ").slice(1).join(" ") || ""
                );

                // Store tokens securely
                TokenStorage.setTokens(response.tokens);
                TokenStorage.setUser(response.user);

                set({
                  user: response.user,
                  tokens: response.tokens,
                  isAuthenticated: true,
                  isLoading: false,
                  error: null,
                });

                // Set up token refresh timer
                setupTokenRefresh(response.tokens.expiresAt);
              } catch (error) {
                const authError: AuthError = {
                  code: "SIGNUP_FAILED",
                  message:
                    error instanceof Error ? error.message : "Signup failed",
                };
                set({ error: authError, isLoading: false });
                throw error;
              }
            },

            logout: () => {
              // Clear refresh timer
              if (refreshTimer) {
                clearTimeout(refreshTimer);
                refreshTimer = null;
              }

              // Clear stored data
              TokenStorage.clearAll();

              set({
                ...initialState,
                isInitialized: true,
              });
            },

            refreshToken: async () => {
              try {
                const currentTokens = TokenStorage.getTokens();
                if (!currentTokens?.refreshToken) {
                  throw new Error("No refresh token available");
                }

                const response = await MockAuthAPI.refreshToken(
                  currentTokens.refreshToken
                );

                // Store new tokens
                TokenStorage.setTokens(response.tokens);

                set({
                  tokens: response.tokens,
                });

                // Set up next refresh
                setupTokenRefresh(response.tokens.expiresAt);
              } catch (error) {
                // Refresh failed, logout user
                get().actions.logout();
                throw error;
              }
            },

            initializeSession: async () => {
              try {
                // Set loading state
                set({ isLoading: true });

                const storedTokens = TokenStorage.getTokens();
                const storedUser = TokenStorage.getUser();

                if (storedTokens && storedUser) {
                  // Check if tokens are still valid
                  if (storedTokens.expiresAt > Date.now()) {
                    // Initialize admin API if user has access token
                    if (storedTokens.accessToken) {
                      try {
                        initializeAdminApi(storedTokens.accessToken);
                      } catch (error) {
                        console.warn("Failed to initialize admin API:", error);
                      }
                    }

                    set({
                      user: storedUser,
                      tokens: storedTokens,
                      isAuthenticated: true,
                      isInitialized: true,
                      isLoading: false,
                    });

                    // Set up token refresh
                    setupTokenRefresh(storedTokens.expiresAt);
                    return;
                  } else {
                    // Tokens expired, clear them
                    TokenStorage.clearAll();
                  }
                }

                // No valid tokens found - initialize as unauthenticated
                set({
                  ...initialState,
                  isInitialized: true,
                  isLoading: false,
                });
              } catch (error) {
                console.error("Session initialization failed:", error);
                // Always ensure initialization completes
                set({
                  ...initialState,
                  isInitialized: true,
                  isLoading: false,
                });
              }
            },

            clearSession: () => {
              if (refreshTimer) {
                clearTimeout(refreshTimer);
                refreshTimer = null;
              }

              TokenStorage.clearAll();
              set(initialState);
            },

            updateUser: (userData: Partial<User>) => {
              const currentUser = get().user;
              if (currentUser) {
                const updatedUser = { ...currentUser, ...userData };
                TokenStorage.setUser(updatedUser);
                set({ user: updatedUser });
              }
            },

            setTokens: (tokens: AuthTokens) => {
              TokenStorage.setTokens(tokens);
              set({ tokens });
              setupTokenRefresh(tokens.expiresAt);
            },

            clearTokens: () => {
              if (refreshTimer) {
                clearTimeout(refreshTimer);
                refreshTimer = null;
              }

              TokenStorage.clearTokens();
              set({ tokens: null, isAuthenticated: false });
            },

            sendEmailVerification: async (email: string, name: string) => {
              set({ isLoading: true, error: null });

              try {
                const response = await MockAuthAPI.sendEmailVerification(
                  email,
                  name
                );
                set({
                  isLoading: false,
                  pendingEmailVerification: email,
                  emailVerificationSent: true,
                });
              } catch (error) {
                const authError: AuthError = {
                  code: "EMAIL_VERIFICATION_FAILED",
                  message:
                    error instanceof Error
                      ? error.message
                      : "Failed to send verification email",
                };
                set({ error: authError, isLoading: false });
                throw error;
              }
            },

            verifyEmail: async (email: string, code: string) => {
              set({ isLoading: true, error: null });

              try {
                await MockAuthAPI.verifyEmail(email, code);

                // Update user's email verification status
                const currentUser = get().user;
                if (currentUser && currentUser.email === email) {
                  const updatedUser = { ...currentUser, isEmailVerified: true };
                  TokenStorage.setUser(updatedUser);
                  set({
                    user: updatedUser,
                    pendingEmailVerification: undefined,
                    emailVerificationSent: false,
                    isLoading: false,
                  });
                } else {
                  set({
                    pendingEmailVerification: undefined,
                    emailVerificationSent: false,
                    isLoading: false,
                  });
                }
              } catch (error) {
                const authError: AuthError = {
                  code: "EMAIL_VERIFICATION_FAILED",
                  message:
                    error instanceof Error
                      ? error.message
                      : "Email verification failed",
                };
                set({ error: authError, isLoading: false });
                throw error;
              }
            },

            resendEmailVerification: async (email: string) => {
              set({ isLoading: true, error: null });

              try {
                const response = await MockAuthAPI.resendEmailVerification(
                  email
                );
                set({
                  isLoading: false,
                  emailVerificationSent: true,
                });
              } catch (error) {
                const authError: AuthError = {
                  code: "EMAIL_VERIFICATION_FAILED",
                  message:
                    error instanceof Error
                      ? error.message
                      : "Failed to resend verification email",
                };
                set({ error: authError, isLoading: false });
                throw error;
              }
            },

            setLoading: (loading: boolean) => {
              set({ isLoading: loading });
            },

            setError: (error: AuthError | null) => {
              set({ error });
            },
          },
        }),
        {
          name: "session-store",
          partialize: (state) => ({
            // Persist authentication state but not sensitive tokens
            user: state.user,
            isAuthenticated: state.isAuthenticated,
            isInitialized: state.isInitialized,
            // Don't persist tokens - they're handled by TokenStorage
            // Don't persist loading/error states
          }),
        }
      )
    ),
    {
      name: "session-store",
    }
  )
);

// Export actions for easier access
export const sessionActions = () => useSessionStore.getState().actions;
