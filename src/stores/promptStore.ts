"use client";

import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { safeLocalStorage } from '@/lib/storage';

type PromptStoreState = {
  prompts: Record<string, string>;
  lastPromptId: string | null;
  savePrompt: (text: string) => string;
  getPrompt: (id: string) => string | null;
  removePrompt: (id: string) => void;
  clear: () => void;
};

export const usePromptStore = create<PromptStoreState>()(
  devtools(
    (set, get) => ({
      prompts: safeLocalStorage.getJSON<Record<string, string>>('prompt_store_prompts', {}) || {},
      lastPromptId: safeLocalStorage.getItem('prompt_store_last_id') || null,

      savePrompt: (text: string) => {
        const id = `${Date.now()}-${Math.random().toString(36).slice(2, 8)}`;
        set((state) => {
          const next = { ...state.prompts, [id]: text };
          try {
            safeLocalStorage.setJSON('prompt_store_prompts', next);
            safeLocalStorage.setItem('prompt_store_last_id', id);
          } catch {}
          return { prompts: next, lastPromptId: id };
        });
        return id;
      },

      getPrompt: (id: string) => {
        const state = get();
        return state.prompts[id] ?? null;
      },

      removePrompt: (id: string) => {
        set((state) => {
          const next = { ...state.prompts };
          delete next[id];
          try { safeLocalStorage.setJSON('prompt_store_prompts', next); } catch {}
          const lastPromptId = state.lastPromptId === id ? null : state.lastPromptId;
          if (state.lastPromptId === id) {
            try { safeLocalStorage.removeItem('prompt_store_last_id'); } catch {}
          }
          return { prompts: next, lastPromptId };
        });
      },

      clear: () => {
        set({ prompts: {}, lastPromptId: null });
        try {
          safeLocalStorage.removeItem('prompt_store_prompts');
          safeLocalStorage.removeItem('prompt_store_last_id');
        } catch {}
      },
    }),
    { name: 'prompt-store' }
  )
);


