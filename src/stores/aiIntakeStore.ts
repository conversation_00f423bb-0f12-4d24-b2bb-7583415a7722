import { create } from 'zustand';

export type IntakeStage = 'idle' | 'streaming_intake' | 'ingesting' | 'ready';

type AiIntakeState = {
  sessionId: string | null;
  stage: IntakeStage;
  progress: string[];
  insertedCount: number | null;
  error: string | null;
  siiftReady: boolean;
  setSessionId: (id: string | null) => void;
  setStage: (stage: IntakeStage) => void;
  appendProgress: (line: string) => void;
  setInsertedCount: (n: number | null) => void;
  setError: (msg: string | null) => void;
  setSiiftReady: (ready: boolean) => void;
  reset: () => void;
};

export const useAiIntakeStore = create<AiIntakeState>((set) => ({
  sessionId: null,
  stage: 'idle',
  progress: [],
  insertedCount: null,
  error: null,
  siiftReady: false,
  setSessionId: (id) => set({ sessionId: id }),
  setStage: (stage) => set({ stage }),
  appendProgress: (line) => set((s) => ({ progress: [...s.progress, line] })),
  setInsertedCount: (n) => set({ insertedCount: n }),
  setError: (msg) => set({ error: msg }),
  setSiiftReady: (ready) => set({ siiftReady: ready }),
  reset: () => set({ sessionId: null, stage: 'idle', progress: [], insertedCount: null, error: null, siiftReady: false }),
}));


