"use client";

import { useAuth } from "@clerk/nextjs";
import { useCallback, useEffect, useState } from "react";

export function useClerkToken() {
  const { getToken, isSignedIn } = useAuth();
  const [token, setToken] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  const fetchAndLogToken = useCallback(async (logToConsole = true) => {
    if (!isSignedIn) {
      if (logToConsole) console.log("❌ User not signed in");
      return null;
    }

    setLoading(true);
    try {
      const currentToken = await getToken();
      setToken(currentToken);

      if (logToConsole && currentToken) {
        console.group("🔐 Clerk Token Retrieved");
        console.log("🎫 Token:", currentToken);
        console.log("📏 Length:", currentToken.length);
        console.log("⏰ Retrieved at:", new Date().toISOString());
        
        // Decode and show token info
        try {
          const payload = JSON.parse(atob(currentToken.split('.')[1]));
          console.log("🆔 Subject (User ID):", payload.sub);
          console.log("📅 Issued At:", new Date(payload.iat * 1000));
          console.log("⏰ Expires At:", new Date(payload.exp * 1000));
          console.log("🏢 Issuer:", payload.iss);
          console.log("📋 Full Payload:", payload);
        } catch (e) {
          console.log("Could not decode JWT payload");
        }
        console.groupEnd();
      }

      return currentToken;
    } catch (error) {
      console.error("❌ Error fetching token:", error);
      return null;
    } finally {
      setLoading(false);
    }
  }, [getToken, isSignedIn]);

  // Auto-fetch token when user signs in
  useEffect(() => {
    if (isSignedIn) {
      fetchAndLogToken(true);
    }
  }, [isSignedIn, fetchAndLogToken]);

  const getTokenSilently = useCallback(async () => {
    return await fetchAndLogToken(false);
  }, [fetchAndLogToken]);

  const logCurrentToken = useCallback(() => {
    if (token) {
      console.group("🔐 Current Stored Token");
      console.log("🎫 Token:", token);
      console.log("📏 Length:", token.length);
      console.groupEnd();
    } else {
      console.log("❌ No token currently stored");
    }
  }, [token]);

  return {
    token,
    loading,
    isSignedIn,
    
    // Methods
    fetchAndLogToken,
    getTokenSilently,
    logCurrentToken,
    
    // Utility methods
    copyTokenToClipboard: () => {
      if (token) {
        navigator.clipboard.writeText(token);
        console.log("📋 Token copied to clipboard");
      }
    },
    
    // Token info
    tokenLength: token?.length || 0,
    hasToken: !!token,
  };
}

// Global function to quickly get and log token from anywhere
export const logClerkToken = async () => {
  // This is a utility function that can be called from browser console
  if (typeof window !== 'undefined') {
    console.log("Use the useClerkToken hook in a React component for full functionality");
  }
};
