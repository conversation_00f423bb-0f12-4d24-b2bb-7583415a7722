"use client";

import { useCallback, useEffect, useRef } from 'react';
import { useAiIntakeStore } from '@/stores/aiIntakeStore';
import { useChatStore } from '@/stores/chatStore';
import { safeLocalStorage } from '@/lib/storage';
import { useQueryClient } from '@tanstack/react-query';

export function useAiIntake() {
  const {
    sessionId,
    stage,
    progress,
    insertedCount,
    error,
    setSessionId,
    setStage,
    appendProgress,
    setInsertedCount,
    setError,
    setSiiftReady,
    reset,
  } = useAiIntakeStore();

  const esRef = useRef<EventSource | null>(null);
  const currentProjectIdRef = useRef<string | null>(null);
  const queryClient = useQueryClient();

  // Chat store hooks for live feedback in chat UI
  const {
    addMessage,
    updateLastMessage,
    appendToLastMessage,
    setProjectLoading,
    setProjectStreaming,
    addCtaMessage,
  } = useChatStore();

  const ensureAiMessage = useCallback(() => {
    const projectId = currentProjectIdRef.current;
    if (!projectId) return;
    // Start an AI message frame if the last message is from user or there are no messages
    updateLastMessage("");
  }, [updateLastMessage]);

  const openStream = useCallback((sessId: string, projectId?: string) => {
    console.log('[useAiIntake] openStream', { sessId, projectId });
    if (projectId) currentProjectIdRef.current = projectId;
    // Use the SSE events endpoint from OpenAPI spec
    const es = new EventSource(`/api/ai-chat/sessions/${sessId}/events`, { withCredentials: true });
    esRef.current = es;

    const add = (msg: string) => appendProgress(msg);

    es.addEventListener('token_stream', (ev) => {
      try {
        const data = JSON.parse((ev as MessageEvent).data);
        if (data.tokens && Array.isArray(data.tokens)) {
          add(`[${data.agent_name || 'AI'}] ${data.tokens.join('')}`);
          ensureAiMessage();
          const projectId = currentProjectIdRef.current;
          if (projectId) setProjectStreaming(projectId, true);
          appendToLastMessage?.(data.tokens.join(''));
        }
      } catch {}
    });

    es.addEventListener('token', (ev) => {
      try {
        const data = JSON.parse((ev as MessageEvent).data);
        const chunk = String(data.content || data.token || '');
        add(`[${data.agent || data.agent_name || 'AI'}] ${chunk}`);
        ensureAiMessage();
        const projectId = currentProjectIdRef.current;
        if (projectId) setProjectStreaming(projectId, true);
        appendToLastMessage?.(chunk);
      } catch {}
    });

    es.addEventListener('context_update', (ev) => {
      try {
        const data = JSON.parse((ev as MessageEvent).data);
        add(`context: ${data.field} = ${data.value}`);
      } catch {}
    });

    es.addEventListener('siift_progress', (ev) => {
      try {
        const data = JSON.parse((ev as MessageEvent).data);
        add(String(data.progress ?? ''));
      } catch {}
    });

    es.addEventListener('complete', () => {
      console.log('[useAiIntake] stream_complete');
      setStage('ingesting');
      add('Stream complete, starting ingestion...');
      const projectId = currentProjectIdRef.current;
      if (projectId) setProjectStreaming(projectId, false);
    });

    es.addEventListener('stream_complete', () => {
      console.log('[useAiIntake] stream_complete');
      setStage('ingesting');
      add('Stream complete, starting ingestion...');
      const projectId = currentProjectIdRef.current;
      if (projectId) setProjectStreaming(projectId, false);
    });

    es.addEventListener('siift_ingest', (ev) => {
      try {
        const data = JSON.parse((ev as MessageEvent).data);
        setInsertedCount(Number(data.inserted ?? 0));
        add(`ingested ${data.inserted ?? 0} entries...`);
        ensureAiMessage();
        appendToLastMessage?.(`\nIngested ${data.inserted ?? 0} entries...`);
      } catch {}
    });

    es.addEventListener('siift_ingest_complete', (ev) => {
      try {
        const data = JSON.parse((ev as MessageEvent).data);
        setInsertedCount(Number(data.inserted ?? 0));
        add(`ingestion complete - ${data.inserted ?? 0} entries, ${data.topics_covered ?? 0} topics`);
      } catch {}
      setStage('ready');
      setSiiftReady(true);
      const projectId = currentProjectIdRef.current;
      if (projectId) {
        setProjectStreaming(projectId, false);
        // Kick off aggressive refetch loop for topics/entries until they appear
        let attempts = 0;
        const interval = setInterval(async () => {
          attempts += 1;
          await queryClient.invalidateQueries({ predicate: (q) => Array.isArray(q.queryKey) && String(q.queryKey[0]).includes('topic') });
          await queryClient.refetchQueries({ predicate: (q) => Array.isArray(q.queryKey) && String(q.queryKey[0]).includes('topic') });
          if (attempts >= 10) clearInterval(interval);
        }, 1500);

        // Inject CTA into chat to let user trigger a manual refetch/polling run
        try {
          if (typeof addCtaMessage === 'function') {
            addCtaMessage(
              'Ingestion complete. Click Start Siifting to pull topics and entries into your workspace.',
              { type: 'refetch_topics', label: 'Start Siifting' }
            );
          }
        } catch {}
      }
    });

    es.addEventListener('siift_ingest_error', (ev) => {
      console.log('[useAiIntake] siift_ingest_error');
      try {
        const data = JSON.parse((ev as MessageEvent).data);
        setError(`Ingestion failed: ${data.error || 'Unknown error'}`);
      } catch {
        setError('Ingestion failed');
      }
      setStage('ready');
    });

    // Handle keepalive events (ignore them)
    es.addEventListener('keepalive', () => {
      // Ignore keepalive events
    });

    es.addEventListener('error', () => {
      console.log('[useAiIntake] event-source error');
      const projectId = currentProjectIdRef.current;
      if (projectId) setProjectStreaming(projectId, false);
      if (stage !== 'ready') setError('Stream connection error');
    });
  }, [appendProgress, setError, setInsertedCount, setSiiftReady, setStage, stage, ensureAiMessage, appendToLastMessage, setProjectStreaming, queryClient]);

  const cleanup = useCallback(() => {
    esRef.current?.close();
    esRef.current = null;
  }, []);

  useEffect(() => () => cleanup(), [cleanup]);

  const startIntake = useCallback(async (projectId: string, message: string) => {
    console.log('[useAiIntake] startIntake', { projectId, message });
    setStage('streaming_intake');
    // reset except keep session if existing
    reset();
    setStage('streaming_intake');

    try {
      let sessId = null;
      
      // 1) First try to find existing active session for this project
      try {
          const existingResp = await fetch(`/api/ai-chat/projects/${projectId}/sessions`, {
          credentials: 'include',
        });
        
        if (existingResp.ok) {
          const sessions = await existingResp.json();
          console.log('[useAiIntake] Existing sessions:', sessions);
          
          // Find the latest active session
          const activeSessions: Array<Record<string, unknown>> = Array.isArray(sessions)
            ? sessions
            : Array.isArray((sessions as any)?.data)
            ? (sessions as any).data
            : [];
          const activeSession = activeSessions.find((s) => (s as any)?.status === 'active') as any;
          
          if (activeSession) {
            sessId = activeSession.id || activeSession.session_id || activeSession.sessionId;
            console.log('[useAiIntake] Found existing session:', sessId);
          }
        }
      } catch (error) {
        console.log('[useAiIntake] No existing sessions found, will create new one');
      }

      // 2) If no existing session, create a new one
      if (!sessId) {
        const resp = await fetch('/api/ai-chat/sessions', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ projectId }),
          credentials: 'include',
        });
        
        if (!resp.ok) {
          const errorText = await resp.text();
          console.error('[useAiIntake] Session creation failed:', resp.status, resp.statusText, errorText);
          setError(`Failed to create AI session: ${resp.status} ${resp.statusText}`);
          setStage('ready');
          return;
        }
        
        const json = await resp.json();
        console.log('[useAiIntake] Session response:', json);
        sessId = json?.session_id ?? json?.id ?? json?.data?.id ?? json?.sessionId;
        
        if (!sessId) {
          console.error('[useAiIntake] No session ID in response:', json);
          setError('Failed to get session ID from response');
          setStage('ready');
          return;
        }
        
        console.log('[useAiIntake] Created new session:', sessId);
      }
      
      setSessionId(sessId);
      try { safeLocalStorage.setItem(`intake_session_${projectId}`, sessId); } catch {}

      // 3) open SSE
      openStream(sessId, projectId);

      // 4) kick off intake using the proper chat endpoint with agent_stage
      const chatResponse = await fetch(`/api/ai-chat/sessions/${sessId}/chat`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          content: message,
          message: message,
          agent_stage: "INTAKE"
        }),
        credentials: 'include',
      });

      if (!chatResponse.ok) {
        const errorText = await chatResponse.text();
        console.error('[useAiIntake] Chat failed:', chatResponse.status, chatResponse.statusText, errorText);
        setError(`Failed to start AI chat: ${chatResponse.status} ${chatResponse.statusText}`);
        setStage('ready');
        return;
      }
      
      console.log('[useAiIntake] Chat started successfully');
    } catch (error) {
      console.error('[useAiIntake] Error in startIntake:', error);
      setError(error instanceof Error ? error.message : 'Unknown error occurred');
      setStage('ready');
    }
  }, [openStream, reset, setError, setSessionId, setStage]);

  const resumeIntake = useCallback(async (projectId: string) => {
    try {
      currentProjectIdRef.current = projectId;
      // Try local cache first
      const cached = safeLocalStorage.getItem(`intake_session_${projectId}`);
      if (cached) {
        setSessionId(cached);
        openStream(cached, projectId);
        setStage('streaming_intake');
        return;
      }

      // Fallback to backend discovery
      const existingResp = await fetch(`/api/ai-chat/projects/${projectId}/sessions`, { credentials: 'include' });

      if (existingResp.ok) {
        const sessions = await existingResp.json();
        const list: Array<Record<string, unknown>> = Array.isArray(sessions)
          ? sessions
          : Array.isArray((sessions as any)?.data)
          ? (sessions as any).data
          : [];
        const active = list.find((item) => (item as any)?.status === 'active') as any;
        const sessId = active?.id || active?.session_id || active?.sessionId;
        if (sessId) {
          setSessionId(sessId);
          try { safeLocalStorage.setItem(`intake_session_${projectId}`, sessId); } catch {}
          openStream(sessId, projectId);
          setStage('streaming_intake');
        }
      }
    } catch (e) {
      // ignore resume errors
    }
  }, [openStream, setSessionId, setStage]);
  return { sessionId, stage, progress, insertedCount, error, startIntake, resumeIntake, cleanup };
}


