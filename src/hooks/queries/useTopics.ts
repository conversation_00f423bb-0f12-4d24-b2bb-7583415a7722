"use client";

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

export type TopicEntry = {
  id: number;
  idea?: string;
  action?: string;
  result?: string;
  status?: "idea" | "action" | "unproven" | "confirmed";
  position?: number;
  metadata?: Record<string, unknown>;
};

export type ProjectTopics = Record<string, Array<{ id: number; title: string }>>;

export type ProjectTopicSummary = {
  sectionId: string;
  topicId: string;
  numericTopicId: number;
  title: string;
  layer?: string;
  dependencies?: string[];
  subject?: string;
  subjectName?: string;
  titlePrompt?: string;
  description?: string;
  mappedLabel?: string;
  mappedCategory?: string;
  mappedCategoryId?: string;
  dependencyLabels?: string[];
};

export function useProjectTopics(projectId: string | undefined, sectionId?: string) {
  return useQuery<ProjectTopics, Error>({
    queryKey: ["topics", projectId, sectionId ?? null],
    queryFn: async () => {
      const qs = sectionId ? `?section_id=${encodeURIComponent(sectionId)}` : "";
      const res = await fetch(`/api/projects/${projectId}/topics${qs}`);
      if (!res.ok) throw new Error("Failed to fetch topics");
      const json = await res.json();
      return json?.data ?? {};
    },
    enabled: !!projectId,
    staleTime: 60_000,
  });
}

export function useTopicEntries(projectId: string | undefined, topicId: number | string | undefined) {
  // Validate that topicId is a valid number (not undefined, null, empty string, etc.)
  const isValidTopicId = topicId !== undefined && topicId !== null && 
                          (typeof topicId === 'number' || (typeof topicId === 'string' && topicId.trim() !== '' && !isNaN(Number(topicId))));
  
  return useQuery<TopicEntry[], Error>({
    queryKey: ["topic-entries", projectId, topicId],
    queryFn: async () => {
      const res = await fetch(`/api/projects/${projectId}/topics/${topicId}/entries`);
      if (!res.ok) throw new Error("Failed to fetch topic entries");
      const json = await res.json();
      const payload = json?.data ?? json;
      const entries = Array.isArray(payload)
        ? payload
        : Array.isArray((payload as any)?.entries)
        ? (payload as any).entries
        : Array.isArray((payload as any)?.data)
        ? (payload as any).data
        : [];
      return entries as TopicEntry[];
    },
    enabled: !!projectId && isValidTopicId,
    staleTime: 60_000,
  });
}

export function useAllProjectTopics(
  projectId: string | undefined,
) {
  // Static mapping from backend layer keys to frontend labels and categories
  // Keep in sync with product taxonomy
  const TOPIC_MAPPING: Record<string, { frontendLabel: string; category: string }> = {
    problem: { frontendLabel: "Problem", category: "Market" },
    "target-market": { frontendLabel: "Audience", category: "Market" },
    "market-size": { frontendLabel: "Market Size", category: "Market" },
    competition: { frontendLabel: "Alternatives", category: "Market" },
    "solution-design": { frontendLabel: "Product or Service", category: "Solution" },
    "value-proposition": { frontendLabel: "Unique Value Proposition", category: "Market" },
    "mvp-features": { frontendLabel: "Packages", category: "Solution" },
    "technical-architecture": { frontendLabel: "Tech", category: "Solution" },
    "gtm-strategy": { frontendLabel: "Positioning", category: "Sales & Marketing" },
    "pricing-strategy": { frontendLabel: "Revenue", category: "Company" },
    "marketing-channels": { frontendLabel: "Channels", category: "Sales & Marketing" },
    "sales-process": { frontendLabel: "Sales Motion", category: "Sales & Marketing" },
    "business-model": { frontendLabel: "Business Model", category: "Company" },
    "team-roles": { frontendLabel: "Team", category: "Company" },
    "financial-projections": { frontendLabel: "Cost", category: "Company" },
    "funding-strategy": { frontendLabel: "Risks", category: "Company" },
  };

  const normalizeId = (value: string) =>
    value
      .trim()
      .toLowerCase()
      .replace(/\s+/g, "-")
      .replace(/&/g, "")
      .replace(/--+/g, "-");

  return useQuery<ProjectTopicSummary[], Error>({
    queryKey: ["all-project-topics-v2", projectId],
    queryFn: async () => {
      // Debug start
      // eslint-disable-next-line no-console
      console.log('[useAllProjectTopics] fetching for project', projectId);
      const res = await fetch(`/api/projects/${projectId}/topics`);
      if (!res.ok) {
        // eslint-disable-next-line no-console
        console.warn('[useAllProjectTopics] non-OK response', res.status, res.statusText);
        return [];
      }
      const json = await res.json();
      // eslint-disable-next-line no-console
      console.log('[useAllProjectTopics] raw json keys', Object.keys(json || {}));
      const root = (json && (json.data ?? json)) || {};

      // Try common containers in order
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      let grouped: any = root;
      if (root && typeof root === 'object') {
        if (root.subjects && typeof root.subjects === 'object') grouped = root.subjects;
        else if (root.topics && typeof root.topics === 'object') grouped = root.topics;
        else if (root.topicsBySubject && typeof root.topicsBySubject === 'object') grouped = root.topicsBySubject;
        else if (root.data && typeof root.data === 'object') grouped = root.data;
      }

      // If still not an object-with-arrays, but an array of topics, handle it
      if (Array.isArray(grouped)) {
        console.log('🔥 [useAllProjectTopics] Processing array of topics:', grouped.slice(0, 3));
        const topicsArr = grouped;
        const result: ProjectTopicSummary[] = topicsArr
          .map((topic: any) => {
            const numericId = Number(topic?.id);
            if (!Number.isFinite(numericId)) return null;
            const rawSubject = String(topic?.subject || topic?.subject_id || 'unknown');
            const sectionId = rawSubject;
            const layer: string | undefined = topic?.layer ?? topic?.key ?? undefined;
            const dependencies: string[] | undefined = Array.isArray(topic?.dependencies)
              ? topic.dependencies
              : Array.isArray(topic?.prerequisites)
              ? topic.prerequisites
              : undefined;
            const subjectName: string | undefined = topic?.subject?.name ?? topic?.subject_name ?? undefined;
            const titlePrompt: string | undefined = topic?.titlePrompt ?? topic?.title_prompt ?? undefined;
            const description: string | undefined = topic?.description ?? undefined;

            let mappedLabel: string | undefined;
            let mappedCategory: string | undefined;
            let mappedCategoryId: string | undefined;
            let dependencyLabels: string[] | undefined;
            if (layer && TOPIC_MAPPING[layer]) {
              mappedLabel = TOPIC_MAPPING[layer].frontendLabel;
              mappedCategory = TOPIC_MAPPING[layer].category;
              mappedCategoryId = normalizeId(mappedCategory);
            }
            if (Array.isArray(dependencies) && dependencies.length) {
              dependencyLabels = dependencies
                .map((d) => TOPIC_MAPPING[d]?.frontendLabel || d)
                .filter(Boolean) as string[];
            }

            console.log(`🔥 [useAllProjectTopics] Topic ${numericId}: subject="${topic?.subject}" subject_id="${topic?.subject_id}" -> sectionId="${sectionId}" layer="${layer}" mappedCategoryId="${mappedCategoryId}"`);
            return {
              sectionId,
              topicId: String(numericId),
              numericTopicId: numericId,
              title: String(topic?.title ?? topic?.name ?? ''),
              layer,
              dependencies,
              subject: rawSubject,
              subjectName,
              titlePrompt,
              description,
              mappedLabel,
              mappedCategory,
              mappedCategoryId,
              dependencyLabels,
            };
          })
          .filter(Boolean) as ProjectTopicSummary[];
        console.log('🔥 [useAllProjectTopics] Array result:', result.slice(0, 3));
        return result;
      }

      const normalizeSectionId = (key: string) =>
        key.trim().toLowerCase().replace(/\s+/g, "-").replace(/&/g, "").replace(/--+/g, "-");

      const topics: ProjectTopicSummary[] = [];

      console.log('🔥 [useAllProjectTopics] Processing object structure:', Object.keys(grouped));
      Object.entries(grouped as Record<string, unknown>).forEach(([key, topicList]) => {
        const sectionId = normalizeSectionId(key);
        console.log(`🔥 [useAllProjectTopics] Object key "${key}" -> sectionId "${sectionId}"`);
        if (Array.isArray(topicList)) {
          console.log(`🔥 [useAllProjectTopics] Key "${key}" has ${topicList.length} topics as array`);
          topicList.forEach((topic: any) => {
            const numericId = Number(topic?.id);
            if (!Number.isFinite(numericId)) return;
            const layer: string | undefined = topic?.layer ?? topic?.key ?? undefined;
            const dependencies: string[] | undefined = Array.isArray(topic?.dependencies)
              ? topic.dependencies
              : Array.isArray(topic?.prerequisites)
              ? topic.prerequisites
              : undefined;
            const subjectName: string | undefined = topic?.subject?.name ?? topic?.subject_name ?? undefined;
            const titlePrompt: string | undefined = topic?.titlePrompt ?? topic?.title_prompt ?? undefined;
            const description: string | undefined = topic?.description ?? undefined;

            let mappedLabel: string | undefined;
            let mappedCategory: string | undefined;
            let mappedCategoryId: string | undefined;
            let dependencyLabels: string[] | undefined;
            if (layer && TOPIC_MAPPING[layer]) {
              mappedLabel = TOPIC_MAPPING[layer].frontendLabel;
              mappedCategory = TOPIC_MAPPING[layer].category;
              mappedCategoryId = normalizeId(mappedCategory);
            }
            if (Array.isArray(dependencies) && dependencies.length) {
              dependencyLabels = dependencies
                .map((d) => TOPIC_MAPPING[d]?.frontendLabel || d)
                .filter(Boolean) as string[];
            }

            topics.push({
              sectionId,
              topicId: String(numericId),
              numericTopicId: numericId,
              title: String(topic?.title ?? topic?.name ?? ""),
              layer,
              dependencies,
              subject: key,
              subjectName,
              titlePrompt,
              description,
              mappedLabel,
              mappedCategory,
              mappedCategoryId,
              dependencyLabels,
            });
          });
        } else if (topicList && typeof topicList === 'object') {
          console.log(`🔥 [useAllProjectTopics] Key "${key}" has nested object structure`);
          // Some backends nest one more level per subject key
          Object.values(topicList as Record<string, unknown>).forEach((maybeList) => {
            if (Array.isArray(maybeList)) {
              (maybeList as any[]).forEach((topic: any) => {
                const numericId = Number(topic?.id);
                if (!Number.isFinite(numericId)) return;
                const layer: string | undefined = topic?.layer ?? topic?.key ?? undefined;
                const dependencies: string[] | undefined = Array.isArray(topic?.dependencies)
                  ? topic.dependencies
                  : Array.isArray(topic?.prerequisites)
                  ? topic.prerequisites
                  : undefined;
                const subjectName: string | undefined = topic?.subject?.name ?? topic?.subject_name ?? undefined;
                const titlePrompt: string | undefined = topic?.titlePrompt ?? topic?.title_prompt ?? undefined;
                const description: string | undefined = topic?.description ?? undefined;

                let mappedLabel: string | undefined;
                let mappedCategory: string | undefined;
                let mappedCategoryId: string | undefined;
                let dependencyLabels: string[] | undefined;
                if (layer && TOPIC_MAPPING[layer]) {
                  mappedLabel = TOPIC_MAPPING[layer].frontendLabel;
                  mappedCategory = TOPIC_MAPPING[layer].category;
                  mappedCategoryId = normalizeId(mappedCategory);
                }
                if (Array.isArray(dependencies) && dependencies.length) {
                  dependencyLabels = dependencies
                    .map((d) => TOPIC_MAPPING[d]?.frontendLabel || d)
                    .filter(Boolean) as string[];
                }

                topics.push({
                  sectionId,
                  topicId: String(numericId),
                  numericTopicId: numericId,
                  title: String(topic?.title ?? topic?.name ?? ''),
                  layer,
                  dependencies,
                  subject: key,
                  subjectName,
                  titlePrompt,
                  description,
                  mappedLabel,
                  mappedCategory,
                  mappedCategoryId,
                  dependencyLabels,
                });
              });
            }
          });
        }
      });
      // eslint-disable-next-line no-console
      console.log('[useAllProjectTopics] topics parsed', topics.length);
      return topics;
    },
    enabled: !!projectId,
    staleTime: 0,
    refetchOnMount: 'always',
    refetchOnReconnect: true,
    refetchOnWindowFocus: false,
  });
}

export function useAllTopicEntries(projectId: string | undefined, topics: Array<{sectionId: string, topicId: string, numericTopicId: number}>) {
  console.log('[useAllTopicEntries] Hook called with:', {
    projectId,
    topicsCount: topics.length,
    enabled: !!projectId && topics.length > 0,
    topics: topics.slice(0, 3) // Show first 3 topics
  });

  return useQuery<Record<string, TopicEntry[]>, Error>({
    queryKey: ["all-topic-entries", projectId, topics.map(t => t.numericTopicId).sort()],
    queryFn: async () => {
      console.log('[useAllTopicEntries] Query function EXECUTING with topics:', topics.length);
      if (!topics.length) return {};
      
      // Fetch all topic entries in parallel
      const promises = topics.map(async (topic) => {
        console.log(`[useAllTopicEntries] Making request for topic ${topic.topicId} (${topic.numericTopicId})`);
        try {
          const url = `/api/projects/${projectId}/topics/${topic.numericTopicId}/entries`;
          console.log(`[useAllTopicEntries] URL: ${url}`);
          const res = await fetch(url);
          if (!res.ok) return { topicId: topic.topicId, entries: [] };
          const json = await res.json();
          const payload = json?.data ?? json;
          const entries = Array.isArray(payload)
            ? payload
            : Array.isArray((payload as any)?.entries)
            ? (payload as any).entries
            : Array.isArray((payload as any)?.data)
            ? (payload as any).data
            : [];
          return { topicId: topic.topicId, entries };
        } catch (error) {
          console.error(`Failed to fetch entries for topic ${topic.topicId}:`, error);
          return { topicId: topic.topicId, entries: [] };
        }
      });
      
      const results = await Promise.all(promises);
      
      // Convert to a record keyed by topicId
      const entriesByTopic: Record<string, TopicEntry[]> = {};
      results.forEach(({ topicId, entries }) => {
        entriesByTopic[topicId] = entries;
      });
      
      return entriesByTopic;
    },
    enabled: !!projectId && topics.length > 0,
    staleTime: 60_000,
  });
}

export function useTopicEntryMutations(projectId: string) {
  const queryClient = useQueryClient();

  const createEntry = useMutation({
    mutationFn: async ({ topicId, payload }: { topicId: number | string; payload: Partial<TopicEntry> }) => {
      // eslint-disable-next-line no-console
      console.log('[useTopicEntryMutations] createEntry →', { projectId, topicId, payload });
      const res = await fetch(`/api/projects/${projectId}/topics/${topicId}/entries`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload),
      });
      if (!res.ok) {
        // eslint-disable-next-line no-console
        console.error('[useTopicEntryMutations] createEntry FAILED', res.status, res.statusText);
        throw new Error('Failed to create topic entry');
      }
      const json = await res.json().catch(() => ({}));
      // eslint-disable-next-line no-console
      console.log('[useTopicEntryMutations] createEntry OK', { status: res.status, json });
      return (json?.data ?? json) as unknown;
    },
    onSuccess: (_data, variables) => {
      // eslint-disable-next-line no-console
      console.log('[useTopicEntryMutations] createEntry onSuccess → invalidating queries', { variables });
      queryClient.invalidateQueries({ predicate: (q) => Array.isArray(q.queryKey) && String(q.queryKey[0]).includes('topic') });
      queryClient.invalidateQueries({ queryKey: ["all-topic-entries", projectId] });
      queryClient.invalidateQueries({ queryKey: ["topic-entries", projectId, variables.topicId] });
    },
    onError: (error, variables) => {
      // eslint-disable-next-line no-console
      console.error('[useTopicEntryMutations] createEntry onError', { error, variables });
    }
  });

  const updateEntry = useMutation({
    mutationFn: async ({ topicId, entryId, payload }: { topicId: number | string; entryId: number | string; payload: Partial<TopicEntry> }) => {
      // eslint-disable-next-line no-console
      console.log('[useTopicEntryMutations] updateEntry →', { projectId, topicId, entryId, payload });
      const url = new URL(window.location.origin + `/api/projects/${projectId}/topics/${topicId}/entries`);
      url.searchParams.set('entryId', String(entryId));
      const res = await fetch(url.toString(), {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload),
      });
      if (!res.ok) {
        // eslint-disable-next-line no-console
        console.error('[useTopicEntryMutations] updateEntry FAILED', res.status, res.statusText);
        throw new Error('Failed to update topic entry');
      }
      const json = await res.json().catch(() => ({}));
      // eslint-disable-next-line no-console
      console.log('[useTopicEntryMutations] updateEntry OK', { status: res.status, json });
      return (json?.data ?? json) as unknown;
    },
    onSuccess: (_data, variables) => {
      // eslint-disable-next-line no-console
      console.log('[useTopicEntryMutations] updateEntry onSuccess → invalidating queries', { variables });
      queryClient.invalidateQueries({ predicate: (q) => Array.isArray(q.queryKey) && String(q.queryKey[0]).includes('topic') });
      queryClient.invalidateQueries({ queryKey: ["all-topic-entries", projectId] });
      queryClient.invalidateQueries({ queryKey: ["topic-entries", projectId, variables.topicId] });
    },
    onError: (error, variables) => {
      // eslint-disable-next-line no-console
      console.error('[useTopicEntryMutations] updateEntry onError', { error, variables });
    }
  });

  const deleteEntry = useMutation({
    mutationFn: async ({ topicId, entryId }: { topicId: number | string; entryId: number | string }) => {
      // eslint-disable-next-line no-console
      console.log('[useTopicEntryMutations] deleteEntry →', { projectId, topicId, entryId });
      const url = new URL(window.location.origin + `/api/projects/${projectId}/topics/${topicId}/entries`);
      url.searchParams.set('entryId', String(entryId));
      const res = await fetch(url.toString(), { method: 'DELETE' });
      if (!res.ok) {
        // eslint-disable-next-line no-console
        console.error('[useTopicEntryMutations] deleteEntry FAILED', res.status, res.statusText);
        throw new Error('Failed to delete topic entry');
      }
      // eslint-disable-next-line no-console
      console.log('[useTopicEntryMutations] deleteEntry OK', { status: res.status });
      return true;
    },
    onSuccess: (_data, variables) => {
      // eslint-disable-next-line no-console
      console.log('[useTopicEntryMutations] deleteEntry onSuccess → invalidating queries', { variables });
      queryClient.invalidateQueries({ predicate: (q) => Array.isArray(q.queryKey) && String(q.queryKey[0]).includes('topic') });
      queryClient.invalidateQueries({ queryKey: ["all-topic-entries", projectId] });
      queryClient.invalidateQueries({ queryKey: ["topic-entries", projectId, variables.topicId] });
    },
    onError: (error, variables) => {
      // eslint-disable-next-line no-console
      console.error('[useTopicEntryMutations] deleteEntry onError', { error, variables });
    }
  });

  return { createEntry, updateEntry, deleteEntry };
}

