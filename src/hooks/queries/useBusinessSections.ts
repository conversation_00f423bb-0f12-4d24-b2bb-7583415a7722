"use client";

import { useQuery } from "@tanstack/react-query";
import type { BusinessSection } from "@/types/BusinessSection.types";

export function useBusinessSections(projectId: string | undefined) {
  return useQuery<BusinessSection[], Error>({
    queryKey: ["business-sections", projectId],
    queryFn: async () => {
      if (!projectId) return [] as BusinessSection[];
      console.log("[useBusinessSections] fetching", { projectId, path: `/api/projects/${projectId}/business-sections` });
      const res = await fetch(`/api/projects/${projectId}/business-sections`);
      console.log("[useBusinessSections] response", { status: res.status });
      if (!res.ok) throw new Error("Failed to fetch business sections");
      const json = await res.json();
      console.log("[useBusinessSections] raw json", json);
      // Our Next API returns { success: true, data: <backendPayload> }
      // Normalize a few common shapes
      const payload = json?.data ?? json;
      if (Array.isArray(payload)) return payload as BusinessSection[];
      if (Array.isArray(payload?.data)) return payload.data as BusinessSection[];
      if (Array.isArray(payload?.sections)) return payload.sections as BusinessSection[];
      if (Array.isArray(payload?.data?.sections)) return payload.data.sections as BusinessSection[];
      if (Array.isArray(payload?.result)) return payload.result as BusinessSection[];
      return [];
    },
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    select: (data: any) => {
      console.log("[useBusinessSections] success", { count: Array.isArray(data) ? data.length : 0, ids: Array.isArray(data) ? data.map((s: BusinessSection) => s.id) : [] });
      return data as BusinessSection[];
    },
    enabled: !!projectId,
    staleTime: 60_000,
  });
}
