"use client";

import { useC<PERSON>kAuth } from "@/hooks/useClerkAuth";
import { queryKeys } from "@/lib/queryClient";
import { useQuery } from "@tanstack/react-query";

export interface User {
  id: string;
  clerkId: string;
  email: string;
  firstName: string;
  lastName: string;
  role: "user" | "admin";
  status: "active" | "inactive";
  avatarUrl?: string;
  bio?: string;
  timezone: string;
  preferences: {
    notifications: boolean;
    theme: "light" | "dark" | "system";
    language: string;
  };
  createdAt: string;
  updatedAt: string;
}

/**
 * Hook to fetch user data by Clerk ID
 * Automatically caches and manages loading/error states
 */
export function useUser() {
  const { userId, getToken, isSignedIn } = useClerkAuth();

  return useQuery({
    queryKey: queryKeys.user(userId),
    queryFn: async (): Promise<User> => {
      if (!userId) {
        throw new Error("User ID not available");
      }

      const token = await getToken();
      const backendUrl =
        process.env.NEXT_PUBLIC_ADMIN_API_URL || "http://localhost:3002";
      const url = `${backendUrl}/api/users/clerk/${userId}`;

      console.log("Fetching user from backend:", {
        url,
        userId,
        hasToken: !!token,
      });

      // Add timeout to prevent hanging requests
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

      const response = await fetch(url, {
        headers: {
          "Content-Type": "application/json",
          ...(token && { Authorization: `Bearer ${token}` }),
        },
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      console.log("Backend response:", {
        status: response.status,
        statusText: response.statusText,
        ok: response.ok,
      });

      if (!response.ok) {
        if (response.status === 404) {
          throw new Error("User not found in backend");
        }
        throw new Error(
          `Failed to fetch user: ${response.status} ${response.statusText}`
        );
      }

      return response.json();
    },
    enabled: !!userId && isSignedIn, // Only run when user is signed in and has ID
    staleTime: 5 * 60 * 1000, // Consider fresh for 5 minutes
    gcTime: 10 * 60 * 1000, // Keep in cache for 10 minutes
    retry: (failureCount, error: any) => {
      // Don't retry if user not found or network errors
      if (
        error?.message?.includes("User not found") ||
        error?.message?.includes("Failed to fetch") ||
        error?.name === "AbortError"
      ) {
        return false;
      }
      return failureCount < 2;
    },
    // Don't throw errors to prevent crashes
    throwOnError: false,
  });
}

/**
 * Hook to get user profile data with additional metadata
 */
export function useUserProfile() {
  const { userId, getToken, isSignedIn } = useClerkAuth();

  return useQuery({
    queryKey: queryKeys.userProfile(userId),
    queryFn: async () => {
      if (!userId) {
        throw new Error("User ID not available");
      }

      const token = await getToken();
      const backendUrl =
        process.env.NEXT_PUBLIC_ADMIN_API_URL || "http://localhost:3002";

      // Add timeout to prevent hanging requests
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

      const response = await fetch(
        `${backendUrl}/api/users/clerk/${userId}/profile`,
        {
          headers: {
            "Content-Type": "application/json",
            ...(token && { Authorization: `Bearer ${token}` }),
          },
          signal: controller.signal,
        }
      );

      clearTimeout(timeoutId);

      if (!response.ok) {
        // If profile endpoint doesn't exist, fall back to regular user data
        if (response.status === 404) {
          const userResponse = await fetch(
            `${backendUrl}/api/users/clerk/${userId}`,
            {
              headers: {
                "Content-Type": "application/json",
                ...(token && { Authorization: `Bearer ${token}` }),
              },
            }
          );

          if (userResponse.ok) {
            return userResponse.json();
          }
        }
        throw new Error(`Failed to fetch user profile: ${response.status}`);
      }

      return response.json();
    },
    enabled: !!userId && isSignedIn,
    staleTime: 2 * 60 * 1000, // Profile data might change more frequently
    retry: (failureCount, error: any) => {
      // Don't retry network errors or timeouts
      if (
        error?.message?.includes("Failed to fetch") ||
        error?.name === "AbortError"
      ) {
        return false;
      }
      return failureCount < 2;
    },
    // Don't throw errors to prevent crashes
    throwOnError: false,
  });
}

/**
 * Hook to check if user exists in backend
 * Useful for determining if user needs to be synced
 */
export function useUserExists() {
  const { userId, getToken, isSignedIn } = useClerkAuth();

  return useQuery({
    queryKey: ["user", "exists", userId],
    queryFn: async (): Promise<boolean> => {
      if (!userId) return false;

      try {
        const token = await getToken();
        const backendUrl =
          process.env.NEXT_PUBLIC_ADMIN_API_URL || "http://localhost:3002";

        // Check if we're in development and backend might not be running
        if (backendUrl.includes("localhost") && typeof window !== "undefined") {
          console.log("Attempting to connect to local backend:", backendUrl);
        }

        // Add timeout to prevent hanging requests
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

        const response = await fetch(
          `${backendUrl}/api/users/clerk/${userId}`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
              ...(token && { Authorization: `Bearer ${token}` }),
            },
            signal: controller.signal,
          }
        );

        clearTimeout(timeoutId);

        console.log("User exists check:", {
          userId,
          status: response.status,
          exists: response.ok,
        });

        return response.ok;
      } catch (error) {
        console.error("Error checking if user exists:", error);
        // Return false for any network errors, timeouts, or fetch failures
        return false;
      }
    },
    enabled: !!userId && isSignedIn,
    staleTime: 30 * 1000, // Check existence frequently
    retry: false, // Don't retry existence checks
    // Add error boundary to prevent crashes
    throwOnError: false,
  });
}
