"use client";

import { useQuery, useQueryClient } from "@tanstack/react-query";

export function useSiiftTable(projectId: string | undefined) {
  return useQuery<any, Error>({
    queryKey: ["siift-table", projectId],
    queryFn: async () => {
      const res = await fetch(`/api/projects/${projectId}/siift-table`);
      if (!res.ok) throw new Error("Failed to fetch SIIFT table");
      return res.json();
    },
    enabled: !!projectId,
    staleTime: 30_000,
  });
}

export function useSiiftTableDetailed(projectId: string | undefined) {
  return useQuery<any, Error>({
    queryKey: ["siift-table-detailed", projectId],
    queryFn: async () => {
      const res = await fetch(`/api/projects/${projectId}/siift-table/detailed`);
      if (!res.ok) throw new Error("Failed to fetch SIIFT table detailed");
      return res.json();
    },
    enabled: !!projectId,
    staleTime: 30_000,
  });
}

export function useInvalidateSiift() {
  const qc = useQueryClient();
  return (projectId: string) => {
    qc.invalidateQueries({ queryKey: ["siift-table", projectId] });
    qc.invalidateQueries({ queryKey: ["siift-table-detailed", projectId] });
    // Also refresh sections/topics that the project page renders
    qc.invalidateQueries({ queryKey: ["business-sections", projectId] });
    qc.invalidateQueries({ queryKey: ["topics", projectId] });
  };
}



