"use client";

import React, { useCallback } from "react";
import { usePostHog } from 'posthog-js/react';

export function useAnalytics() {
  const posthog = usePostHog();

  // Track page views
  const trackPageView = useCallback((url: string, title?: string) => {
    posthog?.capture('$pageview', {
      $current_url: url,
      page_title: title,
    });
  }, [posthog]);

  // Track user interactions
  const trackClick = useCallback((elementName: string, location?: string) => {
    posthog?.capture('button_clicked', {
      element_name: elementName,
      location: location,
    });
  }, [posthog]);

  const trackFormSubmit = useCallback((formName: string, success: boolean = true) => {
    posthog?.capture('form_submitted', {
      form_name: formName,
      success: success,
    });
  }, [posthog]);

  const trackSearch = useCallback((searchTerm: string, resultsCount?: number) => {
    posthog?.capture('search_performed', {
      search_term: searchTerm,
      results_count: resultsCount,
    });
  }, [posthog]);

  // Track business events
  const trackSignUp = useCallback((method: "email" | "google") => {
    posthog?.capture('user_signed_up', {
      method: method,
    });
    // Identify the user for PostHog
    posthog?.identify();
  }, [posthog]);

  const trackLogin = useCallback((method: "email" | "google") => {
    posthog?.capture('user_logged_in', {
      method: method,
    });
  }, [posthog]);

  const trackProjectCreated = useCallback((projectType?: string) => {
    posthog?.capture('project_created', {
      project_type: projectType,
    });
  }, [posthog]);

  const trackFeatureUsed = useCallback((featureName: string, context?: string) => {
    posthog?.capture('feature_used', {
      feature_name: featureName,
      context: context,
    });
  }, [posthog]);

  // Track errors
  const trackError = useCallback((error: string, context?: string, fatal: boolean = false) => {
    posthog?.capture('error_occurred', {
      error_message: error,
      context: context,
      fatal: fatal,
    });
  }, [posthog]);

  // Track performance
  const trackTiming = useCallback((name: string, value: number, category?: string) => {
    posthog?.capture('timing_measured', {
      timing_name: name,
      timing_value: value,
      category: category || "performance",
    });
  }, [posthog]);

  // Track custom events
  const trackCustomEvent = useCallback((eventName: string, parameters?: Record<string, any>) => {
    posthog?.capture(eventName, parameters);
  }, [posthog]);

  // Identify user (call this when user logs in)
  const identifyUser = useCallback((userId: string, properties?: Record<string, any>) => {
    posthog?.identify(userId, properties);
  }, [posthog]);

  // Set user properties
  const setUserProperties = useCallback((properties: Record<string, any>) => {
    posthog?.setPersonProperties(properties);
  }, [posthog]);

  return {
    trackPageView,
    trackClick,
    trackFormSubmit,
    trackSearch,
    trackSignUp,
    trackLogin,
    trackProjectCreated,
    trackFeatureUsed,
    trackError,
    trackTiming,
    trackCustomEvent,
    identifyUser,
    setUserProperties,
  };
}

// Higher-order component for automatic click tracking
export function withAnalytics(
  Component: React.ComponentType<any>,
  eventName: string
) {
  return function AnalyticsWrapper(props: any) {
    const { trackClick } = useAnalytics();

    const handleClick = (e: React.MouseEvent) => {
      trackClick(eventName);
      if (props.onClick) {
        props.onClick(e);
      }
    };

    return React.createElement(Component, { ...props, onClick: handleClick });
  };
}
