import { toast } from "sonner";

// Temporary flag to disable error toasts for testing
const DISABLE_ERROR_TOASTS = false;

export interface ToastOptions {
  title?: string;
  description?: string;
  duration?: number;
}

export const useToast = () => {
  const showToast = {
    success: (message: string, options?: ToastOptions) => {
      toast.success(message, {
        description: options?.description,
        duration: options?.duration || 4000,
      });
    },

    error: (message: string, options?: ToastOptions) => {
      if (DISABLE_ERROR_TOASTS) {
        console.warn('Error toast disabled:', message, options?.description);
        return;
      }
      toast.error(message, {
        description: options?.description,
        duration: options?.duration || 5000,
      });
    },

    info: (message: string, options?: ToastOptions) => {
      toast.info(message, {
        description: options?.description,
        duration: options?.duration || 4000,
      });
    },

    warning: (message: string, options?: ToastOptions) => {
      toast.warning(message, {
        description: options?.description,
        duration: options?.duration || 4000,
      });
    },

    loading: (message: string, options?: ToastOptions) => {
      return toast.loading(message, {
        description: options?.description,
      });
    },

    promise: <T>(
      promise: Promise<T>,
      {
        loading,
        success,
        error,
      }: {
        loading: string;
        success: string | ((data: T) => string);
        error: string | ((error: any) => string);
      }
    ) => {
      return toast.promise(promise, {
        loading,
        success,
        error,
      });
    },

    dismiss: (toastId?: string | number) => {
      toast.dismiss(toastId);
    },
  };

  return showToast;
};

// Export individual toast functions for convenience
export const showSuccessToast = (message: string, options?: ToastOptions) => {
  toast.success(message, {
    description: options?.description,
    duration: options?.duration || 4000,
  });
};

export const showErrorToast = (message: string, options?: ToastOptions) => {
  if (DISABLE_ERROR_TOASTS) {
    console.warn('Error toast disabled:', message, options?.description);
    return;
  }
  toast.error(message, {
    description: options?.description,
    duration: options?.duration || 5000,
  });
};

export const showInfoToast = (message: string, options?: ToastOptions) => {
  toast.info(message, {
    description: options?.description,
    duration: options?.duration || 4000,
  });
};

export const showWarningToast = (message: string, options?: ToastOptions) => {
  toast.warning(message, {
    description: options?.description,
    duration: options?.duration || 4000,
  });
};
