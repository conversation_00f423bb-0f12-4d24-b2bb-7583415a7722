import { useCallback, useEffect, useRef, useState } from 'react';

interface UseResizableProps {
  initialWidth: string;
  minWidthPercent: number;
  maxWidthPercent: number;
  onWidthChange: (width: string) => void;
  onCollapse?: () => void;
}

export function useResizable({
  initialWidth,
  minWidthPercent,
  maxWidthPercent,
  onWidthChange,
  onCollapse,
}: UseResizableProps) {
  const [isDragging, setIsDragging] = useState(false);
  const [currentWidth, setCurrentWidth] = useState(initialWidth);
  const startXRef = useRef(0);
  const startWidthRef = useRef(0);

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    setIsDragging(true);
    startXRef.current = e.clientX;
    
    // Parse current width percentage
    const widthPercent = parseFloat(currentWidth.replace('%', ''));
    startWidthRef.current = (widthPercent / 100) * window.innerWidth;
  }, [currentWidth]);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isDragging) return;

    const deltaX = e.clientX - startXRef.current;
    const newWidthPx = startWidthRef.current + deltaX;
    const newWidthPercent = (newWidthPx / window.innerWidth) * 100;

    // Constrain within bounds
    const constrainedPercent = Math.max(
      minWidthPercent,
      Math.min(maxWidthPercent, newWidthPercent)
    );

    // Auto-collapse if below minimum threshold
    if (constrainedPercent < 10 && onCollapse) {
      onCollapse();
      return;
    }

    const newWidth = `${constrainedPercent}%`;
    setCurrentWidth(newWidth);
    onWidthChange(newWidth);
  }, [isDragging, minWidthPercent, maxWidthPercent, onWidthChange, onCollapse]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = 'col-resize';
      document.body.style.userSelect = 'none';

      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
        document.body.style.cursor = '';
        document.body.style.userSelect = '';
      };
    }
  }, [isDragging, handleMouseMove, handleMouseUp]);

  // Update current width when initialWidth changes
  useEffect(() => {
    setCurrentWidth(initialWidth);
  }, [initialWidth]);

  return {
    isDragging,
    currentWidth,
    handleMouseDown,
  };
}
