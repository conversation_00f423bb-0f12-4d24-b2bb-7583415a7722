"use client";

import { useAuth } from "@clerk/nextjs";
import { useCallback } from "react";

/**
 * Custom hook for making authenticated API calls using Clerk tokens
 * This is the proper way to get access tokens when using Clerk
 */
export function useClerkApi() {
  const { getToken, isSignedIn } = useAuth();

  const makeAuthenticatedRequest = useCallback(
    async (endpoint: string, options: RequestInit = {}) => {
      if (!isSignedIn) {
        throw new Error("User is not signed in");
      }

      // Get the Clerk JWT token
      const token = await getToken();
      
      if (!token) {
        throw new Error("Failed to get authentication token");
      }

      const baseUrl = process.env.NEXT_PUBLIC_ADMIN_API_URL || "http://localhost:3002";
      
      const defaultHeaders = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      };

      const response = await fetch(`${baseUrl}${endpoint}`, {
        ...options,
        headers: {
          ...defaultHeaders,
          ...options.headers,
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`API request failed: ${response.status} ${response.statusText} - ${errorText}`);
      }

      return response;
    },
    [getToken, isSignedIn]
  );

  const get = useCallback(
    async (endpoint: string) => {
      const response = await makeAuthenticatedRequest(endpoint, {
        method: "GET",
      });
      return response.json();
    },
    [makeAuthenticatedRequest]
  );

  const post = useCallback(
    async (endpoint: string, data?: any) => {
      const response = await makeAuthenticatedRequest(endpoint, {
        method: "POST",
        body: data ? JSON.stringify(data) : undefined,
      });
      return response.json();
    },
    [makeAuthenticatedRequest]
  );

  const put = useCallback(
    async (endpoint: string, data?: any) => {
      const response = await makeAuthenticatedRequest(endpoint, {
        method: "PUT",
        body: data ? JSON.stringify(data) : undefined,
      });
      return response.json();
    },
    [makeAuthenticatedRequest]
  );

  const del = useCallback(
    async (endpoint: string) => {
      const response = await makeAuthenticatedRequest(endpoint, {
        method: "DELETE",
      });
      return response.json();
    },
    [makeAuthenticatedRequest]
  );

  const patch = useCallback(
    async (endpoint: string, data?: any) => {
      const response = await makeAuthenticatedRequest(endpoint, {
        method: "PATCH",
        body: data ? JSON.stringify(data) : undefined,
      });
      return response.json();
    },
    [makeAuthenticatedRequest]
  );

  return {
    makeAuthenticatedRequest,
    get,
    post,
    put,
    delete: del,
    patch,
    isSignedIn,
    getToken, // Expose getToken for direct access if needed
  };
}

/**
 * Hook for getting the current Clerk token
 * Useful when you need just the token
 */
export function useClerkToken() {
  const { getToken, isSignedIn } = useAuth();

  const getCurrentToken = useCallback(async () => {
    if (!isSignedIn) {
      return null;
    }
    return await getToken();
  }, [getToken, isSignedIn]);

  return {
    getCurrentToken,
    isSignedIn,
  };
}
