"use client";

import { useAuth, useUser, useSignIn, useSignUp } from "@clerk/nextjs";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

export function useClerkAuth() {
  const { isSignedIn, signOut, getToken } = useAuth();
  const { user, isLoaded } = useUser();
  const { signIn, isLoaded: signInLoaded } = useSignIn();
  const { signUp, isLoaded: signUpLoaded } = useSignUp();
  const router = useRouter();

  const handleSignOut = async () => {
    try {
      await signOut();
      toast.success("Signed out successfully");
      router.push("/");
    } catch (error) {
      toast.error("Failed to sign out");
      console.error("Sign out error:", error);
    }
  };

  const getClerkToken = async () => {
    try {
      return await getToken();
    } catch (error) {
      console.error("Error getting Clerk token:", error);
      return null;
    }
  };

  return {
    // Auth state
    isSignedIn,
    isLoaded,
    user,
    
    // Auth actions
    signOut: handleSignOut,
    getToken: getClerkToken,
    
    // Sign in/up helpers
    signIn,
    signUp,
    signInLoaded,
    signUpLoaded,
    
    // User data helpers
    userId: user?.id,
    email: user?.emailAddresses[0]?.emailAddress,
    firstName: user?.firstName,
    lastName: user?.lastName,
    fullName: user?.fullName,
    imageUrl: user?.imageUrl,
    isEmailVerified: user?.emailAddresses[0]?.verification?.status === "verified",
  };
}
