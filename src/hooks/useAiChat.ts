"use client";

import { useCallback, useEffect, useRef, useState } from 'react';
import { useChatStore } from '@/stores/chatStore';
import type { ChatMessage } from '@/types/Chat.types';

export function useAiChat(projectId: string) {
  const {
    addMessage,
    updateLastMessage,
    appendToLastMessage,
    setProjectLoading,
    setProjectStreaming,
    getProjectLoading,
    getProjectStreaming,
    chatSession,
    setChatSession,
  } = useChatStore();
  
  const [sessionId, setSessionId] = useState<string | null>(null);
  const esRef = useRef<EventSource | null>(null);

  // Initialize session ID from chat session if available
  useEffect(() => {
    if (chatSession?.id && !sessionId) {
      setSessionId(chatSession.id);
    }
  }, [chatSession?.id, sessionId]);
  const abortControllerRef = useRef<AbortController | null>(null);

  // Subscribe reactively to per-project loading/streaming states
  const isLoading = useChatStore((s) => s.projectLoadingStates[projectId] || false);
  const isStreaming = useChatStore((s) => s.projectStreamingStates[projectId] || false);

  const ensureAiMessage = useCallback(() => {
    // Ensure an AI message container exists to stream into
    updateLastMessage("");
  }, [updateLastMessage]);

  const openStream = useCallback((sessId: string) => {
    if (!sessId) return;
    // If an EventSource is already open, replace it to avoid stale state
    if (esRef.current) {
      try {
        esRef.current.close();
      } catch (error) {
        console.error('Error closing EventSource:', error);
      }
      esRef.current = null;
    }

    // Open SSE to receive tokens before sending chat
    const es = new EventSource(`/api/ai-chat/sessions/${sessId}/events`, { withCredentials: true });
    esRef.current = es;
 
    es.addEventListener('token_stream', (ev) => {
      try {
        const data = JSON.parse((ev as MessageEvent).data as string);
        if (data.tokens && Array.isArray(data.tokens)) {
          ensureAiMessage();
          appendToLastMessage?.(data.tokens.join(''));
        }
      } catch {}
    });

    es.addEventListener('token', (ev) => {
      try {
        const data = JSON.parse((ev as MessageEvent).data as string);
        const chunk = String(data.content || data.token || '');
        ensureAiMessage();
        appendToLastMessage?.(chunk);
      } catch {}
    });

    es.addEventListener('context_update', () => {
      // Currently not surfaced in chat UI; could be used for live context hints
    });

    const handleComplete = () => {
      setProjectStreaming(projectId, false);
      try { es.close(); } catch {}
      if (esRef.current === es) esRef.current = null;
    };
    es.addEventListener('complete', handleComplete);
    es.addEventListener('stream_complete', handleComplete);

    es.addEventListener('error', () => {
      setProjectStreaming(projectId, false);
      try { es.close(); } catch {}
      if (esRef.current === es) esRef.current = null;
    });
  }, [appendToLastMessage, ensureAiMessage, projectId, setProjectStreaming]);

  const sendMessage = useCallback(async (message: string) => {
    const trimmed = message?.trim();
    if (!trimmed || !projectId) return;

    // Cancel any ongoing request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create new abort controller
    abortControllerRef.current = new AbortController();

    // Add user message immediately
    const userMessage: ChatMessage = {
      id: `user-${Date.now()}`,
      user: "You",
      avatar: "",
      message: trimmed,
      timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
      isCurrentUser: true,
    };
    addMessage(userMessage);
    setProjectLoading(projectId, true);

    try {
      // Ensure we have a real session id
      let effectiveSessionId: string | null = sessionId || null;
      const existingId = chatSession?.id;
      if (existingId && existingId.length > 0 && existingId !== `session-${projectId}`) {
        effectiveSessionId = existingId;
      }

      if (!effectiveSessionId) {
        // Try discover active session for this project
        try {
          const discoverResp = await fetch(`/api/ai-chat/projects/${projectId}/sessions`, { credentials: 'include' });
          if (discoverResp.ok) {
            const sessions = await discoverResp.json();
            const list: Array<Record<string, unknown>> = Array.isArray(sessions)
              ? sessions
              : Array.isArray((sessions as any)?.data)
              ? (sessions as any).data
              : [];
            const active = list.find((s) => (s as any)?.status === 'active') as any;
            if (active) {
              effectiveSessionId = active.id || active.session_id || active.sessionId || null;
            }
          }
        } catch {}
      }

      if (!effectiveSessionId) {
        // Create a new session
        const createResp = await fetch('/api/ai-chat/sessions', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ projectId }),
          credentials: 'include',
        });
        if (!createResp.ok) {
          throw new Error(`Failed to create AI session: ${createResp.status} ${createResp.statusText}`);
        }
        const created = await createResp.json();
        effectiveSessionId = created?.session_id ?? created?.id ?? created?.data?.id ?? created?.sessionId ?? null;
        if (!effectiveSessionId) {
          throw new Error('No session ID returned from session creation');
        }
      }

      // Persist session on hook and store
      if (effectiveSessionId) {
        setSessionId(effectiveSessionId);
        if (!chatSession || chatSession.id !== effectiveSessionId) {
          setChatSession({ id: effectiveSessionId, projectId });
        }
      }

      // Open SSE BEFORE sending chat so tokens have a stream to go to
      openStream(effectiveSessionId);
      // Switch to streaming state; SSE listeners will append tokens
      setProjectLoading(projectId, false);
      setProjectStreaming(projectId, true);
      // Ensure an AI message container exists to stream into
      updateLastMessage("");

      // Send coaching message to the session chat endpoint
      const resp = await fetch(`/api/ai-chat/sessions/${effectiveSessionId}/chat`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          content: trimmed,
          message: trimmed,
          agent_stage: 'COACHING',
        }),
        credentials: 'include',
        signal: abortControllerRef.current.signal,
      });

      if (!resp.ok) {
        const errorText = await resp.text().catch(() => '');
        throw new Error(`Chat failed: ${resp.status} ${resp.statusText} ${errorText}`);
      }
      // If the backend responded synchronously without streaming, unlock UI
      setProjectStreaming(projectId, false);
    } catch (error: any) {
      console.error('[useAiChat] Error sending message:', error);
      if (error?.name !== 'AbortError') {
        const errorMessage: ChatMessage = {
          id: `error-${Date.now()}`,
          user: 'System',
          avatar: '',
          message: "Sorry, I'm having trouble responding right now. Please try again later.",
          timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
          isCurrentUser: false,
        };
        addMessage(errorMessage);
      }
      setProjectStreaming(projectId, false);
    } finally {
      setProjectLoading(projectId, false);
      // Ensure UI is re-enabled even if completion event was missed
      setProjectStreaming(projectId, false);
      abortControllerRef.current = null;
    }
  }, [
    projectId,
    sessionId,
    addMessage,
    updateLastMessage,
    appendToLastMessage,
    setProjectLoading,
    setProjectStreaming,
    chatSession,
    setChatSession,
    openStream,
  ]);

  // Cleanup SSE on unmount
  useEffect(() => {
    return () => {
      try { esRef.current?.close(); } catch {}
      esRef.current = null;
    };
  }, []);

  const cancelMessage = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      setProjectLoading(projectId, false);
      setProjectStreaming(projectId, false);
    }
  }, [projectId, setProjectLoading, setProjectStreaming]);

  return {
    sendMessage,
    cancelMessage,
    isLoading,
    isStreaming,
    sessionId,
  };
}
