import { useSessionStore } from "@/stores/sessionStore";
import type {
  LoginCredentials,
  SignupCredentials,
} from "@/types/Session.types";
import { useRouter } from "next/navigation";
import { useCallback, useState } from "react";
import { showErrorToast, showSuccessToast } from "./useToast";

export function useAuth() {
  const {
    user,
    isAuthenticated,
    error,
    pendingEmailVerification,
    emailVerificationSent,
    actions,
  } = useSessionStore();
  const router = useRouter();

  // Local loading states for different operations
  const [isLoading, setIsLoading] = useState(false);

  const login = useCallback(
    async (credentials: LoginCredentials) => {
      setIsLoading(true);
      try {
        await actions.login(credentials);
        // Get the user from the store after successful login
        const { user } = useSessionStore.getState();

        showSuccessToast("Welcome back!", {
          description: `Logged in as ${user?.email}`,
        });

        // Redirect directly to the appropriate dashboard based on user role
        if (user?.role === "admin") {
          router.replace("/admin");
        } else {
          router.replace("/user-dashboard");
        }
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Login failed";
        showErrorToast("Login Failed", {
          description: errorMessage,
        });
        throw error;
      } finally {
        setIsLoading(false);
      }
    },
    [actions, router, setIsLoading]
  );

  const signup = useCallback(
    async (credentials: SignupCredentials) => {
      setIsLoading(true);
      try {
        await actions.signup(credentials);
        // Get the user from the store after successful signup
        const { user } = useSessionStore.getState();

        showSuccessToast("Account created successfully!", {
          description: `Welcome ${user?.name || user?.email}!`,
        });

        // Redirect directly to the appropriate dashboard based on user role
        if (user?.role === "admin") {
          router.replace("/admin");
        } else {
          router.replace("/user-dashboard");
        }
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Signup failed";
        showErrorToast("Signup Failed", {
          description: errorMessage,
        });
        throw error;
      } finally {
        setIsLoading(false);
      }
    },
    [actions, router, setIsLoading]
  );

  const logout = useCallback(() => {
    actions.logout();
    router.push("/auth/login");
  }, [actions, router]);

  const clearError = useCallback(() => {
    actions.setError(null);
  }, [actions]);

  const sendEmailVerification = useCallback(
    async (email: string, name: string) => {
      try {
        await actions.sendEmailVerification(email, name);
      } catch (error) {
        throw error;
      }
    },
    [actions]
  );

  const verifyEmail = useCallback(
    async (email: string, code: string) => {
      try {
        await actions.verifyEmail(email, code);
      } catch (error) {
        // Let the calling component handle the error display
        throw error;
      }
    },
    [actions]
  );

  const resendEmailVerification = useCallback(
    async (email: string) => {
      try {
        await actions.resendEmailVerification(email);
      } catch (error) {
        throw error;
      }
    },
    [actions]
  );

  return {
    // State
    user,
    isAuthenticated,
    isLoading,
    error,
    pendingEmailVerification,
    emailVerificationSent,

    // Actions
    login,
    signup,
    logout,
    clearError,
    sendEmailVerification,
    verifyEmail,
    resendEmailVerification,
    updateUser: actions.updateUser,
    refreshToken: actions.refreshToken,
  };
}
