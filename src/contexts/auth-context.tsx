"use client";

import { initializeAdminApi } from "@/lib/admin-api";
import { MockAuthAPI } from "@/lib/mock-auth-api";
import { SessionManager } from "@/lib/session";
import { AuthState } from "@/lib/types";
import React, { createContext, useContext, useEffect, useState } from "react";

interface AuthContextType extends AuthState {
  login: (credentials: {
    email: string;
    password: string;
    rememberMe?: boolean;
  }) => Promise<void>;
  register: (
    email: string,
    password: string,
    firstName: string,
    lastName: string,
    referralCode?: string
  ) => Promise<void>;
  logout: () => void;
  refreshAuth: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [state, setState] = useState<AuthState>({
    user: null,
    isAuthenticated: false,
    isLoading: true, // Start with loading true to check stored auth
    error: null,
  });

  // Initialize authentication state from stored session
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        // Check if we have stored authentication
        const storedUser = SessionManager.getUser();
        const accessToken = SessionManager.getAccessToken();

        if (storedUser && accessToken) {
          // Initialize admin API if we have a token
          initializeAdminApi(accessToken);

          setState({
            user: storedUser,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });
        } else {
          // No stored auth, clear any invalid session data
          SessionManager.clearInvalidSession();
          setState((prev) => ({
            ...prev,
            isLoading: false,
          }));
        }
      } catch (error) {
        console.error("Error initializing auth:", error);
        SessionManager.clearSession();
        setState({
          user: null,
          isAuthenticated: false,
          isLoading: false,
          error: null,
        });
      }
    };

    initializeAuth();
  }, []);

  const login = async (credentials: {
    email: string;
    password: string;
    rememberMe?: boolean;
  }) => {
    setState((prev) => ({ ...prev, isLoading: true, error: null }));

    try {
      const { email, password, rememberMe = false } = credentials;

      // Use mock API for development
      const data = await MockAuthAPI.login(email, password);

      // Create user object from API response
      const user = {
        id: data.user.id,
        email: data.user.email,
        name: data.user.name,
        role: data.user.role as "admin" | "user",
        createdAt: new Date(data.user.createdAt),
        updatedAt: new Date(data.user.updatedAt),
      };

      // Store tokens and user using SessionManager
      const accessToken = data.tokens.accessToken;
      const refreshToken = data.tokens.refreshToken;

      if (accessToken) {
        SessionManager.setTokens(
          {
            accessToken,
            refreshToken,
          },
          rememberMe
        );
        SessionManager.setUser(user, rememberMe);

        // Initialize admin API
        initializeAdminApi(accessToken);
      } else {
        throw new Error("No access token received from server");
      }

      setState({
        user,
        isAuthenticated: true,
        isLoading: false,
        error: null,
      });
    } catch (error: any) {
      setState((prev) => ({
        ...prev,
        isLoading: false,
        error: error.message || "Login failed",
      }));
      throw error;
    }
  };

  const register = async (
    email: string,
    password: string,
    firstName: string,
    lastName: string,
    referralCode?: string
  ) => {
    setState((prev) => ({ ...prev, isLoading: true, error: null }));

    try {
      // Use mock API for development
      const data = await MockAuthAPI.register(
        email,
        password,
        firstName,
        lastName,
        referralCode
      );

      // Create user object from API response
      const user = {
        id: data.user.id,
        email: data.user.email,
        name: data.user.name,
        role: data.user.role as "admin" | "user",
        createdAt: new Date(data.user.createdAt),
        updatedAt: new Date(data.user.updatedAt),
      };

      // Store tokens and user using SessionManager
      const accessToken = data.tokens.accessToken;
      const refreshToken = data.tokens.refreshToken;

      if (accessToken) {
        SessionManager.setTokens(
          {
            accessToken,
            refreshToken,
          },
          false // Default to session storage for registration
        );
        SessionManager.setUser(user, false);

        // Initialize admin API if user is admin
        if (user.role === "admin") {
          initializeAdminApi(accessToken);
        }
      } else {
        throw new Error("No access token received from server");
      }

      setState({
        user,
        isAuthenticated: true,
        isLoading: false,
        error: null,
      });
    } catch (error: any) {
      setState((prev) => ({
        ...prev,
        isLoading: false,
        error: error.message || "Registration failed",
      }));
      throw error;
    }
  };

  const logout = async () => {
    try {
      // Clear session storage
      SessionManager.clearSession();

      // Try to call logout API endpoint if available
      try {
        const apiUrl =
          process.env.NEXT_PUBLIC_ADMIN_API_URL || "http://localhost:3002";
        await fetch(`${apiUrl}/auth/logout`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            ...SessionManager.getAuthHeaders(),
          },
        });
      } catch (apiError) {
        // Ignore API logout errors - local logout is more important
        console.log("API logout failed, but local logout succeeded");
      }
    } catch (error) {
      console.error("Logout error:", error);
    } finally {
      // Always clear local state
      setState({
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
      });
    }
  };

  const refreshAuth = async () => {
    try {
      const refreshToken = SessionManager.getRefreshToken();

      if (!refreshToken) {
        throw new Error("No refresh token available");
      }

      const apiUrl =
        process.env.NEXT_PUBLIC_ADMIN_API_URL || "http://localhost:3002";
      const response = await fetch(`${apiUrl}/auth/refresh`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ refreshToken }),
      });

      if (!response.ok) {
        throw new Error("Token refresh failed");
      }

      const data = await response.json();
      const newAccessToken = data.access_token || data.accessToken;
      const newRefreshToken =
        data.refresh_token || data.refreshToken || newAccessToken;

      if (newAccessToken) {
        // Update tokens
        SessionManager.setTokens(
          {
            accessToken: newAccessToken,
            refreshToken: newRefreshToken,
          },
          !!localStorage.getItem("siift_access_token") // Preserve storage type
        );

        // Initialize admin API with new token
        initializeAdminApi(newAccessToken);
      }

      setState((prev) => ({
        ...prev,
        error: null,
      }));
    } catch (error) {
      console.error("Token refresh failed:", error);
      // If refresh fails, logout the user
      await logout();
    }
  };

  const value: AuthContextType = {
    ...state,
    login,
    register,
    logout,
    refreshAuth,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}
