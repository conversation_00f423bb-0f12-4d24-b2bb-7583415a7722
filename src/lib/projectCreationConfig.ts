// Configuration for project creation event streaming
export const PROJECT_CREATION_CONFIG = {
  // Force real backend/SSE; disable mock
  mode: 'sse' as 'mock' | 'socket' | 'sse',
  
  // Socket.IO configuration
  socketUrl: 'ws://localhost:3002',
  
  // Server-Sent Events configuration
  sseEndpoint: '/api/project-creation/stream',
  apiEndpoint: '/api/project-creation/start',
  
  // Mock configuration
  mockDelay: 1500, // milliseconds between progress updates
  mockStepDelay: 1000, // milliseconds to complete each step
};

// Export the appropriate hook based on configuration
export const useProjectCreationStream = () => {
  switch (PROJECT_CREATION_CONFIG.mode) {
    case 'socket':
      // Dynamically import to avoid bundling unused code
      return import('@/lib/realEventStream').then(module => 
        module.useRealProjectCreationStream()
      );
    
    case 'sse':
      // You can implement SSE hook here
      return import('@/lib/realEventStream').then(module => {
        const stream = new module.RealEventSourceStream();
        return {
          startCreation: (ideaText: string) => {
            stream.connect();
            stream.startProjectCreation(ideaText);
          },
          addEventListener: stream.addEventListener.bind(stream),
          removeEventListener: stream.removeEventListener.bind(stream),
        };
      });
    
    case 'mock':
    default:
      // No-op to avoid mock usage in production
      return Promise.resolve({
        startCreation: (_ideaText: string) => {},
        addEventListener: (_handler: any) => {},
        removeEventListener: (_handler: any) => {},
      } as any);
  }
};

// Helper function to get the current configuration
export const getProjectCreationConfig = () => PROJECT_CREATION_CONFIG;

// Helper function to update configuration (useful for runtime switching)
export const updateProjectCreationConfig = (updates: Partial<typeof PROJECT_CREATION_CONFIG>) => {
  Object.assign(PROJECT_CREATION_CONFIG, updates);
};
