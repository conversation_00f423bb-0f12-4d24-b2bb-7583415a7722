// Mock API service for authentication
// This simulates real API calls with delays and realistic responses

import { MockEmailService } from "./mock-email-service";

interface MockUser {
  id: string;
  email: string;
  name: string;
  role: "admin" | "user";
  createdAt: string;
  updatedAt: string;
  isEmailVerified: boolean;
}

interface AuthTokens {
  accessToken: string;
  refreshToken: string;
  expiresAt: number; // Unix timestamp
}

interface MockAuthResponse {
  user: MockUser;
  tokens: AuthTokens;
}

// Mock database
const mockUsers: MockUser[] = [
  {
    id: "admin-1",
    email: "<EMAIL>",
    name: "Admin User",
    role: "admin",
    isEmailVerified: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: "user-1",
    email: "<EMAIL>",
    name: "Test User",
    role: "user",
    isEmailVerified: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
];

// Mock verification codes storage
const mockVerificationCodes: Record<string, { code: string; expires: number }> =
  {};

// Helper function to simulate API delay
const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

// Helper function to generate mock JWT token
const generateMockToken = (user: MockUser): string => {
  const payload = {
    userId: user.id,
    email: user.email,
    role: user.role,
    exp: Math.floor(Date.now() / 1000) + 7 * 24 * 60 * 60, // 7 days
  };
  return `mock.jwt.${btoa(JSON.stringify(payload))}`;
};

// Helper function to generate verification code
const generateVerificationCode = (): string => {
  return Math.floor(100000 + Math.random() * 900000).toString();
};

export class MockAuthAPI {
  // Mock login
  static async login(
    email: string,
    password: string
  ): Promise<MockAuthResponse> {
    await delay(1000); // Simulate network delay

    const user = mockUsers.find((u) => u.email === email);

    if (!user) {
      throw new Error("User not found");
    }

    // Mock password validation (in real app, this would be hashed)
    const validPasswords: Record<string, string> = {
      "<EMAIL>": "123456789@",
      "<EMAIL>": "password123",
    };

    if (validPasswords[email] !== password) {
      throw new Error("Invalid password");
    }

    const accessToken = generateMockToken(user);
    const refreshToken = generateMockToken(user);
    const expiresAt = Date.now() + 7 * 24 * 60 * 60 * 1000; // 7 days from now

    return {
      user,
      tokens: {
        accessToken,
        refreshToken,
        expiresAt,
      },
    };
  }

  // Mock register
  static async register(
    email: string,
    password: string,
    firstName: string,
    lastName: string,
    referralCode?: string
  ): Promise<MockAuthResponse> {
    await delay(1200); // Simulate network delay

    // Check if user already exists
    const existingUser = mockUsers.find((u) => u.email === email);
    if (existingUser) {
      throw new Error("User already exists with this email");
    }

    // Validate referral code if provided
    if (referralCode) {
      if (referralCode === "000000") {
        throw new Error("Invalid referral code. Please check and try again.");
      }
      // For demo purposes, accept any other referral code
      console.log(`Mock: Referral code ${referralCode} accepted`);
    }

    // Create new user
    const newUser: MockUser = {
      id: `user-${Date.now()}`,
      email,
      name: `${firstName} ${lastName}`,
      role: "user",
      isEmailVerified: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    // Add to mock database
    mockUsers.push(newUser);

    // Send email verification (don't wait for it to complete)
    try {
      await MockEmailService.sendEmailVerification({
        email: newUser.email,
        name: newUser.name,
        type: "registration",
      });
    } catch (error) {
      console.warn(
        "Failed to send verification email during registration:",
        error
      );
      // Don't fail registration if email sending fails
    }

    const accessToken = generateMockToken(newUser);
    const refreshToken = generateMockToken(newUser);
    const expiresAt = Date.now() + 7 * 24 * 60 * 60 * 1000; // 7 days from now

    return {
      user: newUser,
      tokens: {
        accessToken,
        refreshToken,
        expiresAt,
      },
    };
  }

  // Mock forgot password
  static async forgotPassword(
    email: string
  ): Promise<{ message: string; code?: string }> {
    await delay(800); // Simulate network delay

    const user = mockUsers.find((u) => u.email === email);
    if (!user) {
      // For security, don't reveal if email exists
      // But still return success to prevent email enumeration
      console.log(
        `Mock: Email ${email} not found, but returning success for security`
      );
      return {
        message:
          "If an account with this email exists, a reset code has been sent.",
      };
    }

    try {
      // Use MockEmailService to send password reset email
      const response = await MockEmailService.sendPasswordReset({
        email: user.email,
        name: user.name,
      });

      return {
        message: response.message,
        code: response.code, // Only in development
      };
    } catch (error) {
      if (error instanceof Error && "code" in error) {
        throw error;
      }
      throw new Error("Failed to send password reset email");
    }
  }

  // Mock reset password
  static async resetPassword(
    email: string,
    code: string,
    newPassword: string
  ): Promise<{ message: string }> {
    await delay(1000); // Simulate network delay

    const user = mockUsers.find((u) => u.email === email);
    if (!user) {
      throw new Error("User not found");
    }

    try {
      // Verify code using MockEmailService
      await MockEmailService.verifyCode({
        email,
        code,
        type: "password-reset",
      });

      // In a real app, you would hash and store the new password
      console.log(`Mock: Password reset successful for ${email}`);
      console.log(`Mock: New password would be: ${newPassword}`);

      // Update user's password in mock database (in real app, this would be hashed)
      // For now, we'll just update the valid passwords record
      const validPasswords: Record<string, string> = {
        "<EMAIL>": "123456789@",
        "<EMAIL>": "password123",
      };
      validPasswords[email] = newPassword;

      return {
        message: "Password reset successfully",
      };
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error("Password reset failed");
    }
  }

  // Mock refresh token
  static async refreshToken(refreshToken: string): Promise<{
    tokens: AuthTokens;
  }> {
    await delay(500); // Simulate network delay

    try {
      // In a real app, you would validate the refresh token
      const payload = JSON.parse(atob(refreshToken.split(".")[2]));
      const user = mockUsers.find((u) => u.id === payload.userId);

      if (!user) {
        throw new Error("Invalid refresh token");
      }

      const newAccessToken = generateMockToken(user);
      const newRefreshToken = generateMockToken(user);
      const expiresAt = Date.now() + 7 * 24 * 60 * 60 * 1000; // 7 days from now

      return {
        tokens: {
          accessToken: newAccessToken,
          refreshToken: newRefreshToken,
          expiresAt,
        },
      };
    } catch (error) {
      throw new Error("Invalid refresh token");
    }
  }

  // Mock verify token
  static async verifyToken(token: string): Promise<boolean> {
    await delay(300); // Simulate network delay

    try {
      const payload = JSON.parse(atob(token.split(".")[2]));
      return payload.exp > Math.floor(Date.now() / 1000);
    } catch (error) {
      return false;
    }
  }

  // Helper method to get current verification codes (for debugging)
  static getVerificationCodes(): Record<
    string,
    { code: string; expires: number }
  > {
    return { ...mockVerificationCodes };
  }

  // Helper method to clear expired codes
  static clearExpiredCodes(): void {
    const now = Date.now();
    Object.keys(mockVerificationCodes).forEach((email) => {
      if (mockVerificationCodes[email].expires < now) {
        delete mockVerificationCodes[email];
      }
    });
  }

  // Send email verification code
  static async sendEmailVerification(
    email: string,
    name: string
  ): Promise<{ success: boolean; message: string; code?: string }> {
    await delay(800); // Simulate network delay

    try {
      // Check if user exists
      const user = mockUsers.find((u) => u.email === email);
      if (!user) {
        throw new Error("User not found");
      }

      // Use MockEmailService to send verification email
      const response = await MockEmailService.sendEmailVerification({
        email,
        name,
        type: "registration",
      });

      return {
        success: response.success,
        message: response.message,
        code: response.code, // Only in development
      };
    } catch (error) {
      if (error instanceof Error && "code" in error) {
        throw error;
      }
      throw new Error("Failed to send verification email");
    }
  }

  // Verify email with code
  static async verifyEmail(
    email: string,
    code: string
  ): Promise<{ success: boolean; message: string }> {
    await delay(600); // Simulate network delay

    try {
      // Verify code using MockEmailService
      await MockEmailService.verifyCode({
        email,
        code,
        type: "email-verification",
      });

      // Update user's email verification status
      const user = mockUsers.find((u) => u.email === email);
      if (user) {
        user.isEmailVerified = true;
        user.updatedAt = new Date().toISOString();
      }

      return {
        success: true,
        message: "Email verified successfully",
      };
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error("Email verification failed");
    }
  }

  // Resend email verification code
  static async resendEmailVerification(
    email: string
  ): Promise<{ success: boolean; message: string; code?: string }> {
    await delay(800); // Simulate network delay

    try {
      // Check if user exists
      const user = mockUsers.find((u) => u.email === email);
      if (!user) {
        throw new Error("User not found");
      }

      if (user.isEmailVerified) {
        throw new Error("Email is already verified");
      }

      // Use MockEmailService to resend verification email
      const response = await MockEmailService.sendEmailVerification({
        email,
        name: user.name,
        type: "registration",
      });

      return {
        success: response.success,
        message: response.message,
        code: response.code, // Only in development
      };
    } catch (error) {
      if (error instanceof Error && "code" in error) {
        throw error;
      }
      throw new Error("Failed to resend verification email");
    }
  }
}
