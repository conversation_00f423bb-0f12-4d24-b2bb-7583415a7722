// Icon size configuration - centralized place to manage all icon sizes
export const ICON_SIZES = {
  // Base icon sizes (slightly larger than standard)
  sm: "h-4 w-4",    // Small icons (1.25x from h-4 w-4)
  md: "h-6 w-6",    // Medium icons (increased from h-5 w-5 to h-6 w-6)
  lg: "h-7 w-7",    // Large icons (1.17x from h-6 w-6)
  xl: "h-8 w-8",    // Extra large icons (1.14x from h-7 w-7)

  // Specific use case sizes (slightly larger for better visibility)
  badge: "h-4 w-4",           // Badge icons (1.33x from h-3 w-3)
  navigation: "h-6 w-6",      // Navigation icons (1.2x from h-5 w-5)
  sidebar: "h-6 w-6",         // Sidebar icons (1.2x from h-5 w-5)
  header: "h-6 w-6",          // Header icons (1.2x from h-5 w-5)
  button: "h-6 w-6",          // Button icons (1.2x from h-5 w-5)

  // Project specific sizes (slightly larger)
  projectIcon: "h-6 w-6",     // Project folder icons (1.17x from h-6 w-6)
  actionIcon: "h-6 w-6",      // Action icons (1.2x from h-5 w-5)
  chatIcon: "h-6 w-6",        // Chat icons (1.2x from h-5 w-5)
  profileIcon: "h-6 w-6",     // Profile icons (1.2x from h-5 w-5)
  notificationIcon: "h-6 w-6", // Notification icons (1.2x from h-5 w-5)
} as const;

// Icon size multiplier for easy scaling
export const ICON_SCALE_FACTOR = 1;

// Helper function to get icon size
export function getIconSize(size: keyof typeof ICON_SIZES): string {
  return ICON_SIZES[size];
}

// Helper function to create custom icon size
export function createIconSize(baseSize: number): string {
  const scaledSize = Math.round(baseSize * ICON_SCALE_FACTOR);
  return `h-${scaledSize} w-${scaledSize}`;
}
