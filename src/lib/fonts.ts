import localFont from "next/font/local";
import { Inter } from "next/font/google";

// Outfit font family from public/fonts/outfit folder
export const outfit = localFont({
  src: [
    {
      path: "../../public/fonts/outfit/Outfit-Thin.ttf",
      weight: "100",
      style: "normal",
    },
    {
      path: "../../public/fonts/outfit/Outfit-ExtraLight.ttf",
      weight: "200",
      style: "normal",
    },
    {
      path: "../../public/fonts/outfit/Outfit-Light.ttf",
      weight: "300",
      style: "normal",
    },
    {
      path: "../../public/fonts/outfit/Outfit-Regular.ttf",
      weight: "400",
      style: "normal",
    },
    {
      path: "../../public/fonts/outfit/Outfit-Medium.ttf",
      weight: "500",
      style: "normal",
    },
    {
      path: "../../public/fonts/outfit/Outfit-SemiBold.ttf",
      weight: "600",
      style: "normal",
    },
    {
      path: "../../public/fonts/outfit/Outfit-Bold.ttf",
      weight: "700",
      style: "normal",
    },
    {
      path: "../../public/fonts/outfit/Outfit-ExtraBold.ttf",
      weight: "800",
      style: "normal",
    },
    {
      path: "../../public/fonts/outfit/Outfit-Black.ttf",
      weight: "900",
      style: "normal",
    },
  ],
  variable: "--font-outfit",
  display: "swap",
});

// Variable font version (alternative)
export const outfitVariable = localFont({
  src: "../../public/fonts/outfit/Outfit-VariableFont_wght.ttf",
  variable: "--font-outfit-variable",
  weight: "100 900",
  display: "swap",
});

// Inter font (brand default for headings and body)
export const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
  display: "swap",
});
