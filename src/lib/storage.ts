/**
 * SSR-safe localStorage utilities with proper error handling
 */

export const safeLocalStorage = {
  /**
   * Safely get an item from localStorage
   */
  getItem: (key: string): string | null => {
    if (typeof window === 'undefined') return null;
    
    try {
      return localStorage.getItem(key);
    } catch (error) {
      console.warn(`Failed to get localStorage item "${key}":`, error);
      return null;
    }
  },

  /**
   * Safely set an item in localStorage
   */
  setItem: (key: string, value: string): boolean => {
    if (typeof window === 'undefined') return false;
    
    try {
      localStorage.setItem(key, value);
      return true;
    } catch (error) {
      console.warn(`Failed to set localStorage item "${key}":`, error);
      return false;
    }
  },

  /**
   * Safely remove an item from localStorage
   */
  removeItem: (key: string): boolean => {
    if (typeof window === 'undefined') return false;
    
    try {
      localStorage.removeItem(key);
      return true;
    } catch (error) {
      console.warn(`Failed to remove localStorage item "${key}":`, error);
      return false;
    }
  },

  /**
   * Safely parse JSON from localStorage
   */
  getJSON: <T>(key: string, fallback: T): T => {
    const item = safeLocalStorage.getItem(key);
    if (!item) return fallback;
    
    try {
      return JSON.parse(item) as T;
    } catch (error) {
      console.warn(`Failed to parse JSON from localStorage "${key}":`, error);
      return fallback;
    }
  },

  /**
   * Safely stringify and store JSON in localStorage
   */
  setJSON: <T>(key: string, value: T): boolean => {
    try {
      const serialized = JSON.stringify(value);
      return safeLocalStorage.setItem(key, serialized);
    } catch (error) {
      console.warn(`Failed to stringify and store JSON "${key}":`, error);
      return false;
    }
  }
};