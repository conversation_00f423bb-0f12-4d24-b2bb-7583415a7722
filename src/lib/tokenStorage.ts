import { AuthTokens } from "@/types/Session.types";

// Constants
const TOKEN_KEY = "auth_tokens";
const USER_KEY = "user_data";
const ENCRYPTION_KEY =
  process.env.NEXT_PUBLIC_TOKEN_ENCRYPTION_KEY ||
  "fallback-key-change-in-production";

// Simple encryption/decryption (in production, use a proper encryption library)
class TokenEncryption {
  private static key: string = ENCRYPTION_KEY; // Initialize immediately

  static initialize() {
    this.key = ENCRYPTION_KEY;
  }

  static encrypt(text: string): string {
    if (typeof window === "undefined") return text; // Server-side

    try {
      // Ensure key is initialized
      if (!this.key) this.key = ENCRYPTION_KEY;

      // Simple XOR encryption (replace with proper encryption in production)
      let result = "";
      for (let i = 0; i < text.length; i++) {
        result += String.fromCharCode(
          text.charCodeAt(i) ^ this.key.charCodeAt(i % this.key.length)
        );
      }
      return btoa(result); // Base64 encode
    } catch (error) {
      console.error("Encryption failed:", error);
      return text;
    }
  }

  static decrypt(encryptedText: string): string {
    if (typeof window === "undefined") return encryptedText; // Server-side

    try {
      // Ensure key is initialized
      if (!this.key) this.key = ENCRYPTION_KEY;

      const decoded = atob(encryptedText); // Base64 decode
      let result = "";
      for (let i = 0; i < decoded.length; i++) {
        result += String.fromCharCode(
          decoded.charCodeAt(i) ^ this.key.charCodeAt(i % this.key.length)
        );
      }
      return result;
    } catch (error) {
      console.error("Decryption failed:", error);
      return encryptedText;
    }
  }
}

// Initialize encryption
TokenEncryption.initialize();

export class TokenStorage {
  /**
   * Store tokens securely
   */
  static setTokens(tokens: AuthTokens): void {
    if (typeof window === "undefined") return; // Server-side

    try {
      const encryptedTokens = TokenEncryption.encrypt(JSON.stringify(tokens));
      sessionStorage.setItem(TOKEN_KEY, encryptedTokens);

      // Also store in localStorage if tokens are long-lived (refresh tokens)
      if (tokens.expiresAt > Date.now() + 7 * 24 * 60 * 60 * 1000) {
        // 7 days
        localStorage.setItem(TOKEN_KEY, encryptedTokens);
      }
    } catch (error) {
      console.error("Failed to store tokens:", error);
    }
  }

  /**
   * Retrieve tokens securely
   */
  static getTokens(): AuthTokens | null {
    if (typeof window === "undefined") return null; // Server-side

    try {
      // Try sessionStorage first (more secure)
      let encryptedTokens = sessionStorage.getItem(TOKEN_KEY);

      if (!encryptedTokens) {
        // Fallback to localStorage
        encryptedTokens = localStorage.getItem(TOKEN_KEY);
      }

      if (!encryptedTokens) return null;

      const decryptedTokens = TokenEncryption.decrypt(encryptedTokens);
      const tokens: AuthTokens = JSON.parse(decryptedTokens);

      // Check if tokens are expired
      if (tokens.expiresAt < Date.now()) {
        this.clearTokens();
        return null;
      }

      return tokens;
    } catch (error) {
      console.error("Failed to retrieve tokens:", error);
      this.clearTokens();
      return null;
    }
  }

  /**
   * Clear all stored tokens
   */
  static clearTokens(): void {
    if (typeof window === "undefined") return; // Server-side

    try {
      sessionStorage.removeItem(TOKEN_KEY);
      localStorage.removeItem(TOKEN_KEY);
    } catch (error) {
      console.error("Failed to clear tokens:", error);
    }
  }

  /**
   * Check if tokens exist and are valid
   */
  static hasValidTokens(): boolean {
    const tokens = this.getTokens();
    return tokens !== null && tokens.expiresAt > Date.now();
  }

  /**
   * Get access token only
   */
  static getAccessToken(): string | null {
    const tokens = this.getTokens();
    return tokens?.accessToken || null;
  }

  /**
   * Get refresh token only
   */
  static getRefreshToken(): string | null {
    const tokens = this.getTokens();
    return tokens?.refreshToken || null;
  }

  /**
   * Store user data (non-sensitive)
   */
  static setUser(user: any): void {
    if (typeof window === "undefined") return; // Server-side

    try {
      // Store user data in both sessionStorage and localStorage for persistence
      const userData = JSON.stringify(user);
      sessionStorage.setItem(USER_KEY, userData);
      localStorage.setItem(USER_KEY, userData);
    } catch (error) {
      console.error("Failed to store user data:", error);
    }
  }

  /**
   * Retrieve user data
   */
  static getUser(): any | null {
    if (typeof window === "undefined") return null; // Server-side

    try {
      // Try sessionStorage first, then localStorage
      let userData = sessionStorage.getItem(USER_KEY);
      if (!userData) {
        userData = localStorage.getItem(USER_KEY);
      }
      return userData ? JSON.parse(userData) : null;
    } catch (error) {
      console.error("Failed to retrieve user data:", error);
      return null;
    }
  }

  /**
   * Clear user data
   */
  static clearUser(): void {
    if (typeof window === "undefined") return; // Server-side

    try {
      sessionStorage.removeItem(USER_KEY);
      localStorage.removeItem(USER_KEY);
    } catch (error) {
      console.error("Failed to clear user data:", error);
    }
  }

  /**
   * Clear all session data
   */
  static clearAll(): void {
    this.clearTokens();
    this.clearUser();
  }
}
