// Mock Email Service for development and testing
// Simulates email sending with verification codes and rate limiting

import type {
  CodeVerificationRequest,
  EmailMessage,
  EmailServiceConfig,
  EmailServiceError,
  EmailServiceResponse,
  EmailTemplate,
  EmailVerificationRequest,
  PasswordResetRequest,
  RateLimitInfo,
  VerificationCode,
} from "@/types/email.types";

// Default configuration
const DEFAULT_CONFIG: EmailServiceConfig = {
  codeLength: 6,
  codeExpirationMinutes: 15,
  maxAttempts: 3,
  rateLimitMinutes: 1,
  maxEmailsPerHour: 10,
};

// In-memory storage for development (in production, this would be a database)
class EmailStorage {
  private static codes: Map<string, VerificationCode> = new Map();
  private static messages: EmailMessage[] = [];
  private static rateLimits: Map<string, RateLimitInfo> = new Map();

  static savecode(key: string, code: VerificationCode): void {
    this.codes.set(key, code);
  }

  static getCode(key: string): VerificationCode | undefined {
    return this.codes.get(key);
  }

  static deleteCode(key: string): void {
    this.codes.delete(key);
  }

  static saveMessage(message: EmailMessage): void {
    this.messages.push(message);
    // Keep only last 100 messages for development
    if (this.messages.length > 100) {
      this.messages.shift();
    }
  }

  static getMessages(email?: string): EmailMessage[] {
    if (email) {
      return this.messages.filter((msg) => msg.to === email);
    }
    return [...this.messages];
  }

  static setRateLimit(email: string, info: RateLimitInfo): void {
    this.rateLimits.set(email, info);
  }

  static getRateLimit(email: string): RateLimitInfo | undefined {
    return this.rateLimits.get(email);
  }

  static clearExpiredCodes(): void {
    const now = Date.now();
    for (const [key, code] of this.codes.entries()) {
      if (code.expiresAt < now) {
        this.codes.delete(key);
      }
    }
  }

  static clearExpiredRateLimits(): void {
    const now = Date.now();
    for (const [email, limit] of this.rateLimits.entries()) {
      if (limit.resetAt < now) {
        this.rateLimits.delete(email);
      }
    }
  }

  static getAllCodes(): Map<string, VerificationCode> {
    return this.codes;
  }

  static clearMessages(): void {
    this.messages.splice(0);
  }

  static clearRateLimits(): void {
    this.rateLimits.clear();
  }
}

// Email templates
const EMAIL_TEMPLATES: Record<string, EmailTemplate> = {
  "email-verification": {
    subject: "Verify your email address",
    body: `
      <h2>Welcome to Siift!</h2>
      <p>Please verify your email address by entering this code:</p>
      <div style="font-size: 24px; font-weight: bold; color: #2563eb; padding: 20px; background: #f3f4f6; border-radius: 8px; text-align: center; margin: 20px 0;">
        {{CODE}}
      </div>
      <p>This code will expire in 15 minutes.</p>
      <p>If you didn't create an account, please ignore this email.</p>
    `,
    type: "verification",
  },
  "password-reset": {
    subject: "Reset your password",
    body: `
      <h2>Password Reset Request</h2>
      <p>You requested to reset your password. Use this code to continue:</p>
      <div style="font-size: 24px; font-weight: bold; color: #dc2626; padding: 20px; background: #fef2f2; border-radius: 8px; text-align: center; margin: 20px 0;">
        {{CODE}}
      </div>
      <p>This code will expire in 15 minutes.</p>
      <p>If you didn't request this, please ignore this email.</p>
    `,
    type: "password-reset",
  },
  welcome: {
    subject: "Welcome to Siift!",
    body: `
      <h2>Welcome to Siift!</h2>
      <p>Your email has been verified successfully.</p>
      <p>You can now access all features of your account.</p>
      <p>Thank you for joining us!</p>
    `,
    type: "welcome",
  },
};

export class MockEmailService {
  private static config: EmailServiceConfig = DEFAULT_CONFIG;

  // Generate a random verification code
  private static generateCode(): string {
    const length = this.config.codeLength;
    let code = "";
    for (let i = 0; i < length; i++) {
      code += Math.floor(Math.random() * 10).toString();
    }
    return code;
  }

  // Create a unique key for storing codes
  private static createCodeKey(email: string, type: string): string {
    return `${email}:${type}`;
  }

  // Check rate limiting
  private static checkRateLimit(email: string): void {
    EmailStorage.clearExpiredRateLimits();

    const rateLimit = EmailStorage.getRateLimit(email);
    const now = Date.now();

    if (rateLimit && rateLimit.resetAt > now) {
      if (rateLimit.count >= this.config.maxEmailsPerHour) {
        const error = new Error(
          "Rate limit exceeded. Please try again later."
        ) as EmailServiceError;
        error.code = "RATE_LIMITED";
        error.details = { resetAt: rateLimit.resetAt };
        throw error;
      }

      // Increment count
      EmailStorage.setRateLimit(email, {
        ...rateLimit,
        count: rateLimit.count + 1,
      });
    } else {
      // Create new rate limit entry
      EmailStorage.setRateLimit(email, {
        email,
        count: 1,
        resetAt: now + 60 * 60 * 1000, // 1 hour
      });
    }
  }

  // Send email verification code
  static async sendEmailVerification(
    request: EmailVerificationRequest
  ): Promise<EmailServiceResponse> {
    await new Promise((resolve) => setTimeout(resolve, 800)); // Simulate network delay

    try {
      this.checkRateLimit(request.email);

      const code = this.generateCode();
      const now = Date.now();
      const expiresAt = now + this.config.codeExpirationMinutes * 60 * 1000;

      // Store verification code
      const verificationCode: VerificationCode = {
        code,
        email: request.email,
        type: "email-verification",
        expiresAt,
        attempts: 0,
        maxAttempts: this.config.maxAttempts,
        createdAt: now,
      };

      const key = this.createCodeKey(request.email, "email-verification");
      EmailStorage.savecode(key, verificationCode);

      // Create email message
      const template = EMAIL_TEMPLATES["email-verification"];
      const message: EmailMessage = {
        id: `msg_${now}_${Math.random().toString(36).substr(2, 9)}`,
        to: request.email,
        subject: template.subject,
        body: template.body.replace("{{CODE}}", code),
        type: template.type,
        sentAt: now,
        verificationCode: code,
      };

      EmailStorage.saveMessage(message);

      // Clean up expired codes
      EmailStorage.clearExpiredCodes();

      return {
        success: true,
        messageId: message.id,
        message: "Verification email sent successfully",
      };
    } catch (error) {
      if (error instanceof Error && "code" in error) {
        throw error;
      }
      throw new Error("Failed to send verification email");
    }
  }

  // Send password reset code
  static async sendPasswordReset(
    request: PasswordResetRequest
  ): Promise<EmailServiceResponse> {
    await new Promise((resolve) => setTimeout(resolve, 800)); // Simulate network delay

    try {
      this.checkRateLimit(request.email);

      const code = this.generateCode();
      const now = Date.now();
      const expiresAt = now + this.config.codeExpirationMinutes * 60 * 1000;

      // Store verification code
      const verificationCode: VerificationCode = {
        code,
        email: request.email,
        type: "password-reset",
        expiresAt,
        attempts: 0,
        maxAttempts: this.config.maxAttempts,
        createdAt: now,
      };

      const key = this.createCodeKey(request.email, "password-reset");
      EmailStorage.savecode(key, verificationCode);

      // Create email message
      const template = EMAIL_TEMPLATES["password-reset"];
      const message: EmailMessage = {
        id: `msg_${now}_${Math.random().toString(36).substr(2, 9)}`,
        to: request.email,
        subject: template.subject,
        body: template.body.replace("{{CODE}}", code),
        type: template.type,
        sentAt: now,
        verificationCode: code,
      };

      EmailStorage.saveMessage(message);

      // Clean up expired codes
      EmailStorage.clearExpiredCodes();

      return {
        success: true,
        messageId: message.id,
        message: "Password reset email sent successfully",
      };
    } catch (error) {
      if (error instanceof Error && "code" in error) {
        throw error;
      }
      throw new Error("Failed to send password reset email");
    }
  }

  // Verify a code
  static async verifyCode(request: CodeVerificationRequest): Promise<boolean> {
    await new Promise((resolve) => setTimeout(resolve, 500)); // Simulate network delay

    EmailStorage.clearExpiredCodes();

    const key = this.createCodeKey(request.email, request.type);
    const storedCode = EmailStorage.getCode(key);

    if (!storedCode) {
      const error = new Error(
        "Verification code not found or expired"
      ) as EmailServiceError;
      error.code = "CODE_EXPIRED";
      throw error;
    }

    // Check if code is expired
    if (storedCode.expiresAt < Date.now()) {
      EmailStorage.deleteCode(key);
      const error = new Error(
        "Verification code has expired"
      ) as EmailServiceError;
      error.code = "CODE_EXPIRED";
      throw error;
    }

    // Check max attempts
    if (storedCode.attempts >= storedCode.maxAttempts) {
      EmailStorage.deleteCode(key);
      const error = new Error(
        "Maximum verification attempts exceeded"
      ) as EmailServiceError;
      error.code = "MAX_ATTEMPTS_EXCEEDED";
      throw error;
    }

    // Increment attempts
    storedCode.attempts++;
    EmailStorage.savecode(key, storedCode);

    // Check if code matches
    if (storedCode.code !== request.code) {
      const error = new Error("Invalid verification code") as EmailServiceError;
      error.code = "CODE_INVALID";
      error.details = {
        attemptsRemaining: storedCode.maxAttempts - storedCode.attempts,
      };
      throw error;
    }

    // Code is valid, remove it
    EmailStorage.deleteCode(key);
    return true;
  }

  // Get messages for development/debugging
  static getMessages(email?: string): EmailMessage[] {
    return EmailStorage.getMessages(email);
  }

  // Get the latest verification code for development/debugging
  static getLatestCode(
    email: string,
    type: "email-verification" | "password-reset"
  ): string | null {
    const key = this.createCodeKey(email, type);
    const code = EmailStorage.getCode(key);
    return code?.code || null;
  }

  // Get all active verification codes for development/debugging
  static getAllActiveCodes(): VerificationCode[] {
    EmailStorage.clearExpiredCodes();
    const allCodes: VerificationCode[] = [];

    // Access the codes Map through the public method
    const codesMap = EmailStorage.getAllCodes();
    for (const [key, code] of codesMap.entries()) {
      if (code.expiresAt > Date.now()) {
        allCodes.push(code);
      }
    }

    return allCodes;
  }

  // Clear all data (for testing)
  static clearAll(): void {
    const codesMap = EmailStorage.getAllCodes();
    codesMap.clear();
    EmailStorage.clearMessages();
    EmailStorage.clearRateLimits();
  }
}
