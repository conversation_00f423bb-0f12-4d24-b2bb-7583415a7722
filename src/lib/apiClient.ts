import { TokenStorage } from "@/lib/tokenStorage";
import { useSessionStore } from "@/stores/sessionStore";
import type { AuthError } from "@/types/Session.types";

// API Configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:3000";
const API_TIMEOUT = 10000; // 10 seconds

// Custom error class
export class ApiError extends Error {
  constructor(
    message: string,
    public status: number,
    public code?: string,
    public data?: any
  ) {
    super(message);
    this.name = "ApiError";
  }
}

// Request configuration interface
interface RequestConfig extends RequestInit {
  timeout?: number;
  skipAuth?: boolean;
  retryCount?: number;
}

// Response wrapper
interface ApiResponse<T = any> {
  data: T;
  message?: string;
  success: boolean;
}

class ApiClient {
  private baseURL: string;
  private defaultTimeout: number;

  constructor(baseURL: string, defaultTimeout: number = API_TIMEOUT) {
    this.baseURL = baseURL;
    this.defaultTimeout = defaultTimeout;
  }

  /**
   * Make an authenticated API request
   */
  async request<T = any>(
    endpoint: string,
    config: RequestConfig = {}
  ): Promise<T> {
    const {
      timeout = this.defaultTimeout,
      skipAuth = false,
      retryCount = 0,
      ...requestConfig
    } = config;

    // Build request URL
    const url = `${this.baseURL}${endpoint}`;

    // Prepare headers
    const headers = new Headers(requestConfig.headers);

    if (
      !headers.has("Content-Type") &&
      !(requestConfig.body instanceof FormData)
    ) {
      headers.set("Content-Type", "application/json");
    }

    // Add authentication header if not skipped
    if (!skipAuth) {
      const accessToken = TokenStorage.getAccessToken();
      if (accessToken) {
        headers.set("Authorization", `Bearer ${accessToken}`);
      }
    }

    // Create request configuration
    const requestInit: RequestInit = {
      ...requestConfig,
      headers,
    };

    // Create abort controller for timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
      const response = await fetch(url, {
        ...requestInit,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      // Handle authentication errors
      if (response.status === 401) {
        // Try to refresh token
        if (retryCount === 0) {
          try {
            await useSessionStore.getState().actions.refreshToken();
            // Retry the request once
            return this.request(endpoint, {
              ...config,
              retryCount: 1,
            });
          } catch (refreshError) {
            // Refresh failed, logout user
            useSessionStore.getState().actions.logout();
            throw new ApiError("Authentication failed", 401, "AUTH_FAILED");
          }
        } else {
          // Already retried, logout user
          useSessionStore.getState().actions.logout();
          throw new ApiError("Authentication failed", 401, "AUTH_FAILED");
        }
      }

      // Handle other HTTP errors
      if (!response.ok) {
        let errorData: any = {};
        try {
          errorData = await response.json();
        } catch (e) {
          // If response is not JSON, use status text
          errorData = { message: response.statusText };
        }

        throw new ApiError(
          errorData.message || `HTTP ${response.status}`,
          response.status,
          errorData.code,
          errorData
        );
      }

      // Parse response
      const contentType = response.headers.get("content-type");
      if (contentType && contentType.includes("application/json")) {
        const data = await response.json();
        return data;
      } else {
        return (await response.text()) as T;
      }
    } catch (error) {
      clearTimeout(timeoutId);

      if (error instanceof ApiError) {
        throw error;
      }

      if (error instanceof Error && error.name === "AbortError") {
        throw new ApiError("Request timeout", 408, "TIMEOUT");
      }

      throw new ApiError(
        error instanceof Error ? error.message : "Network error",
        0,
        "NETWORK_ERROR"
      );
    }
  }

  /**
   * GET request
   */
  async get<T = any>(endpoint: string, config?: RequestConfig): Promise<T> {
    return this.request<T>(endpoint, { method: "GET", ...config });
  }

  /**
   * POST request
   */
  async post<T = any>(
    endpoint: string,
    data?: any,
    config?: RequestConfig
  ): Promise<T> {
    return this.request<T>(endpoint, {
      method: "POST",
      body: data ? JSON.stringify(data) : undefined,
      ...config,
    });
  }

  /**
   * PUT request
   */
  async put<T = any>(
    endpoint: string,
    data?: any,
    config?: RequestConfig
  ): Promise<T> {
    return this.request<T>(endpoint, {
      method: "PUT",
      body: data ? JSON.stringify(data) : undefined,
      ...config,
    });
  }

  /**
   * PATCH request
   */
  async patch<T = any>(
    endpoint: string,
    data?: any,
    config?: RequestConfig
  ): Promise<T> {
    return this.request<T>(endpoint, {
      method: "PATCH",
      body: data ? JSON.stringify(data) : undefined,
      ...config,
    });
  }

  /**
   * DELETE request
   */
  async delete<T = any>(endpoint: string, config?: RequestConfig): Promise<T> {
    return this.request<T>(endpoint, { method: "DELETE", ...config });
  }

  /**
   * Upload file
   */
  async upload<T = any>(
    endpoint: string,
    file: File,
    config?: RequestConfig
  ): Promise<T> {
    const formData = new FormData();
    formData.append("file", file);

    return this.request<T>(endpoint, {
      method: "POST",
      body: formData,
      ...config,
    });
  }

  /**
   * Upload multiple files
   */
  async uploadMultiple<T = any>(
    endpoint: string,
    files: File[],
    config?: RequestConfig
  ): Promise<T> {
    const formData = new FormData();
    files.forEach((file, index) => {
      formData.append(`files[${index}]`, file);
    });

    return this.request<T>(endpoint, {
      method: "POST",
      body: formData,
      ...config,
    });
  }
}

// Create and export the API client instance
export const apiClient = new ApiClient(API_BASE_URL);

// Export the class for testing or custom instances
export { ApiClient };

// Utility function to handle API errors
export const handleApiError = (error: unknown): AuthError => {
  if (error instanceof ApiError) {
    return {
      code: error.code || "API_ERROR",
      message: error.message,
    };
  }

  if (error instanceof Error) {
    return {
      code: "UNKNOWN_ERROR",
      message: error.message,
    };
  }

  return {
    code: "UNKNOWN_ERROR",
    message: "An unexpected error occurred",
  };
};
