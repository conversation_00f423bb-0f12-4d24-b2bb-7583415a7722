import {
  ActivityFilters,
  ActivityMetrics,
  ActivityTrends,
  AdminHealthCheck,
  AgentAnalyticsFilters,
  AgentCallsFilters,
  AgentCallsResponse,
  AgentEfficiency,
  AgentUsageStats,
  AnalyticsSummary,
  ModelPerformance,
  TokenTrends,
  UserAnalyticsFilters,
  UserAnalyticsResponse,
  UserCostsResponse,
} from "./types";

const ADMIN_API_BASE_URL = process.env.NEXT_PUBLIC_ADMIN_API_URL || "/api";

// Debug log to verify environment variable
console.log("🔧 Admin API Base URL:", ADMIN_API_BASE_URL);
console.log(
  "🔧 Environment variable NEXT_PUBLIC_ADMIN_API_URL:",
  process.env.NEXT_PUBLIC_ADMIN_API_URL
);

class AdminApiClient {
  private token: string | null = null;

  setToken(token: string) {
    this.token = token;
    console.log(
      "🔑 Admin API token set:",
      token ? `${token.substring(0, 20)}...` : "null"
    );
  }

  getToken(): string | null {
    return this.token;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${ADMIN_API_BASE_URL}${endpoint}`;

    const config: RequestInit = {
      headers: {
        "Content-Type": "application/json",
        ...(this.token && { Authorization: `Bearer ${this.token}` }),
        ...options.headers,
      },
      ...options,
    };

    // Log the request
    console.log(`🚀 Admin API Request: ${options.method || "GET"} ${url}`);
    console.log("� Token available:", !!this.token);
    console.log("�📤 Request config:", {
      headers: config.headers,
      method: options.method || "GET",
      body: options.body,
    });

    try {
      const startTime = Date.now();
      const response = await fetch(url, config);
      const duration = Date.now() - startTime;

      // Log response details
      console.log(`📥 Admin API Response: ${endpoint}`);
      console.log(`⏱️  Duration: ${duration}ms`);
      console.log(`📊 Status: ${response.status} ${response.statusText}`);

      if (!response.ok) {
        // Handle specific error cases
        if (response.status === 401) {
          console.error(
            "❌ Unauthorized - Please check your admin credentials"
          );
          throw new Error("Unauthorized - Please check your admin credentials");
        }
        if (response.status === 403) {
          console.error("❌ Forbidden - Admin access required");
          throw new Error("Forbidden - Admin access required");
        }

        const errorData = await response.json().catch(() => ({}));
        console.error("❌ Error response:", errorData);
        throw new Error(
          errorData.message || `HTTP error! status: ${response.status}`
        );
      }

      const data = await response.json();
      console.log("✅ Response data:", data);
      console.log("---");

      return data;
    } catch (error) {
      console.error(`❌ Admin API request failed: ${endpoint}`, error);
      console.log("---");
      throw error;
    }
  }

  // Analytics Summary
  async getAnalyticsSummary(): Promise<AnalyticsSummary> {
    return this.request("/admin/analytics/summary");
  }

  // User Analytics
  async getUserAnalytics(
    filters: UserAnalyticsFilters = {}
  ): Promise<UserAnalyticsResponse> {
    const params = new URLSearchParams();

    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        params.append(key, value.toString());
      }
    });

    const queryString = params.toString();
    const endpoint = `/admin/analytics/users${
      queryString ? `?${queryString}` : ""
    }`;

    return this.request(endpoint);
  }

  // Activity Metrics
  async getActivityMetrics(
    filters: ActivityFilters = {}
  ): Promise<ActivityMetrics> {
    const params = new URLSearchParams();

    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        params.append(key, value.toString());
      }
    });

    const queryString = params.toString();
    const endpoint = `/admin/analytics/activity-metrics${
      queryString ? `?${queryString}` : ""
    }`;

    return this.request(endpoint);
  }

  // Activity Trends
  async getActivityTrends(
    filters: ActivityFilters = {}
  ): Promise<ActivityTrends> {
    const params = new URLSearchParams();

    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        params.append(key, value.toString());
      }
    });

    const queryString = params.toString();
    const endpoint = `/admin/analytics/activity-trends${
      queryString ? `?${queryString}` : ""
    }`;

    return this.request(endpoint);
  }

  // Health Check
  async getHealthCheck(): Promise<AdminHealthCheck> {
    return this.request("/admin/health");
  }

  // Agent Analytics
  async getAgentCalls(
    filters: AgentCallsFilters = {}
  ): Promise<AgentCallsResponse> {
    const params = new URLSearchParams();

    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        params.append(key, value.toString());
      }
    });

    const queryString = params.toString();
    const endpoint = `/admin/agent-analytics/calls${
      queryString ? `?${queryString}` : ""
    }`;

    return this.request(endpoint);
  }

  async getAgentUsageStats(
    filters: AgentAnalyticsFilters = {}
  ): Promise<AgentUsageStats> {
    const params = new URLSearchParams();

    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        params.append(key, value.toString());
      }
    });

    const queryString = params.toString();
    const endpoint = `/admin/agent-analytics/usage-stats${
      queryString ? `?${queryString}` : ""
    }`;

    return this.request(endpoint);
  }

  async getTokenTrends(
    filters: AgentAnalyticsFilters = {}
  ): Promise<TokenTrends> {
    const params = new URLSearchParams();

    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        params.append(key, value.toString());
      }
    });

    const queryString = params.toString();
    const endpoint = `/admin/agent-analytics/token-trends${
      queryString ? `?${queryString}` : ""
    }`;

    return this.request(endpoint);
  }

  async getUserCosts(
    filters: AgentAnalyticsFilters = {}
  ): Promise<UserCostsResponse> {
    const params = new URLSearchParams();

    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        params.append(key, value.toString());
      }
    });

    const queryString = params.toString();
    const endpoint = `/admin/agent-analytics/user-costs${
      queryString ? `?${queryString}` : ""
    }`;

    return this.request(endpoint);
  }

  async getModelPerformance(
    filters: AgentAnalyticsFilters = {}
  ): Promise<ModelPerformance[]> {
    const params = new URLSearchParams();

    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        params.append(key, value.toString());
      }
    });

    const queryString = params.toString();
    const endpoint = `/admin/agent-analytics/model-performance${
      queryString ? `?${queryString}` : ""
    }`;

    return this.request(endpoint);
  }

  async getAgentEfficiency(
    filters: AgentAnalyticsFilters = {}
  ): Promise<AgentEfficiency[]> {
    const params = new URLSearchParams();

    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        params.append(key, value.toString());
      }
    });

    const queryString = params.toString();
    const endpoint = `/admin/agent-analytics/agent-efficiency${
      queryString ? `?${queryString}` : ""
    }`;

    return this.request(endpoint);
  }

  // System Management
  async runMigrations(): Promise<any> {
    return this.request("/admin/migrations/run", {
      method: "POST",
    });
  }

  async cleanupTenantSchemas(): Promise<any> {
    return this.request("/admin/cleanup/schemas", {
      method: "POST",
    });
  }

  // Feedback
  async getFeedback(filters?: {
    page?: number;
    limit?: number;
    search?: string;
    isHighPriority?: boolean;
    minSubmissionCount?: number;
    startDate?: string;
    endDate?: string;
    sortBy?: "createdAt" | "userEmail" | "submissionCount" | "isHighPriority";
    sortOrder?: "ASC" | "DESC";
  }): Promise<any> {
    const params = new URLSearchParams();

    if (filters?.page) params.append("page", filters.page.toString());
    if (filters?.limit) params.append("limit", filters.limit.toString());
    if (filters?.search) params.append("search", filters.search);
    if (filters?.isHighPriority !== undefined)
      params.append("isHighPriority", filters.isHighPriority.toString());
    if (filters?.minSubmissionCount)
      params.append(
        "minSubmissionCount",
        filters.minSubmissionCount.toString()
      );
    if (filters?.startDate) params.append("startDate", filters.startDate);
    if (filters?.endDate) params.append("endDate", filters.endDate);
    if (filters?.sortBy) params.append("sortBy", filters.sortBy);
    if (filters?.sortOrder) params.append("sortOrder", filters.sortOrder);

    const queryString = params.toString();
    const url = queryString
      ? `/admin/feedback?${queryString}`
      : "/admin/feedback";

    return this.request<any>(url);
  }
}

// Create a singleton instance
export const adminApi = new AdminApiClient();

// Helper function to initialize admin API with token
export const initializeAdminApi = (token: string) => {
  adminApi.setToken(token);
};
