import { io, Socket } from 'socket.io-client';
import { ProjectCreationProgress } from '@/stores/projectCreationStore';

export interface ProjectCreationEvent {
  type: 'progress' | 'complete' | 'error';
  data: ProjectCreationProgress | { projectId: string; name: string; description: string } | { error: string };
}

export class RealEventStream {
  private socket: Socket | null = null;
  private eventTarget: EventTarget;
  private isConnected = false;

  constructor() {
    this.eventTarget = new EventTarget();
  }

  // Connect to the real WebSocket server
  connect(serverUrl: string = 'ws://localhost:3002'): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.socket = io(serverUrl, {
          transports: ['websocket'],
          timeout: 5000,
        });

        this.socket.on('connect', () => {
          console.log('Connected to project creation server');
          this.isConnected = true;
          resolve();
        });

        this.socket.on('disconnect', () => {
          console.log('Disconnected from project creation server');
          this.isConnected = false;
        });

        this.socket.on('connect_error', (error) => {
          console.error('Connection error:', error);
          this.isConnected = false;
          reject(error);
        });

        // Listen for project creation events
        this.socket.on('project_progress', (data: ProjectCreationProgress) => {
          this.dispatchEvent('progress', data);
        });

        this.socket.on('project_complete', (data: { projectId: string; name: string; description: string }) => {
          this.dispatchEvent('complete', data);
        });

        this.socket.on('project_error', (data: { error: string }) => {
          this.dispatchEvent('error', data);
        });

      } catch (error) {
        reject(error);
      }
    });
  }

  // Start project creation with real API
  startProjectCreation(ideaText: string): void {
    if (!this.socket || !this.isConnected) {
      console.error('Socket not connected. Cannot start project creation.');
      return;
    }

    // Send the idea to the server
    this.socket.emit('create_project', {
      idea: ideaText,
      timestamp: Date.now(),
    });
  }

  // Add event listener
  addEventListener(type: string, listener: (event: CustomEvent<ProjectCreationEvent>) => void): void {
    this.eventTarget.addEventListener(type, listener as EventListener);
  }

  // Remove event listener
  removeEventListener(type: string, listener: (event: CustomEvent<ProjectCreationEvent>) => void): void {
    this.eventTarget.removeEventListener(type, listener as EventListener);
  }

  // Dispatch custom event
  private dispatchEvent(type: ProjectCreationEvent['type'], data: ProjectCreationEvent['data']): void {
    const event = new CustomEvent('message', {
      detail: { type, data }
    });
    this.eventTarget.dispatchEvent(event);
  }

  // Disconnect from server
  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
      this.isConnected = false;
    }
  }

  // Check connection status
  isSocketConnected(): boolean {
    return this.isConnected && this.socket?.connected === true;
  }
}

// Singleton instance for global use
export const realEventStream = new RealEventStream();

// Hook for easier integration with React components
export const useRealProjectCreationStream = () => {
  const connect = async (serverUrl?: string) => {
    try {
      await realEventStream.connect(serverUrl);
      return true;
    } catch (error) {
      console.error('Failed to connect to project creation server:', error);
      return false;
    }
  };

  const startCreation = (ideaText: string) => {
    realEventStream.startProjectCreation(ideaText);
  };

  const disconnect = () => {
    realEventStream.disconnect();
  };

  const addEventListener = (listener: (event: CustomEvent<ProjectCreationEvent>) => void) => {
    realEventStream.addEventListener('message', listener);
  };

  const removeEventListener = (listener: (event: CustomEvent<ProjectCreationEvent>) => void) => {
    realEventStream.removeEventListener('message', listener);
  };

  const isConnected = () => {
    return realEventStream.isSocketConnected();
  };

  return {
    connect,
    startCreation,
    disconnect,
    addEventListener,
    removeEventListener,
    isConnected,
  };
};

// Alternative EventSource implementation for Server-Sent Events
export class RealEventSourceStream {
  private eventSource: EventSource | null = null;
  private eventTarget: EventTarget;

  constructor() {
    this.eventTarget = new EventTarget();
  }

  // Connect to SSE endpoint
  connect(endpoint: string = '/api/project-creation/stream'): void {
    if (this.eventSource) {
      this.eventSource.close();
    }

    this.eventSource = new EventSource(endpoint);

    this.eventSource.onopen = () => {
      console.log('Connected to project creation stream');
    };

    this.eventSource.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        this.dispatchEvent(data.type, data.data);
      } catch (error) {
        console.error('Failed to parse SSE message:', error);
      }
    };

    this.eventSource.onerror = (error) => {
      console.error('SSE connection error:', error);
      this.dispatchEvent('error', { error: 'Connection lost' });
    };
  }

  // Start project creation via POST request
  async startProjectCreation(ideaText: string): Promise<void> {
    try {
      const response = await fetch('/api/project-creation/start', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          idea: ideaText,
          timestamp: Date.now(),
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      console.log('Project creation started:', result);
    } catch (error) {
      console.error('Failed to start project creation:', error);
      this.dispatchEvent('error', { error: 'Failed to start project creation' });
    }
  }

  // Add event listener
  addEventListener(type: string, listener: (event: CustomEvent<ProjectCreationEvent>) => void): void {
    this.eventTarget.addEventListener(type, listener as EventListener);
  }

  // Remove event listener
  removeEventListener(type: string, listener: (event: CustomEvent<ProjectCreationEvent>) => void): void {
    this.eventTarget.removeEventListener(type, listener as EventListener);
  }

  // Dispatch custom event
  private dispatchEvent(type: ProjectCreationEvent['type'], data: ProjectCreationEvent['data']): void {
    const event = new CustomEvent('message', {
      detail: { type, data }
    });
    this.eventTarget.dispatchEvent(event);
  }

  // Close connection
  disconnect(): void {
    if (this.eventSource) {
      this.eventSource.close();
      this.eventSource = null;
    }
  }
}

// Usage example:
/*
// To switch to real Socket.IO:
import { useRealProjectCreationStream } from '@/lib/realEventStream';

// In your component:
const { connect, startCreation, addEventListener, removeEventListener } = useRealProjectCreationStream();

// Connect to server
await connect('ws://localhost:3002');

// Start creation
startCreation(ideaText);

// To switch to Server-Sent Events:
import { RealEventSourceStream } from '@/lib/realEventStream';

const stream = new RealEventSourceStream();
stream.connect('/api/project-creation/stream');
stream.startProjectCreation(ideaText);
*/
