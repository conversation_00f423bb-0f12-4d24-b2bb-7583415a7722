import {
  LoginCredentials,
  RegisterCredentials,
  User,
  AuthTokens,
  Project,
  CreateProjectData,
  UpdateProjectData,
  ApiResponse,
} from "./types";
import { SessionManager } from "./session";

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || "/api";

class ApiClient {
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`;

    const config: RequestInit = {
      headers: {
        "Content-Type": "application/json",
        ...SessionManager.getAuthHeaders(),
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.message || `HTTP error! status: ${response.status}`
        );
      }

      const result = await response.json();

      // Handle API response format { success: true, data: ... }
      if (result.success && result.data !== undefined) {
        return result.data;
      }

      // Handle direct data response
      return result;
    } catch (error) {
      console.error(`API request failed: ${endpoint}`, error);
      throw error;
    }
  }

  // Auth endpoints
  async login(
    credentials: LoginCredentials
  ): Promise<{ user: User; accessToken: string; refreshToken: string }> {
    return this.request("/auth/signin", {
      method: "POST",
      body: JSON.stringify(credentials),
    });
  }

  async register(
    credentials: RegisterCredentials
  ): Promise<{ user: User; accessToken: string; refreshToken: string }> {
    return this.request("/auth/signup", {
      method: "POST",
      body: JSON.stringify(credentials),
    });
  }

  async refreshToken(
    refreshToken: string
  ): Promise<{ accessToken: string; refreshToken: string }> {
    return this.request("/auth/refresh", {
      method: "POST",
      body: JSON.stringify({ refreshToken }),
    });
  }

  async verifyToken(token: string): Promise<boolean> {
    try {
      await this.request("/auth/verify", {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      return true;
    } catch {
      return false;
    }
  }

  async logout(): Promise<void> {
    return this.request("/auth/logout", {
      method: "POST",
    });
  }

  // User endpoints
  async getCurrentUser(): Promise<User> {
    return this.request("/auth/me");
  }

  async updateProfile(data: Partial<User>): Promise<User> {
    return this.request("/auth/profile", {
      method: "PUT",
      body: JSON.stringify(data),
    });
  }

  // Project endpoints
  async getProjects(): Promise<Project[]> {
    return this.request("/projects");
  }

  async getProject(id: string): Promise<Project> {
    return this.request(`/projects/${id}`);
  }

  async createProject(data: CreateProjectData): Promise<Project> {
    return this.request("/projects", {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  async updateProject(id: string, data: UpdateProjectData): Promise<Project> {
    return this.request(`/projects/${id}`, {
      method: "PUT",
      body: JSON.stringify(data),
    });
  }

  async deleteProject(id: string): Promise<void> {
    console.log("🔗 API Client: Deleting project", {
      id,
      url: `/projects/${id}`,
    });
    return this.request(`/projects/${id}`, {
      method: "DELETE",
    });
  }
}

export const authApi = new ApiClient();
