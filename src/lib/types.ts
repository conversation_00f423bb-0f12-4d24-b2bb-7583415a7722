// Authentication types
export interface User {
  id: string;
  email: string;
  name: string;
  role?: "admin" | "user"; // Optional since we're using email domain for admin check
  createdAt: Date | string;
  updatedAt: Date | string;
}

export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface RegisterCredentials {
  name: string;
  email: string;
  password: string;
  confirmPassword: string;
}

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

export interface SessionPayload {
  userId: string;
  email: string;
  role: string;
  expiresAt: number; // Unix timestamp
  [key: string]: any; // Index signature for JWT compatibility
}

// Project types
export interface Project {
  id: string;
  name: string;
  description: string;
  status: "active" | "completed" | "archived";
  createdAt: Date;
  updatedAt: Date;
  userId: string;
}

export interface CreateProjectData {
  name: string;
  description: string;
}

export interface UpdateProjectData {
  name?: string;
  description?: string;
  status?: "active" | "completed" | "archived";
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Admin Analytics types
export interface AnalyticsSummary {
  totalUsers: number;
  activeToday: number;
  newUsersThisWeek: number;
  usersWithFeedback: number;
  feedbackRate: number;
  averageSessionTime: number;
  totalRequestsToday: number;
  averageResponseTime: number;
  systemUptime: number;
}

export interface UserAnalytics {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: string;
  signupDate: string;
  referralCode: string;
  lastSignIn: string;
  hasSubmittedFeedback: boolean;
  actionCount: number;
  averageSessionTime: number;
  totalSessions: number;
  feedbackSummary: string;
}

export interface UserAnalyticsResponse {
  data: UserAnalytics[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface ActivityMetrics {
  dau: number;
  wau: number;
  mau: number;
  yau: number;
  dauChange: number;
  wauChange: number;
  mauChange: number;
  yauChange: number;
  date: string;
}

export interface ActivityTrendData {
  date: string;
  activeUsers: number;
  newUsers: number;
  totalRequests: number;
  averageSessionTime: number;
}

export interface ActivityTrends {
  data: ActivityTrendData[];
  summary: {
    totalDays: number;
    averageActiveUsers: number;
    totalNewUsers: number;
    growthRate: number;
  };
}

export interface AdminHealthCheck {
  status: string;
  timestamp: string;
  services: {
    database: string;
    analytics: string;
  };
}

export interface UserAnalyticsFilters {
  page?: number;
  limit?: number;
  role?: string;
  hasSubmittedFeedback?: boolean;
  startDate?: string;
  endDate?: string;
  search?: string;
}

export interface ActivityFilters {
  startDate?: string;
  endDate?: string;
  granularity?: "day" | "week" | "month" | "year";
}

// Agent Analytics types
export interface AgentCall {
  id: string;
  callType: "agent_execution" | "llm_call" | "tool_call";
  agentType: "intake" | "validate" | "build" | "grow" | "coordinator" | "topic";
  userId: string;
  projectId: string;
  workflowType: string;
  modelProvider: "openai" | "deepseek" | "anthropic";
  modelName: string;
  inputTokens: number;
  outputTokens: number;
  totalTokens: number;
  cost: number;
  duration: number;
  status: "success" | "error" | "timeout" | "cancelled";
  prompt: string;
  response: string;
  toolsCalled: string[];
  errorMessage: string | null;
  createdAt: string;
}

export interface AgentCallsResponse {
  data: AgentCall[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface AgentCallsFilters {
  page?: number;
  limit?: number;
  agentType?: string;
  callType?: string;
  modelProvider?: string;
  status?: string;
  userId?: string;
  projectId?: string;
  workflowType?: string;
  modelName?: string;
  startDate?: string;
  endDate?: string;
  minCost?: number;
  maxCost?: number;
  minTokens?: number;
  maxTokens?: number;
}

export interface AgentUsageStats {
  totalCalls: number;
  successfulCalls: number;
  failedCalls: number;
  successRate: number;
  totalTokens: number;
  totalCost: number;
  averageTokensPerCall: number;
  averageCostPerCall: number;
  averageDuration: number;
  mostUsedAgentType: string;
  mostUsedModel: string;
  callsByAgentType: Record<string, number>;
  callsByProvider: Record<string, number>;
  costByModel: Record<string, number>;
}

export interface TokenTrendData {
  date: string;
  totalTokens: number;
  inputTokens: number;
  outputTokens: number;
  totalCost: number;
  callCount: number;
  averageTokensPerCall: number;
}

export interface TokenTrends {
  data: TokenTrendData[];
  summary: {
    totalDays: number;
    totalTokens: number;
    totalCost: number;
    averageTokensPerDay: number;
    averageCostPerDay: number;
    growthRate: number;
  };
}

export interface UserCostData {
  userId: string;
  userEmail: string;
  totalCalls: number;
  totalTokens: number;
  totalCost: number;
  averageCostPerCall: number;
  mostUsedAgentType: string;
  dateRangeStart: string;
  dateRangeEnd: string;
}

export interface UserCostsResponse {
  data: UserCostData[];
  summary: {
    totalUsers: number;
    totalCost: number;
    averageCostPerUser: number;
    highestSpendingUser: UserCostData;
    mostActiveUser: UserCostData;
  };
}

export interface ModelPerformance {
  modelName: string;
  provider: string;
  totalCalls: number;
  successRate: number;
  averageDuration: number;
  averageTokensPerCall: number;
  averageCostPerCall: number;
  totalCost: number;
  costPer1kTokens: number;
}

export interface AgentEfficiency {
  agentType: string;
  averageCompletionTime: number;
  successRate: number;
  averageTokensPerExecution: number;
  averageCostPerExecution: number;
  averageToolsPerExecution: number;
  commonTools: string[];
  totalExecutions: number;
}

export interface AgentAnalyticsFilters {
  startDate?: string;
  endDate?: string;
  userId?: string;
  granularity?: "day" | "week" | "month";
  limit?: number;
}
