import { SignJWT, jwtVerify } from "jose";
import { SessionPayload } from "./types";

const secretKey =
  process.env.JWT_SECRET || "your-secret-key-change-in-production";
const encodedKey = new TextEncoder().encode(secretKey);

export async function encrypt(payload: SessionPayload): Promise<string> {
  return new SignJWT(payload)
    .setProtectedHeader({ alg: "HS256" })
    .setIssuedAt()
    .setExpirationTime("7d")
    .sign(encodedKey);
}

export async function decrypt(
  session: string | undefined = ""
): Promise<SessionPayload | null> {
  try {
    if (!session || session.trim() === "") {
      return null;
    }

    const { payload } = await jwtVerify(session, encodedKey, {
      algorithms: ["HS256"],
    });
    return payload as SessionPayload;
  } catch (error) {
    console.log("Failed to verify session:", error);
    console.log("Token was:", session?.substring(0, 50) + "...");
    return null;
  }
}

export async function createAccessToken(user: {
  id: string;
  email: string;
  role: string;
}): Promise<string> {
  const payload: SessionPayload = {
    userId: user.id,
    email: user.email,
    role: user.role,
    expiresAt: Date.now() + 7 * 24 * 60 * 60 * 1000, // 7 days from now (timestamp)
  };

  return encrypt(payload);
}

export async function verifyAccessToken(
  token: string
): Promise<SessionPayload | null> {
  return decrypt(token);
}
