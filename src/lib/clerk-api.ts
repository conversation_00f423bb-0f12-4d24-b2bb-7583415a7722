import { auth } from "@clerk/nextjs/server";

/**
 * Server-side API utility for making authenticated requests to your backend
 */
export async function makeAuthenticatedRequest(
  endpoint: string,
  options: RequestInit = {}
) {
  const { getToken } = await auth();
  const token = await getToken();

  const baseUrl = process.env.NEXT_PUBLIC_ADMIN_API_URL || "http://localhost:3002";
  
  const defaultHeaders = {
    "Content-Type": "application/json",
    ...(token && { Authorization: `Bearer ${token}` }),
  };

  const response = await fetch(`${baseUrl}${endpoint}`, {
    ...options,
    headers: {
      ...defaultHeaders,
      ...options.headers,
    },
  });

  if (!response.ok) {
    throw new Error(`API request failed: ${response.status} ${response.statusText}`);
  }

  return response;
}

/**
 * Client-side API utility for making authenticated requests to your backend
 * This function should be used from React components with useAuth hook
 */
export async function makeClientAuthenticatedRequest(
  endpoint: string,
  token: string | null,
  options: RequestInit = {}
) {
  const baseUrl = process.env.NEXT_PUBLIC_ADMIN_API_URL || "http://localhost:3002";

  const defaultHeaders = {
    "Content-Type": "application/json",
    ...(token && { Authorization: `Bearer ${token}` }),
  };

  const response = await fetch(`${baseUrl}${endpoint}`, {
    ...options,
    headers: {
      ...defaultHeaders,
      ...options.headers,
    },
  });

  if (!response.ok) {
    throw new Error(`API request failed: ${response.status} ${response.statusText}`);
  }

  return response;
}

/**
 * Create a user in your backend with Clerk data
 */
export async function createUserInBackend(userData: {
  email: string;
  firstName: string;
  lastName: string;
  clerkId: string;
  role?: string;
  status?: string;
  avatarUrl?: string;
  bio?: string;
  timezone?: string;
  preferences?: any;
}) {
  try {
    const response = await makeAuthenticatedRequest("/api/users", {
      method: "POST",
      body: JSON.stringify({
        ...userData,
        role: userData.role || "user",
        status: userData.status || "active",
        bio: userData.bio || "",
        timezone: userData.timezone || "UTC",
        preferences: userData.preferences || {
          notifications: true,
          theme: "system",
          language: "en"
        }
      }),
    });

    return await response.json();
  } catch (error) {
    console.error("Error creating user in backend:", error);
    throw error;
  }
}

/**
 * Get user from backend by Clerk ID
 */
export async function getUserFromBackend(clerkId: string) {
  try {
    const response = await makeAuthenticatedRequest(`/api/users/clerk/${clerkId}`);
    return await response.json();
  } catch (error) {
    console.error("Error getting user from backend:", error);
    throw error;
  }
}

/**
 * Update user in backend by internal user ID
 */
export async function updateUserInBackend(userId: string, userData: {
  email?: string;
  firstName?: string;
  lastName?: string;
  clerkId?: string;
  role?: string;
  status?: string;
  avatarUrl?: string;
  bio?: string;
  timezone?: string;
  preferences?: any;
}) {
  try {
    const response = await makeAuthenticatedRequest(`/api/users/${userId}`, {
      method: "PATCH",
      body: JSON.stringify(userData),
    });

    return await response.json();
  } catch (error) {
    console.error("Error updating user in backend:", error);
    throw error;
  }
}

/**
 * Update user in backend by Clerk ID (finds user first, then updates)
 */
export async function updateUserByClerkId(clerkId: string, userData: {
  email?: string;
  firstName?: string;
  lastName?: string;
  role?: string;
  status?: string;
  avatarUrl?: string;
  bio?: string;
  timezone?: string;
  preferences?: any;
}) {
  try {
    // First, get the user by Clerk ID to find the internal user ID
    const user = await getUserFromBackend(clerkId);

    // Then update using the internal user ID
    const fullUserData = {
      ...userData,
      clerkId, // Ensure clerkId is included
    };

    return await updateUserInBackend(user.id, fullUserData);
  } catch (error) {
    console.error("Error updating user by Clerk ID:", error);
    throw error;
  }
}
