"use client";

import { AuthTokens, User } from "./types";

const ACCESS_TOKEN_KEY = "siift_access_token";
const REFRESH_TOKEN_KEY = "siift_refresh_token";
const USER_KEY = "siift_user";

export class SessionManager {
  static setTokens(tokens: AuthTokens, rememberMe: boolean = false): void {
    const storage = rememberMe ? localStorage : sessionStorage;

    storage.setItem(ACCESS_TOKEN_KEY, tokens.accessToken);
    if (tokens.refreshToken) {
      storage.setItem(REFRESH_TOKEN_KEY, tokens.refreshToken);
    }
  }

  static getAccessToken(): string | null {
    // Check localStorage first, then sessionStorage
    return (
      localStorage.getItem(ACCESS_TOKEN_KEY) ||
      sessionStorage.getItem(ACCESS_TOKEN_KEY)
    );
  }

  static getRefreshToken(): string | null {
    // Check localStorage first, then sessionStorage
    return (
      localStorage.getItem(REFRESH_TOKEN_KEY) ||
      sessionStorage.getItem(REFRESH_TOKEN_KEY)
    );
  }

  static setUser(user: User, rememberMe: boolean = false): void {
    const storage = rememberMe ? localStorage : sessionStorage;
    storage.setItem(USER_KEY, JSON.stringify(user));
  }

  static getUser(): User | null {
    try {
      // Check localStorage first, then sessionStorage
      const userStr =
        localStorage.getItem(USER_KEY) || sessionStorage.getItem(USER_KEY);

      if (!userStr) return null;

      const userData = JSON.parse(userStr);

      // Convert date strings back to Date objects
      if (userData.createdAt && typeof userData.createdAt === "string") {
        userData.createdAt = new Date(userData.createdAt);
      }
      if (userData.updatedAt && typeof userData.updatedAt === "string") {
        userData.updatedAt = new Date(userData.updatedAt);
      }

      return userData;
    } catch (error) {
      console.error("Error parsing user data:", error);
      return null;
    }
  }

  static clearSession(): void {
    // Clear from both storages
    [localStorage, sessionStorage].forEach((storage) => {
      storage.removeItem(ACCESS_TOKEN_KEY);
      storage.removeItem(REFRESH_TOKEN_KEY);
      storage.removeItem(USER_KEY);
    });
  }

  static clearInvalidSession(): void {
    // Clear session if tokens appear to be invalid format
    const token = this.getAccessToken();
    if (token && (!token.includes(".") || token.split(".").length !== 3)) {
      console.log("Clearing invalid token format");
      this.clearSession();
    }
  }

  static isAuthenticated(): boolean {
    return !!this.getAccessToken();
  }

  static getAuthHeaders(): Record<string, string> {
    const token = this.getAccessToken();
    return token ? { Authorization: `Bearer ${token}` } : {};
  }
}

// Server-side session utilities
export function getTokenFromCookies(
  cookieHeader: string | null
): string | null {
  if (!cookieHeader) return null;

  const cookies = cookieHeader.split(";").reduce((acc, cookie) => {
    const [key, value] = cookie.trim().split("=");
    acc[key] = value;
    return acc;
  }, {} as Record<string, string>);

  return cookies[ACCESS_TOKEN_KEY] || null;
}
