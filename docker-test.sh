#!/bin/bash

echo "🐳 Testing Docker Setup for Siift Next.js App"
echo "=============================================="

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

echo "✅ Docker is running"

# Check if docker-compose is available
if ! command -v docker-compose &> /dev/null; then
    echo "❌ docker-compose is not installed. Please install docker-compose."
    exit 1
fi

echo "✅ docker-compose is available"

# Build the image
echo "🔨 Building Docker image..."
docker-compose build

if [ $? -eq 0 ]; then
    echo "✅ Docker image built successfully"
else
    echo "❌ Docker build failed"
    exit 1
fi

# Start the container
echo "🚀 Starting container..."
docker-compose up -d

if [ $? -eq 0 ]; then
    echo "✅ Container started successfully"
else
    echo "❌ Failed to start container"
    exit 1
fi

# Wait for container to be ready
echo "⏳ Waiting for container to be ready..."
sleep 10

# Check if the app is responding
echo "🔍 Checking if app is responding..."
if curl -f http://localhost:3000/api/health > /dev/null 2>&1; then
    echo "✅ App is responding at http://localhost:3000"
else
    echo "⚠️  App might not be ready yet. Check logs with: docker-compose logs"
fi

echo ""
echo "🎉 Docker setup test completed!"
echo "📝 Useful commands:"
echo "   - View logs: docker-compose logs -f"
echo "   - Stop app: docker-compose down"
echo "   - Restart app: docker-compose restart"
echo "   - Access app: http://localhost:3000" 