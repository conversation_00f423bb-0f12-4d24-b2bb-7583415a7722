{"name": "siift-next", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3001", "build": "next build", "build:no-lint": "ESLINT_NO_DEV_ERRORS=true next build", "build:force": "next build --no-lint", "build:skip-all": "ESLINT_NO_DEV_ERRORS=true TYPESCRIPT_NO_BUILD_ERRORS=true next build", "start": "next start -p 3001", "start:prod": "NODE_ENV=production next start -p 3001", "lint": "next lint", "lint:fix": "next lint --fix", "docker:build": "docker build -t siift-next .", "docker:run": "docker run -p 3001:3001 siift-next"}, "dependencies": {"@clerk/nextjs": "^6.27.1", "@hello-pangea/dnd": "^18.0.1", "@hookform/resolvers": "^5.1.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-query": "^5.83.0", "@tanstack/react-query-devtools": "^5.83.0", "@types/recharts": "^1.8.29", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.23.6", "jose": "^6.0.11", "lucide-react": "^0.525.0", "motion": "^12.23.6", "next": "15.3.5", "next-themes": "^0.4.6", "posthog-js": "^1.258.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.60.0", "react-markdown": "^10.1.0", "recharts": "^3.1.0", "remark-gfm": "^4.0.1", "socket.io-client": "^4.8.1", "sonner": "^2.0.6", "svix": "^1.69.0", "tailwind-merge": "^3.3.1", "zod": "^3.25.76", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "depcheck": "^1.4.7", "eslint": "^9", "eslint-config-next": "15.3.5", "eslint-plugin-unused-imports": "^4.1.4", "knip": "^5.62.0", "tailwindcss": "^4", "ts-prune": "^0.10.3", "tw-animate-css": "^1.3.5", "typescript": "^5"}}